import {defineConfig} from "umi";

export default defineConfig({
    esbuildMinifyIIFE: true,
    plugins: [
        "@umijs/plugins/dist/tailwindcss",
        "@umijs/plugins/dist/request",
        '@umijs/plugins/dist/initial-state',
        "@umijs/plugins/dist/model",
        '@umijs/plugins/dist/access',
    ],
    access: {},
    model: {},
    initialState: {
    },
    extraPostCSSPlugins: [
        require("@tailwindcss/postcss")
    ],
    request: {
        dataField: "data",
    },
    routes: [
        {path: "/login", component: "login", layout: false},
        {
            path: "/setting",
            wrappers:['@/wrappers/auth'],
            code:'SETTINGS',
            routes: [
                {
                    path: "/setting/menu",
                    component: "@/pages/settings/menu/index",
                    code:'SETTING:MENU'
                },
                {
                    path: "/setting/account",
                    component: "@/pages/settings/account/index",
                    code:'SETTING:ACCOUNT'
                },
                {
                    path: "/setting/role",
                    component: "@/pages/settings/role/index",
                    code:'SETTING:ROLE'
                },
                {
                    path: "/setting/group",
                    component: "@/pages/settings/group/index",
                    code:'SETTING:GROUP'
                },
            ]

        },
        {
            path: "/experience",
            routes: [
                {
                    path: "/experience/classify",
                    component: "@/pages/cases/experience/classify/index"
                },
                {
                    path: "/experience/cases",
                    component: "@/pages/cases/experience/cases/index",
                },
                {
                    path: "/experience/cases/new",
                    component: "@/pages/cases/experience/cases/edit",
                },
                {
                    path: "/experience/cases/edit/:id",
                    component: "@/pages/cases/experience/cases/edit",
                },
                {
                    path: "/experience/cases/:id",
                    component: "@/pages/cases/experience/cases/detail",
                }
            ]

        },
        {
            path: "/product",
            routes: [
                {
                    path: "/product/classify",
                    component: "@/pages/cases/product/classify/index"
                },
                {
                    path: "/product/cases",
                    component: "@/pages/cases/product/cases/index",
                },
                {
                    path: "/product/cases/new",
                    component: "@/pages/cases/product/cases/edit",
                },
                {
                    path: "/product/cases/edit/:id",
                    component: "@/pages/cases/product/cases/edit",
                },
                {
                    path: "/product/cases/:id",
                    component: "@/pages/cases/product/cases/detail",
                },
                {
                    path: "/product/download-limit",
                    component: "@/pages/cases/product/download/index",
                },
                {
                    path: "/product/authorization-rule",
                    component: "@/pages/cases/product/authorization-rule/index",
                },
                {
                    path: "/product/download-record",
                    component: "@/pages/cases/product/download-record/index",
                }
            ]

        },
        {
            path: "/share",
            routes: [
                {
                    path: "/share/cases",
                    component: "@/pages/cases/share/index",
                },
            ]

        },
        {
            path: "/news",
            routes: [
                {
                    path: "/news/navigation",
                    component: "@/pages/news/navigation/index",
                },
                {
                    path: "/news/block",
                    component: "@/pages/news/block/index",
                },
                {
                    path: "/news/resource-manager",
                    component: "@/pages/news/resource-manager/index",
                },
                {
                    path: "/news/resource-manager/new",
                    component: "@/pages/news/resource-manager/edit",
                },
                {
                    path: "/news/resource-manager/edit/:id",
                    component: "@/pages/news/resource-manager/edit",
                },
                {
                    path: "/news/newspaper",
                    component: "@/pages/news/newspaper/index",
                },
                {
                    path: "/news/newspaper-board",
                    component: "@/pages/news/newspaper-board/index",
                },
                {
                    path: "/news/banner",
                    component: "@/pages/news/banner/index",
                },
            ]

        },
        {
            path: "/interior",
            routes: [
                // 项目管理
                {
                    path: "/interior/project",
                    component: "@/pages/interior/project/index",
                },
                {
                    path: "/interior/project-datum",
                    component: "@/pages/interior/project-datum/index",
                },
                {
                    path: "/interior/project-datum/new",
                    component: "@/pages/interior/project-datum/edit",
                },
                {
                    path: "/interior/project-datum/edit/:id",
                    component: "@/pages/interior/project-datum/edit",
                },
                {
                    path: "/interior/project-datum/:id",
                    component: "@/pages/interior/project-datum/detail",
                },
                // 制度管理
                {
                    path: "/interior/regime",
                    component: "@/pages/interior/regime/index",
                },
                {
                    path: "/interior/regime-datum",
                    component: "@/pages/interior/regime-datum/index",
                },
                // 岗位标准SOP

            ]

        },

    ],
    npmClient: "pnpm",
    proxy: {
        '/api': {
            target: 'http://localhost:9099',
            changeOrigin: true,
            pathRewrite: {'^/api': ''},
        },
    },
});
