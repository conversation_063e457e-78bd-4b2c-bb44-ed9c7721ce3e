{"private": true, "author": "Loong <<EMAIL>>", "scripts": {"dev": "umi dev", "build": "umi build", "postinstall": "umi setup", "setup": "umi setup", "start": "npm run dev"}, "dependencies": {"@hookform/resolvers": "^4.1.3", "@radix-ui/react-accordion": "^1.2.4", "@radix-ui/react-alert-dialog": "^1.1.7", "@radix-ui/react-aspect-ratio": "^1.1.3", "@radix-ui/react-avatar": "^1.1.4", "@radix-ui/react-checkbox": "^1.1.5", "@radix-ui/react-collapsible": "^1.1.4", "@radix-ui/react-context-menu": "^2.2.7", "@radix-ui/react-dialog": "^1.1.7", "@radix-ui/react-dropdown-menu": "^2.1.7", "@radix-ui/react-hover-card": "^1.1.7", "@radix-ui/react-label": "^2.1.3", "@radix-ui/react-menubar": "^1.1.7", "@radix-ui/react-navigation-menu": "^1.2.6", "@radix-ui/react-popover": "^1.1.7", "@radix-ui/react-progress": "^1.1.3", "@radix-ui/react-radio-group": "^1.2.4", "@radix-ui/react-scroll-area": "^1.2.4", "@radix-ui/react-select": "^2.1.7", "@radix-ui/react-separator": "^1.1.3", "@radix-ui/react-slider": "^1.2.4", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-switch": "^1.1.4", "@radix-ui/react-tabs": "^1.1.4", "@radix-ui/react-toast": "^1.2.7", "@radix-ui/react-toggle": "^1.1.3", "@radix-ui/react-toggle-group": "^1.1.3", "@radix-ui/react-tooltip": "^1.2.0", "@tailwindcss/postcss": "^4.0.14", "@tanstack/react-table": "^8.21.2", "@tanstack/react-virtual": "^3.13.6", "@tiptap/extension-color": "^3.0.7", "@tiptap/extension-highlight": "^3.0.7", "@tiptap/extension-image": "^3.0.7", "@tiptap/extension-link": "^3.0.7", "@tiptap/extension-placeholder": "^3.0.7", "@tiptap/extension-task-item": "^3.0.7", "@tiptap/extension-task-list": "^3.0.7", "@tiptap/extension-text-align": "^3.0.7", "@tiptap/extension-underline": "^3.0.7", "@tiptap/pm": "^3.0.7", "@tiptap/react": "^3.0.7", "@tiptap/starter-kit": "^3.0.7", "axios": "^1.8.4", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.0.0", "date-fns": "^4.1.0", "embla-carousel-react": "^8.6.0", "framer-motion": "^12.5.0", "input-otp": "^1.4.2", "lodash": "^4.17.21", "lucide-react": "^0.483.0", "next-themes": "^0.4.6", "nuqs": "^2.4.1", "react": "^19.0.0", "react-day-picker": "9.7.0", "react-dom": "^19.0.0", "react-dropzone": "^14.3.8", "react-hook-form": "^7.55.0", "react-infinite-scroll-component": "^6.1.0", "react-intersection-observer": "^9.16.0", "react-resizable-panels": "^2.1.7", "recharts": "^2.15.2", "sonner": "^2.0.3", "tailwind-merge": "^3.0.2", "tw-animate-css": "^1.2.4", "umi": "^4.4.6", "uuid": "^11.1.0", "vaul": "^1.1.2", "zod": "^3.24.2"}, "devDependencies": {"@types/lodash": "^4.17.16", "@types/react": "^19.0.0", "@types/react-dom": "^19.0.0", "@types/uuid": "^10.0.0", "@umijs/plugins": "^4.4.6", "tailwindcss": "^4.0.14", "typescript": "^5.8.2"}}