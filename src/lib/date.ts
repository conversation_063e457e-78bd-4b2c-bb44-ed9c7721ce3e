import {format, parse} from "date-fns";

/**
 * 格式化日期时间为 URL 友好的格式
 * @param date 日期对象
 * @returns 格式化后的字符串 (YYYY-MM-DDTHH:mm)
 */
export function formatDateTimeForUrl(date: Date): string {
  return format(date, "yyyy-MM-dd'T'HH:mm");
}

/**
 * 从 URL 格式解析日期时间
 * @param dateString URL 中的日期时间字符串
 * @returns 日期对象
 */
export function parseDateTimeFromUrl(dateString: string): Date {
  return parse(dateString, "yyyy-MM-dd'T'HH:mm", new Date());
}

/**
 * 格式化时间为显示格式
 * @param date 日期对象
 * @returns 格式化后的字符串 (HH:mm)
 */
export function formatTimeForDisplay(date: Date): string {
  return format(date, "HH:mm");
}

/**
 * 从显示格式解析时间
 * @param timeString 显示格式的时间字符串
 * @param baseDate 基准日期
 * @returns 包含时间的日期对象
 */
export function parseTimeFromDisplay(timeString: string, baseDate: Date): Date {
  const [hours, minutes] = timeString.split(':').map(Number);
  const date = new Date(baseDate);
  date.setHours(hours, minutes);
  return date;
} 