import {ResponseStructure} from '@/app';
import {request} from 'umi';
import {Page, PageQuery, Specification} from "@/types";
import {Permission} from "@/service/auth";
import {Role} from "@/service/role";
import {Group} from "@/service/group";

export type Account = {
    id: string;
    code: string;
    name: string;
    email: string;
    phone: string;
}
export  type Preference ={
    theme: themeMode
    workspace: string
}

export type themeMode = 'DARK' | 'LIGHT' |'SYSTEM';


export type AccountInfo={
    account: string;
    user:{
        name: string;
        email: string;
        phone: string;
    }
    roles:Role[]
    groups:Group[]
    preference: Preference;
}

export type AccountSpecification = {
    userName?: string;
    realName?: string;
    orgId?: string;
    roleId?: string;
    groupId?: string;
}

export type AccountAuthorization = {
    id: string;
    roles?: Pick<Role, 'id'>[];
    groups?: Pick<Group, 'id'>[];
}

export type AccountPreferences={
    account:string;
    preference: Preference;
}

export type UserInfoUnit={
    name:string;
    orgs: Array<{
        parent: {
            name: string;
        }
    }>;
}

export function fetchAccounts(data: PageQuery<Specification<Account>>){
    return  request<ResponseStructure<Page<Account>>>('/api/account', {
        method: 'POST',
        data,
    });
}

//查询用户详细信息
export function fetchAccountInfo(){
    return  request<ResponseStructure<AccountInfo>>('/api/account/info', {
        method: 'GET',
    })
}

export function fetchAccountRoles(accountId: string) {
    return request<ResponseStructure<Role[]>>(`/api/account/roles/${accountId}`, {
        method: 'GET',
    });
}

export function accountAuthorization(data: AccountAuthorization) {
    return request<ResponseStructure<void>>('/api/account/authorization', {
        method: 'POST',
        data,
    });
}

export function fetchWorkspace(){
    return request<ResponseStructure<Permission[]>>('/api/account/workspace', {
        method: 'GET',
    });
}

export function fetchRoles(){
    return request<ResponseStructure<Role[]>>('/api/role', {
        method: 'GET',
    });
}

export function fetchAccountGroups(accountId: string) {
    return request<ResponseStructure<Group[]>>(`/api/account/groups/${accountId}`, {
        method: 'GET',
    });
}


export function savePreferences(preferences:AccountPreferences) {
    return request<ResponseStructure<void>>(`/api/account/preferences`, {
        method: 'POST',
        data: preferences,
    });
}

export function getUserInfo() {
    return request<ResponseStructure<UserInfoUnit>>(`/api/account/getUserInfo`, {
        method: 'GET'
    });
}


