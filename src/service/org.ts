import {Page, PageQuery} from "@/types";
import {request} from "umi";
import {ResponseStructure} from "@/app";


export type OrgTree = {
    value: string;
    label: string;
    id: string;
    pid: string;
    children: OrgTree[];
}
export type Org = {
    code: string;
    name: string;
    id: string;
    children: Org[];
}
export type OrgSpecification = {
    code?: string;
    name?: string;
    id?: string;
    parentId?: string;
}

export function fetchOrgPage(data: PageQuery<OrgSpecification>) {
    return request<ResponseStructure<Page<Org>>>('/api/organization/page', {
        method: 'POST',
        data,
    });
}

export function fetchOrgTree(id?: string) {
    return request<ResponseStructure<Org[]>>('/api/organization/list', {
        method: 'GET',
        params: {
            pid: id
        }
    })
}

export function fetchOrgFilter(keyword?: string) {
    return request<ResponseStructure<OrgTree[]>>('/api/organization/filter', {
        method: 'GET',
        params: {
            keyword: keyword
        }
    })
}
