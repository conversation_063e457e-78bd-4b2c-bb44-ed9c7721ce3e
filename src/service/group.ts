import {Account} from "@/service/account";
import {Page, PageQuery, Specification} from "@/types";
import {request} from "@@/exports";
import {ResponseStructure} from "@/app";
import {Permission} from "@/service/auth";
import {Group as ConditionGroup} from "@/components/condition-react/types";

export type Group = {
    code: string;
    name: string;
    id: string;
    account: Account[];
    permissions: Permission[];
}

export type GroupSave = {
    code: string;
    name: string;
    id?: string;
}

export type GroupAuthorization = {
    id: string;
    accounts?: Pick<Account, 'id'>[]
    permissions?: Pick<Permission, 'id'>[]
    groupTerm?: {
        terms: ConditionGroup,
        expression: string
    }
}

export function fetchGroups(data: PageQuery<Specification<Group>>) {
    return request<ResponseStructure<Page<Group>>>('/api/auth/group/page', {
        method: 'POST',
        data,
    });
}


export function saveGroup(data: GroupSave) {
    return request<ResponseStructure<Group>>('/api/auth/group/save', {
        method: 'POST',
        data,
    });
}

export function fetchGroupAccounts(groupId: string) {
    return request<ResponseStructure<Account[]>>(`/api/auth/group/accounts`, {
        method: 'GET',
        params: {
            groupId
        }
    })
}

export function fetchTerms(groupId: string) {
    return request<ResponseStructure<ConditionGroup | undefined>>(`/api/auth/group/terms`, {
        method: 'GET',
        params: {
            groupId
        }
    })
}

export function fetchGroupPermissions(groupId: string) {
    return request<ResponseStructure<string[]>>(`/api/auth/group/permissions`, {
        method: 'GET',
        params: {
            groupId
        }
    })
}

export function groupAuthorization(data: GroupAuthorization) {
    return request<ResponseStructure<void>>('/api/auth/group/authorization', {
        method: 'POST',
        data,
    });
}