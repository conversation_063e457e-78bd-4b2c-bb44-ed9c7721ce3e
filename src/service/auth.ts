import {ResponseStructure} from '@/app';
import {request} from 'umi';

export type LoginParams = {
    account: string;
    password: string;
    scenario: LoginScenario;
}
export type Token = {
    username: string;
    token: string;
    expireTime: number;
    roles: string[];
    permissions: string[];
}
export type LoginScenario = 'web' | 'dingTalk';

export type PermissionType = 'WORKSPACE' | 'ACTION' | 'MENU';
export type PermissionStatus = 'INACTIVE' | 'ACTIVE' | 'CONCEAL';

export interface Permission {
    id: string;
    code: string;
    name: string;
    type: PermissionType;
    description?: string;
    href?: string;
    status: PermissionStatus;
    meta?: {
        hidden?: boolean;
        icon?: string;
        [key: string]: any;
    };
    parent?: Permission;
    children?: Permission[];
}

export interface SavePermission extends Omit<Permission,'id'|'parent'|'children'> {
    id?: string;
    parent?:{
        id: string;
    } | undefined |null
}


export function login(data: LoginParams){
    return  request<ResponseStructure<Token>>('/api/auth/login', {
        method: 'POST',
        data,
    });     
}

export function fetchPermission(){
    return request<ResponseStructure<Permission[]>>('/api/auth/permissions', {
        method: 'GET',
    })
}

export function savePermission(permission: SavePermission){
    return request<ResponseStructure<Permission>>('/api/auth/permissions', {
        method: 'POST',
        data: permission,
    })
}
export function onceToken(){
    return request<ResponseStructure<string>>('/api/auth/once',{
        method: 'GET',
    })
}

export const logout = () => request('/api/auth/logout', {
    method: 'POST',
});


