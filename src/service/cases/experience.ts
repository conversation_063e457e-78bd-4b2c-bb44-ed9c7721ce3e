import {Page, PageQuery, Specification} from "@/types";
import {request} from "umi";
import {ResponseStructure} from "@/app";


export interface ExperienceClassify {
    id: string;
    code: string;
    name: string;
    parent: ExperienceClassify | undefined;
    children: ExperienceClassify[];
}

export interface ExperienceClassifySave {
    id: string | undefined;
    code: string;
    name: string;
    parentId: string | undefined;
}


export type ExperienceCaseStatus = 'ON_SHELVES' | 'OFF_SHELVES';

export type ExperienceTagColor = 'default' | 'secondary' | 'destructive' | 'outline';


export interface ExperienceCase {
    id: string;
    code: string;
    path:[];
    title: string;
    content: string;
    status: ExperienceCaseStatus;
    classifyId: string;
    tags: string[];
    caseOfYear: string;
    postDate:string;
    postUser:string;
    file:{
        id: string;
        url: string;
        fileName: string;
        metadata?:{
            previewUrl: string;
        }
    }
}

export interface ExperienceCaseSave {
    id: string | undefined;
    code: string;
    title: string;
    classifyId: string;
    tags: string[];
    caseOfYear: string;
    fileId:string;
    postDate:string;
    status: ExperienceCaseStatus;
}

export function getTagColor(tag: string): ExperienceTagColor {
    const primaryColorWords = /.*[成功|优秀].*/;
    if (tag.match(primaryColorWords)) {
        return 'default';
      }
      return 'destructive';
}

export function translateStatus(status: ExperienceCaseStatus) {
    switch (status) {
        case 'ON_SHELVES':
            return '上架';
        case 'OFF_SHELVES':
            return '下架';
    }
}

export function fetchClassifiesTree(id: String) {
    return request<ResponseStructure<ExperienceClassify[]>>('/api/experience/classify/tree', {
        method: 'GET',
        params: {
            pid: id
        }
    });
}
export function filterClassifies(keywords: string) {
    return request<ResponseStructure<ExperienceClassify[]>>('/api/experience/classify/filter', {
        method: 'GET',
        data: {
            keywords,
        },
    });
}
export function saveClassify(classify: ExperienceClassifySave) {
    return request<ResponseStructure<ExperienceClassify[]>>('/api/experience/classify/save', {
        method: 'POST',
        data: classify
    });
}

export function fetchCasesPage(query: PageQuery<Specification<ExperienceCase>>) {
    return request<ResponseStructure<Page<ExperienceCase>>>('/api/experience/cases', {
        method: 'POST',
        data: query,
    });
}

export function saveCase(experienceCase: ExperienceCaseSave) {
    return request<ResponseStructure<ExperienceCase>>('/api/experience/cases/save', {
        method: 'POST',
        data: experienceCase,
    });
}

export function deleteCase(id: string) {
    return request<ResponseStructure<void>>(`/api/experience/cases/${id}`, {
        method: 'DELETE',
    });
}

export function fetchCaseDetail(id: string) {
    return request<ResponseStructure<ExperienceCase>>(`/api/experience/cases/${id}`, {
        method: 'GET'
    });
}
