import {Page, PageQuery, Specification} from "@/types";
import {request} from "umi";
import {ResponseStructure} from "@/app";
import {Group as ConditionGroup} from "@/components/condition-react/types";
import {Group} from "@/service/group";


export interface AuthorizationRule {
    id?: string;
    terms: ConditionGroup
    expression: string
    descriptions: string
    createdTime:string
    groups: Group[]
}

export interface AuthorizationRuleSave {
    id?: string;
    terms: ConditionGroup
    expression: string
    descriptions: string
    groups:Pick<Group, 'id'>[]
}

export function fetchRules(query: PageQuery<Specification<AuthorizationRule>>) {
    return request<ResponseStructure<Page<AuthorizationRule>>>('/api/product/authorization-rule', {
        method: 'POST',
        data: query,
    });
}

export function saveRule(downloadControl: AuthorizationRuleSave) {
    return request<ResponseStructure<AuthorizationRule>>('/api/product/authorization-rule/save', {
        method: 'POST',
        data: downloadControl,
    });
}

export function deleteRule(id: string) {
    return request<ResponseStructure<void>>(`/api/product/authorization-rule/${id}`, {
        method: 'DELETE',
    });
}

export function fetchRule(id: string) {
    return request<ResponseStructure<AuthorizationRule>>(`/api/product/authorization-rule/${id}`, {
        method: 'GET'
    });
}
