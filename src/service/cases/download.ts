import {Page, PageQuery, Specification} from "@/types";
import {request} from "umi";
import {ResponseStructure} from "@/app";
import {Group, fetchGroups} from "@/service/group";

export interface Tag {
    id: string;
    name: string;
}


export interface DownloadControl {
    id: string;
    tag: string;
    groups: Group[];
    limits: number;
    createdTime: string;
    modifiedTime: string;
}

export interface DownloadControlSave {
    id?: string;
    tag: string; // 单个 Tag ID
    groups: Pick<Group, 'id'>[]; // Group IDs
    limits: number;
}

export interface LimitRecordSpecification {
    accountCode: string;
    accountName: string;
}

export interface LimitRecord {
    id: string;
    tag: string;
    counts: number;
    account: {
        user: {
            code: string
            name: string
        }
    }
    attachments:{
        file: {
            id: string;
            originalFilename: string;
            metadata?: {
                previewUrl: string;
            }
        }
    } []
}

export function fetchDownloadControlPage(query: PageQuery<Specification<DownloadControl>>) {
    return request<ResponseStructure<Page<DownloadControl>>>('/api/product/download-limit', {
        method: 'POST',
        data: query,
    });
}

export function saveDownloadControl(downloadControl: DownloadControlSave) {
    return request<ResponseStructure<DownloadControl>>('/api/product/download-limit/save', {
        method: 'POST',
        data: downloadControl,
    });
}

export function deleteDownloadControl(id: string) {
    return request<ResponseStructure<void>>(`/api/product/download-limit/${id}`, {
        method: 'DELETE',
    });
}

export function fetchDownloadControlDetail(id: string) {
    return request<ResponseStructure<DownloadControl>>(`/api/product/download-limit/${id}`, {
        method: 'GET'
    });
}

export function fetchLimitRecords(query: PageQuery<Specification<LimitRecord>>) {
    return request<ResponseStructure<Page<LimitRecord>>>(`/api/product/download-limit/records`, {
        method: 'POST',
        data:query
    });
}

export function fetchTags() {
    // For now, we'll return some mock tags since we're manually filling them
    return Promise.resolve({
        success: true,
        data: [
            {id: "产品案例", name: "产品案例"},
            {id: "产品案例", name: "技术文档"},
            {id: "产品案例", name: "解决方案"},
            {id: "产品案例", name: "白皮书"},
            {id: "产品案例", name: "操作手册"}
        ]
    });
}
