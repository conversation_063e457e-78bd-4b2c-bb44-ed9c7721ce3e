import {Page, PageQuery} from "@/types";
import {request} from "umi";
import {ResponseStructure} from "@/app";

export interface PathInfo{
    id:string
    name:string
}

export interface ShareResource {
    id: string;
    name: string;
    parentId: string;
    path: PathInfo[];
    type: 'DIRECTORY' | 'FILE';
    file?: {
        id: string;
        url: string;
        originalFilename: string;
        metadata?: {
            previewUrl: string;
        }
        ext:string
    };
    tags: string[];
    content: string[];
    createdTime: string;
}

export interface FileSave {
    name: string;
    parentId: string | undefined;
    type: 'FILE';
    tags:string[]
    fileId?:string
}


export interface FolderSave {
    name: string;
    parentId: string | undefined;
    type: 'DIRECTORY';
    tags:string[]
}
// 重命名资源
export interface ResourceRename {
    id: string;
    name: string;
}


export function  fetchResources(pageQuery:PageQuery<ShareResource>) {
    return request<ResponseStructure<Page<ShareResource>>>('/api/share/resource',{
        method: 'POST',
        data: pageQuery,
    })
}


export function  fetchPath(resourceId:string) {
    return request<ResponseStructure<PathInfo[]>>( `/api/share/resource/path/${resourceId}`,{
        method: 'GET',
    })
}

export function createFolder(folder: FolderSave) {
    return request<ResponseStructure<ShareResource[]>>('/api/share/resource/folder', {
        method: 'POST',
        data: folder
    });
}

export function createFile(classify: FileSave) {
    return request<ResponseStructure<ShareResource[]>>('/api/share/resource/file', {
        method: 'POST',
        data: classify
    });
}

export function renameResource(rename: ResourceRename) {
    return request<ResponseStructure<ShareResource>>('/api/share/resource/rename', {
        method: 'POST',
        data: rename
    })
}

export function deleteResource(id: string) {
    return request<ResponseStructure<void>>(`/api/share/resource/delete`, {
        method: 'GET',
        params: {
            id
        }
    });
}

