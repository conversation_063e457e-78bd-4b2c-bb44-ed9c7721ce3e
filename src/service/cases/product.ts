import {Page, PageQuery, Specification} from "@/types";
import {request} from "umi";
import {ResponseStructure} from "@/app";


export interface ProductClassify {
  id: string;
  code: string;
  name: string;
  parent: ProductClassify | undefined;
  children: ProductClassify[];
}

export interface ProductClassifySave {
  id: string | undefined;
  code: string;
  name: string;
  parentId: string | undefined;
}


export type ProductCaseStatus = 'ON_SHELVES' | 'OFF_SHELVES';

export type ProductTagColor = 'default' | 'secondary' | 'destructive' | 'outline';


export interface ProductCase {
  id: string;
  code: string;
  title: string;
  content: string[];
  status: ProductCaseStatus;
  classifyId: string;
  tags: string[];
  attachments: Array<{
    id:string
    file: {
      id: string;
      fileName: string;
      metadata?: {
        previewUrl: string;
      }
    }
  }>;
}

export interface ProductCaseSave {
  id: string | undefined;
  code: string;
  title: string;
  classifyId: string;
  tags: string[];
  attachments: Array<{
    fileId: string;
  }>;
  status: ProductCaseStatus;
}



export function fetchClassifiesTree(id: String) {
  return request<ResponseStructure<ProductClassify[]>>('/api/product/classify/tree', {
    method: 'GET',
    params: {
      pid: id
    }
  });
}
export function filterClassifies(keywords: string) {
  return request<ResponseStructure<ProductClassify[]>>('/api/product/classify/filter', {
    method: 'GET',
    data: {
      keywords,
    },
  });
}
export function saveClassify(classify: ProductClassifySave) {
  return request<ResponseStructure<ProductClassify[]>>('/api/product/classify/save', {
    method: 'POST',
    data: classify
  });
}

export function fetchCasesPage(query: PageQuery<Specification<ProductCase>>) {
  return request<ResponseStructure<Page<ProductCase>>>('/api/product/cases', {
    method: 'POST',
    data: query,
  });
}

export function saveCase(productCase: ProductCaseSave) {
  return request<ResponseStructure<ProductCase>>('/api/product/cases/save', {
    method: 'POST',
    data: productCase,
  });
}

export function deleteCase(id: string) {
  return request<ResponseStructure<void>>(`/api/product/cases/${id}`, {
    method: 'DELETE',
  });
}

export function fetchCaseDetail(id: string) {
  return request<ResponseStructure<ProductCase>>(`/api/product/cases/${id}`, {
    method: 'GET'
  });
}
