import {Page, PageQuery, Specification} from "@/types";
import {request} from "umi";
import {ResponseStructure} from "@/app";
import {Navigation, NavigationTreeOptions} from "@/service/news/navigation";
import {NewsBlock} from "@/service/news/block";

export type ExperienceTagColor = 'default' | 'secondary' | 'destructive' | 'outline';

export interface NewsPaper {
    id?: string;
    /** 栏目名称 */
    title: string;

    /** 标签 */
    tags: string[];

    /** 期数 */
    periods: number;

    /** 页数 */
    pageNum: number;

    /** 主办 */
    sponsor: string;

    /** 出版日期 */
    publishDate: string;

    /** 发布时间 */
    issueTime: string;

    /** 发布人 */
    issuer: string;

    /** 发布单位 */
    issueUnit?: string;

    /** 状态 */
    status: string;
    /** 阅读量 */
    readingNum: string;

    newsBlockId: string;
    newsBlock: {
        id: string;
        title: string;
        navigation: {
            id: string;
            title: string;
        }
    }
    file: {
        id: string;
        originalFilename: string;
        metadata?: {
            previewUrl: string;
        }
    }

    // Audit fields
    createTime?: string;
    updateTime?: string;
    createBy?: string;
    updateBy?: string;
}

export interface NewspaperTreeOptions {
    id: string;
    title: string;
    newspaperBoards: Array<{
        id:string
        title: string;
    }>;
}

export interface NewspaperSave {
    id?: string | null |undefined;
    title: string;
    tags: string[];
    periods: number;
    pageNum: number;
    sponsor: string;
    publishDate: string;
    issueTime: string;
    issuer: string;
    issueUnit: string;
    navigationId: string;
    newsBlockId: string;
    fileId: string;
}


export function getTagColor(tag: string): ExperienceTagColor {
    const primaryColorWords = /.*[成功|优秀].*/;
    if (tag.match(primaryColorWords)) {
        return 'default';
    }
    return 'default';
}

export function fetchNewspaperPage(query: PageQuery<Specification<NewsPaper>>) {
    return request<ResponseStructure<Page<NewsPaper>>>('/api/news/newspaper/page', {
        method: 'POST',
        data: query,
    });
}

export function fetchNewspaperTree() {
    return request<ResponseStructure<NewspaperTreeOptions[]>>('/api/news/newspaper/getNewspaperTree', {
        method: 'GET'
    });
}

export function saveNewspaper(newspaper: NewspaperSave) {
    return request<ResponseStructure<NewsPaper>>('/api/news/newspaper/save', {
        method: 'POST',
        data: newspaper,
    });
}

export function deleteNewspaper(id: string) {
    return request<ResponseStructure<void>>(`/api/news/newspaper/delete/${id}`, {
        method: 'DELETE',
    });
}

export function getNewspaperDetail(id: string) {
    return request<ResponseStructure<NewsPaper>>(`/api/news/newspaper/detail/${id}`, {
        method: 'GET',
    });
}
