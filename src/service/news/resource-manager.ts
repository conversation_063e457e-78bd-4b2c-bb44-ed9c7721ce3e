import {Page, PageQuery, Specification} from "@/types";
import {request} from "umi";
import {ResponseStructure} from "@/app";
import {NewsBlock} from "@/service/news/block";
import {Navigation} from "@/service/news/navigation";

export interface NewsResourceManager {
    id?: string;
    /**
     * 新闻标题
     */
    title: string;

    /**
     * 编码
     */
    code: string;

    /**
     * 标签
     */
    tags: string[];

    /**
     * 作者
     */
    author: string;

    /**
     * 所属部门
     */
    issueDept: string;

    /**
     * 发布时间
     */
    issueTime: string;

    /**
     * 发布人
     */
    issuer: string;

    /**
     * 发布单位
     */
    issueUnit: string;

    /**
     * 内容摘要
     */
    contentBrief: string;

    /**
     * 内容
     */
    content: string;

    /**
     * 内容
     */
    contentFile: {
        id: string;
        originalFilename: string;
        metadata: {
            previewUrl: string;
        };
    };

    /**
     * 新闻类型
     */
    newsType: "PLATFORM" | "OA";

    /**
     * 新闻类型
     */
    contentType: "FILE" | "CONTENT";

    /**
     * 阅读量
     */
    readingNum: number;

    /**
     * 序列
     */
    sequence?: number;

    /**
     * 置顶
     */
    topStatus: string;

    /**
     * 状态
     */
    status: "ENABLED" | "DISABLED";

    /**
     * 删除状态
     */
    deleted?: string;

    /**
     * 封面图
     */
    attachments: Array<{
        id: string
        file: {
            id: string;
            fileName: string;
            metadata?: {
                previewUrl: string;
            }
            url: string;
        }
    }>;

    /**
     * 模块
     */
    newsBlocks: Array<{
        id: string;
        title: string;
    }>

    /**
     * 报刊版面
     */
    newspaperBoards: Array<{
        id: string;
        title: string;
    }>
}


export interface NewsResourceManagerSave {
    id?: string | undefined;
    title: string;
    code: string;
    tags: string[];
    author: string;
    issueDept: string;
    issueTime: string;
    issuer: string;
    issueUnit: string;
    contentBrief: string;
    content: string;
    contentFileId: string;
    readingNum: number;
    newsType: "PLATFORM" | "OA";
    contentType: "FILE" | "CONTENT";
    topStatus: string;
    sequence: number;
    status: "ENABLED" | "DISABLED";
    attachments: Array<{
        fileId: string;
    }>;
    newsBlocks: Array<{
        id: string;
    }>;
    newspaperBoards: Array<{
        id: string;
    }>;
}

export function fetchResourceManagerPage(query: PageQuery<Specification<NewsResourceManager>>) {
    return request<ResponseStructure<Page<NewsResourceManager>>>('/api/news/resourceManager/page', {
        method: 'POST',
        data: query,
    });
}

export function saveResourceManager(newsResourceManagerSave: NewsResourceManagerSave) {
    return request<ResponseStructure<NewsResourceManager>>('/api/news/resourceManager/save', {
        method: 'POST',
        data: newsResourceManagerSave,
    });
}

export function deleteResourceManager(id: string) {
    return request<ResponseStructure<void>>(`/api/news/resourceManager/delete/${id}`, {
        method: 'DELETE',
    });
}

export function getResourceManagerDetail(id: string) {
    return request<ResponseStructure<NewsResourceManager>>(`/api/news/resourceManager/detail/${id}`, {
        method: 'GET',
    });
}
