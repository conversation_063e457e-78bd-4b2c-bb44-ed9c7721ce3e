import {Page, PageQuery, Specification} from "@/types";
import {request} from "umi";
import {ResponseStructure} from "@/app";
import {Navigation} from "@/service/news/navigation";
import {NewsBlock} from "@/service/news/block";

export type ExperienceTagColor = 'default' | 'secondary' | 'destructive' | 'outline';

export interface NewspaperBoard {
    id?: string;
    /** 栏目名称 */
    title: string;

    newspaperId: string;
    newspaper: {
        id: string;
        title: string;
    }

    sequence: number;

    image: {
        id: string;
        originalFilename: string;
        url: string;
        metadata: {
            previewUrl: string;
        };
    };
    unfold: "true" | "false";

    // Audit fields
    createTime?: string;
    updateTime?: string;
    createBy?: string;
    updateBy?: string;
}


export interface NewspaperBoardSave {
    id?: string | null |undefined;
    title: string;
    newspaperId: string;
    imageId: string;
    unfold: "false" | "true";
    sequence: number;
}

export function fetchNewspaperBoardPage(query: PageQuery<Specification<NewspaperBoard>>) {
    return request<ResponseStructure<Page<NewspaperBoard>>>('/api/news/newspaperBoard/page', {
        method: 'POST',
        data: query,
    });
}

export function saveNewspaperBoard(newspaperBoard: NewspaperBoardSave) {
    return request<ResponseStructure<NewspaperBoard>>('/api/news/newspaperBoard/save', {
        method: 'POST',
        data: newspaperBoard,
    });
}

export function deleteNewspaperBoard(id: string) {
    return request<ResponseStructure<void>>(`/api/news/newspaperBoard/delete/${id}`, {
        method: 'DELETE',
    });
}

export function getNewspaperBoardDetail(id: string) {
    return request<ResponseStructure<NewspaperBoard>>(`/api/news/newspaperBoard/detail/${id}`, {
        method: 'GET',
    });
}
