import {Page, PageQuery, Specification} from "@/types";
import {request} from "umi";
import {ResponseStructure} from "@/app";

export interface NewsBanner {
    id: string;
    /**
     * 模块名称
     */
    title: string;

    /**
     * 模板图片
     */
    image: {
        id: string;
        originalFilename: string;
        url: string;
        metadata: {
            previewUrl: string;
        };
    };

    /**
     * 栏目
     */
    navigation: {
        id: string;
        title: string;
    }

    /**
     * 文章链接
     */
    articleUrl: string;

    /**
     * 发布人
     */
    issuer: string;

    /**
     * 发布时间
     */
    issueDate: string;

    /**
     * 序列
     */
    sequence: number;

    // Audit fields
    createTime?: string;
    updateTime?: string;
    createBy?: string;
    updateBy?: string;
}


export interface bannerSave {
    title: string;
    imageId?: string;
    navigationId: string;
    sequence: number;
    id?: string | undefined;
    articleUrl?: string;
    issuer?: string;
    issueDate?: string;
}

export function fetchBannerSelect() {
    return request<ResponseStructure<NewsBanner[]>>('/api/news/banner/getBannerSelect', {
        method: 'GET'
    });
}

export function fetchBannerPage(query: PageQuery<Specification<NewsBanner>>) {
    return request<ResponseStructure<Page<NewsBanner>>>('/api/news/banner/page', {
        method: 'POST',
        data: query,
    });
}

export function saveBanner(banner: bannerSave) {
    return request<ResponseStructure<NewsBanner>>('/api/news/banner/save', {
        method: 'POST',
        data: banner,
    });
}

export function deleteBanner(id: string) {
    return request<ResponseStructure<void>>(`/api/news/banner/delete/${id}`, {
        method: 'DELETE',
    });
}
