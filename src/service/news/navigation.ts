import {Page, PageQuery, Specification} from "@/types";
import {request} from "umi";
import {ResponseStructure} from "@/app";
import {NewsBlock} from "@/service/news/block";

export interface Navigation {
  id: string;
  /**
   * 栏目名称
   */
  title: string;
  
  /**
   * 编码
   */
  code: string;
  
  /**
   * 父级id
   */
  parentId?: string;
  parent?: Navigation;
  
  /**
   * 说明
   */
  remark?: string;
  
  /**
   * 序列
   */
  sequence?: number;
  
  /**
   * 状态
   */
  status?: string;
  
  /**
   * 删除状态
   */
  deleted?: string;
  
  /**
   * 子栏目
   */
  children?: Navigation[];
  
  // Audit fields
  createTime?: string;
  updateTime?: string;
  createBy?: string;
  updateBy?: string;
}

export interface NavigationTreeOptions {
    id: string;
    title: string;
    blocks: Array<{
        id:string
        title: string;
    }>;
}

export interface SelectOptions {
    navigationOptions: NavigationTreeOptions[];
    blockOptions: NewsBlock[];
}

export interface NavigationSave {
    title: string;
    code: string;
    sequence: number;
    status: "ENABLED" | "DISABLED";
    id?: string | undefined;
    remark?: string | undefined;
}

// CasesNavigation API
export function fetchNavigationTree() {
    return request<ResponseStructure<NavigationTreeOptions[]>>('/api/news/navigation/getNavigationTree', {
        method: 'GET'
    });
}

export function fetchNavigationPage(query: PageQuery<Specification<Navigation>>) {
    return request<ResponseStructure<Page<Navigation>>>('/api/news/navigation/page', {
        method: 'POST',
        data: query,
    });
}

export function saveNavigation(navigation: NavigationSave) {
    return request<ResponseStructure<Navigation>>('/api/news/navigation/save', {
        method: 'POST',
        data: navigation,
    });
}

export function deleteNavigation(id: string) {
    return request<ResponseStructure<void>>(`/api/news/navigation/delete/${id}`, {
        method: 'DELETE',
    });
}

export function getNavigationDetail(id: string) {
    return request<ResponseStructure<Navigation>>(`/api/news/navigation/detail/${id}`, {
        method: 'GET',
    });
}
