import {Page, PageQuery, Specification} from "@/types";
import {request} from "umi";
import {ResponseStructure} from "@/app";

export interface NewsBlock {
    id: string;
    /**
     * 模块名称
     */
    title: string;

    /**
     * 编码
     */
    code: string;

    /**
     * 模板图片
     */
    image: {
        id: string;
        originalFilename: string;
        url: string;
        metadata: {
            previewUrl: string;
        };
    };

    /**
     * 栏目
     */
    navigation: {
        id: string;
        title: string;
    }

    /**
     * 说明
     */
    remark?: string;

    /**
     * 序列
     */
    sequence?: number;

    /**
     * 状态
     */
    status?: string;

    /**
     * 删除状态
     */
    deleted?: string;

    // Audit fields
    createTime?: string;
    updateTime?: string;
    createBy?: string;
    updateBy?: string;
}


export interface blockSave {
    title: string;
    code: string;
    imageId?: string;
    navigationId: string;
    sequence: number;
    status: "ENABLED" | "DISABLED";
    id?: string | undefined;
    remark?: string | undefined;
}

// CasesNavigation API
export function fetchBlockSelect() {
    return request<ResponseStructure<NewsBlock[]>>('/api/news/block/getBlockSelect', {
        method: 'GET'
    });
}

export function fetchBlockPage(query: PageQuery<Specification<NewsBlock>>) {
    return request<ResponseStructure<Page<NewsBlock>>>('/api/news/block/page', {
        method: 'POST',
        data: query,
    });
}

export function saveBlock(block: blockSave) {
    return request<ResponseStructure<NewsBlock>>('/api/news/block/save', {
        method: 'POST',
        data: block,
    });
}

export function deleteBlock(id: string) {
    return request<ResponseStructure<void>>(`/api/news/block/delete/${id}`, {
        method: 'DELETE',
    });
}

export function getBlockDetail(id: string) {
    return request<ResponseStructure<NewsBlock>>(`/api/news/block/detail/${id}`, {
        method: 'GET',
    });
}
