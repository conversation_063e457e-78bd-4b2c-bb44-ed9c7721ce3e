import {Page, PageQuery, Specification} from "@/types";
import {request} from "umi";
import {ResponseStructure} from "@/app";

export type ExperienceTagColor = 'default' | 'secondary' | 'destructive' | 'outline';

export interface InteriorProject {
    id?: string;
    /** 栏目名称 */
    title: string;

    /** 负责人 */
    tags: string[];

    /** 负责人 */
    principal: string;

    /** 成员 */
    member: string;

    /** 项目成本 */
    projectCost: string;

    /** 开始时间 */
    startTime: string;

    /** 结束时间 */
    endTime: string;

    /** 项目周期 */
    planningCycle: string;

    /** 说明 */
    remark?: string;

    /** 状态 */
    // status: string;

    // Audit fields
    createTime?: string;
    updateTime?: string;
    createBy?: string;
    updateBy?: string;
}


export interface ProjectSave {
    id?: string | null |undefined;
    title: string;
    tags: string[];
    principal: string;
    member: string;
    projectCost: string;
    // status: string;
    startTime: string;
    endTime: string;
    planningCycle: string;
    remark?: string;
}

export function getTagColor(tag: string): ExperienceTagColor {
    const primaryColorWords = /.*[成功|优秀].*/;
    if (tag.match(primaryColorWords)) {
        return 'default';
    }
    return 'default';
}

export function fetchProjectPage(query: PageQuery<Specification<InteriorProject>>) {
    return request<ResponseStructure<Page<InteriorProject>>>('/api/interior/project/page', {
        method: 'POST',
        data: query,
    });
}

export function saveProject(project: ProjectSave) {
    return request<ResponseStructure<InteriorProject>>('/api/interior/project/save', {
        method: 'POST',
        data: project,
    });
}

export function deleteProject(id: string) {
    return request<ResponseStructure<void>>(`/api/interior/project/delete/${id}`, {
        method: 'DELETE',
    });
}

export function getProjectDetail(id: string) {
    return request<ResponseStructure<InteriorProject>>(`/api/interior/project/${id}`, {
        method: 'GET',
    });
}
