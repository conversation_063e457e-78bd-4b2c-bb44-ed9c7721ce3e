import {Page, PageQuery, Specification} from "@/types";
import {request} from "umi";
import {ResponseStructure} from "@/app";

export interface InteriorProjectStage {
    id?: string;
    /** code */
    code: string;
    /** 标签 */
    title: string;
}

export function fetchProjectStates() {
    return request<ResponseStructure<InteriorProjectStage[]>>('/api/interior/projectStage/fetchStages', {
        method: 'GET'
    });
}