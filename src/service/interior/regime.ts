import {Page, PageQuery, Specification} from "@/types";
import {request} from "umi";
import {ResponseStructure} from "@/app";

export type ExperienceTagColor = 'default' | 'secondary' | 'destructive' | 'outline';

export interface InteriorRegime {
    id?: string;
    /** 栏目名称 */
    title: string;

    /** 负责人 */
    tags: string[];

    /** 负责人 */
    principal: string;

    /** 负责组 */
    responsibleTeam: string;

    /** 制度生效期 */
    effectiveDate: string;

    /** 废止日期 */
    failureDate: string;

    /** 制度版本号 */
    regimeVersion: string;

    /** 状态 */
    status: string;

    /** 说明 */
    remark?: string;

    // Audit fields
    createTime?: string;
    updateTime?: string;
    createBy?: string;
    updateBy?: string;
}


export interface RegimeSave {
    id?: string | null |undefined;
    title: string;
    tags: string[];
    principal: string;
    responsibleTeam: string;
    effectiveDate: string;
    failureDate: string;
    regimeVersion: string;
    status: string;
    remark?: string;
}

export function getTagColor(tag: string): ExperienceTagColor {
    const primaryColorWords = /.*[成功|优秀].*/;
    if (tag.match(primaryColorWords)) {
        return 'default';
    }
    return 'default';
}

export function fetchRegimePage(query: PageQuery<Specification<InteriorRegime>>) {
    return request<ResponseStructure<Page<InteriorRegime>>>('/api/interior/regime/page', {
        method: 'POST',
        data: query,
    });
}

export function saveRegime(regime: RegimeSave) {
    return request<ResponseStructure<InteriorRegime>>('/api/interior/regime/save', {
        method: 'POST',
        data: regime,
    });
}

export function deleteRegime(id: string) {
    return request<ResponseStructure<void>>(`/api/interior/regime/delete/${id}`, {
        method: 'DELETE',
    });
}

export function getRegimeDetail(id: string) {
    return request<ResponseStructure<InteriorRegime>>(`/api/interior/regime/${id}`, {
        method: 'GET',
    });
}
