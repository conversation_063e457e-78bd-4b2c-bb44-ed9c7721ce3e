import {Page, PageQuery, Specification} from "@/types";
import {request} from "umi";
import {ResponseStructure} from "@/app";

export interface InteriorProjectDatum {
    id?: string;
    /** 栏目名称 */
    title: string;

    /** 标签 */
    tags: string[];

    /** 类型 */
    type: 'FILE' |'DIRECTORY';

    /** 流程id */
    stageId: string;

    /** 项目id */
    projectId: string;

    /** 父级id */
    parentId: string;

    /** 父级对象 */
    parent: {
        id: string;
    };

    /** 说明 */
    remark?: string;

    /** 状态 */
    status: string;

    file: {
        id: string;
        originalFilename: string;
        metadata: {
            previewUrl: string;
        };
    };

    // Audit fields
    createTime?: string;
    updateTime?: string;
    createBy?: string;
    updateBy?: string;
}


export interface ProjectDatumSave {
    id?: string | null |undefined;
    title: string;
    tags: string[];
    type: string;
    stageId: string;
    projectId: string;
    parentId: string;
    fileId: string;
    status: string;
    remark?: string;
}

export function fetchProjectDatumPage(query: PageQuery<Specification<InteriorProjectDatum>>) {
    return request<ResponseStructure<Page<InteriorProjectDatum>>>('/api/interior/projectDatum/page', {
        method: 'POST',
        data: query,
    });
}

export function saveProjectDatum(project: ProjectDatumSave) {
    return request<ResponseStructure<InteriorProjectDatum>>('/api/interior/projectDatum/save', {
        method: 'POST',
        data: project,
    });
}

export function deleteProjectDatum(id: string) {
    return request<ResponseStructure<void>>(`/api/interior/projectDatum/delete/${id}`, {
        method: 'DELETE',
    });
}

export function getProjectDatumDetail(id: string) {
    return request<ResponseStructure<InteriorProjectDatum>>(`/api/interior/projectDatum/detail/${id}`, {
        method: 'GET',
    });
}
