import {Account} from "@/service/account";
import {Page, PageQuery, Specification} from "@/types";
import {request} from "@@/exports";
import {ResponseStructure} from "@/app";
import {Permission} from "@/service/auth";

export type Role= {
    code: string;
    name: string;
    id: string;
    accounts:Account[];
    permissions:Permission[];
}
export type RoleSave = {
    id?: string;
    code: string;
    name: string;
}
export type RoleSpecification = {
    id?: string;
    code: string;
    name: string;
}
export type RoleAuthorization ={
    id: string;
    accounts?:Pick<Account, 'id'>[]
    permissions?:Pick<Permission, 'id'>[]
}

export function fetchRoles(data: PageQuery<Specification<Role>>){
    return  request<ResponseStructure<Page<Role>>>('/api/auth/role/page', {
        method: 'POST',
        data,
    });
}
export function saveRole(data: RoleSave){
    return request<ResponseStructure<Role>>('/api/auth/role/save', {
        method: 'POST',
        data,
    });
}
export function fetchRoleAccounts(id: string){
    return request<ResponseStructure<Account[]>>(`/api/auth/role/accounts`, {
        method:'GET',
        params:{
            roleId: id
        }
    })
}

export function fetchRolePermissions(roleId: string){
    return request<ResponseStructure<string[]>>(`/api/auth/role/permissions`, {
        method:'GET',
        params:{
            roleId: roleId
        }
    })
}

export function roleAuthorization(data: RoleAuthorization) {
    return  request<ResponseStructure<Page<Role>>>('/api/auth/role/authorization', {
        method: 'POST',
        data,
    });
}