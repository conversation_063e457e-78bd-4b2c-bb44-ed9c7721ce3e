import {ResponseStructure} from '@/app';
import {request} from 'umi';

interface UploadResponse {
    fileUrl: string;
    previewUrl: string;
}

interface FileInfo {
    id: string;
    url: string;
}

export function uploadFile(formData: FormData) {
    return request<ResponseStructure<string>>('/api/storage/upload', {
        method: 'POST',
        data: formData,
        headers: {
            'content-type': 'multipart/form-data'
        }
    });
}

export function uploadFileInfo(formData: FormData) {
    return request<ResponseStructure<FileInfo>>('/api/storage/uploadInfo', {
        method: 'POST',
        data: formData,
        headers: {
            'content-type': 'multipart/form-data'
        }
    });
}

export function download(token: string, id: string) {
    return request('/api/storage/download', {
        method: 'GET',
        params: {
            token,
            id,
        },
        getResponse: true,
        skipErrorHandler:true,
        responseType:'stream'
    })
}

export function fetchPreviewUrl(fileId: string) {
    return request<ResponseStructure<string>>('/api/file/preview', {
        method: 'GET',
        params: {fileId}
    });
}

export function addPreviewTask(fileId: string) {
    return request<ResponseStructure<string>>('/api/file/addTask', {
        method: 'GET',
        params: {fileId}
    });
}
