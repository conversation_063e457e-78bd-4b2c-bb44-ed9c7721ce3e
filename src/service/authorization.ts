import {Account} from "@/service/account";
import {request} from "umi";
import {ResponseStructure} from "@/app";
import {Org} from "@/service/org";
import {Role} from "@/service/role";
import {Group} from "@/service/group";


export type ACL= {
    resource: string;

    resourceId?: string;

    policies: Policy[];
}


export type PolicyType = 'USER' | 'ROLE' | 'ORGANIZATION' | 'GROUP';

export type StrategyType = 'ROOT' | 'DRILL_UP' | 'DRILL_DOWN';

export type Policy ={
    id?: string;
    policyType: PolicyType;
    value: string;
    strategyType: StrategyType;
}

export type ACLAuthorization = {
    resource: string;
    resourceId:string;
    policies: Policy[];
}




export function fetchAuthorizationAccount(resourceId: string) {
    return request<ResponseStructure<Account[]>>(`/api/dataAuth/acl/account`, {
        method: 'GET',
        params:{
            resourceId
        }
    });
}

export function fetchAuthorizationOrg(resourceId: string) {
    return request<ResponseStructure<Org[]>>(`/api/dataAuth/acl/organization`, {
        method: 'GET',
        params:{
            resourceId
        }
    });
}


export function fetchAuthorizationRole(resourceId: string) {
    return request<ResponseStructure<Role[]>>(`/api/dataAuth/acl/role`, {
        method: 'GET',
        params:{
            resourceId
        }
    });
}


export function fetchAuthorizationGroup(resourceId: string) {
    return request<ResponseStructure<Group[]>>(`/api/dataAuth/acl/group`, {
        method: 'GET',
        params:{
            resourceId
        }
    });
}


export function aclAuthorization(data:ACLAuthorization) {
    return request<ResponseStructure<void>>(`/api/dataAuth/acl/authorization`, {
        method: 'POST',
        data: data,
    });
}

