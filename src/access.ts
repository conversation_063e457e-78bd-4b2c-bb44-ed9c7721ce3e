import {AccountInfo} from "@/service/account";

export default function (initialState:AccountInfo) {

    const workspaceSet = new Set<string>()
    const menuSet =new Set<string>()
    const actionSet = new Set<string>()


    if (initialState) {
        initialState.roles.flatMap(role => role?.permissions.filter(permission => permission.type === 'WORKSPACE'))
            .forEach(permission => workspaceSet.add(permission.code))
        initialState.groups.flatMap(group => group?.permissions.filter(permission => permission.type === 'WORKSPACE'))
            .forEach(permission => workspaceSet.add(permission.code))


        initialState.roles
            .flatMap(role => role?.permissions.filter(permission => permission.type === 'MENU'))
            .forEach(permission => menuSet.add(permission.code))

        initialState.groups
            .flatMap(group => group?.permissions.filter(permission => permission.type === 'MENU'))
            .forEach(permission => menuSet.add(permission.code))

        initialState.roles
            .flatMap(role => role?.permissions.filter(permission => permission.type === 'ACTION'))
            .forEach(permission => actionSet.add(permission.code))

        initialState.groups
            .flatMap(group => group?.permissions.filter(permission => permission.type === 'ACTION'))
            .forEach(permission => actionSet.add(permission.code))
    }

    const hasMenu = (menuCode:string) => {
        return menuSet.has(menuCode)
    }
    const hasAction = (actionCode:string) => {
        return Array.from(actionSet).some(action => action.includes(actionCode) && !actionCode.includes("VIEW"))
    }

    const hasRole = (roleCode:string) => {
        return initialState.roles.some(role => role.code === roleCode)
    }
    const hasGroup = (groupCode:string) => {
        return initialState.groups.some(group => group.code === groupCode)
    }
    const hasWorkspace = (workspaceCode:string) => {
        return workspaceSet.has(workspaceCode)
    }



    return{
        hasRole,
        hasGroup,
        hasWorkspace,
        hasMenu,
        hasAction,
    }
}