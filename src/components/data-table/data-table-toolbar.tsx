"use client";

/**
 * @fileoverview 数据表格的工具栏组件，提供搜索、过滤和视图选项功能
 */

import {
  CommaKeyOf,
  ConditionRange,
  DataTableFilterField,
  ESSpecification,
  NestedKeyOf,
  PageQuery,
  Specification,
  SQLSpecification
} from "@/types";
import type {AccessorKeyColumnDef, Table} from "@tanstack/react-table";
import {X} from "lucide-react";
import * as React from "react";
import {useMemo} from "react";

import {DataTableFacetedFilter} from "@/components/data-table/data-table-faceted-filter";
import {DataTableRangeFilter} from "@/components/data-table/data-table-range-filter";
import {DataTableViewOptions} from "@/components/data-table/data-table-view-options";
import {Button} from "@/components/ui/button";
import {Input} from "@/components/ui/input";
import {cn} from "@/lib/utils";
import {useDebouncedCallback} from "@/hooks/use-debounced-callback";

/**
 * 工具栏组件的属性接口
 * @template TData 表格数据类型
 */
interface DataTableToolbarProps<TData>
  extends React.HTMLAttributes<HTMLDivElement> {
  /** 表格实例 */
  table: Table<TData>;
  /**
   * An array of filter field configurations for the data table.
   * When options are provided, a faceted filter is rendered.
   * Otherwise, a search filter is rendered.
   *
   * @example
   * const filterFields = [
   *   {
   *     id: 'name',
   *     label: 'Name',
   *     placeholder: 'Filter by name...'
   *   },
   *   {
   *     id: 'status',
   *     label: 'Status',
   *     options: [
   *       { label: 'Active', value: 'active', icon: ActiveIcon, count: 10 },
   *       { label: 'Inactive', value: 'inactive', icon: InactiveIcon, count: 5 }
   *     ]
   *   }
   * ]
   */
  filterFields?: DataTableFilterField<TData>[];
  onFilterChange?: ( pageQuery: PageQuery<Specification<TData>>) => void;
  type?:'es' | 'sql'
}

/**
 * 数据表格工具栏组件
 * @template TData 表格数据类型
 * @param type
 * @param props 组件属性
 * @param props.table 表格实例
 * @param props.filterFields 过滤字段配置数组
 * @param props.onFilterChange 过滤条件变化时的回调函数
 * @param props.children 子元素
 * @param props.className 自定义CSS类名
 * @returns 渲染的工具栏组件，包含搜索框、过滤器和视图选项
 */
export function DataTableToolbar<TData>({
  table,
  filterFields = [],
  onFilterChange,
  type = 'sql',
  children,
  className,
  ...props
}: DataTableToolbarProps<TData>) {

  const isFiltered = useMemo(()=>{
    const  initialStateId= table.initialState.columnFilters.map(filter=>filter.id)
     const filters = table.getState().columnFilters.map(filter=>filter.id);
    return filters.filter(f=>!initialStateId.includes(f)).length > 0
  },[table.initialState,table.getState().columnFilters])
  
  // 缓存列类型计算结果
  const { searchableColumns, filterableColumns, rangeColumns } = React.useMemo(() => {
    return {
      searchableColumns: filterFields.filter((field) => !field.options && !field.range),
      filterableColumns: filterFields.filter((field) => field.options),
      rangeColumns: filterFields.filter((field) => field.range),
    };
  }, [filterFields]);
  
  // 构建查询条件
  const buildConditions = React.useCallback(() => {
    if (type === 'sql') {
      // SQL查询：构建简单的键值对
      const sqlSpecification: SQLSpecification<TData> = {};
      
      // 处理普通搜索字段
      searchableColumns.forEach((column) => {
        const value = table.getColumn(column.id)?.getFilterValue() as string;
        if (value) {
          if (!column.id.includes(',')) { // SQL 不支持多字段匹配
            sqlSpecification[column.id] = value;
          }
        }
      });

      // 处理分面过滤字段
      filterableColumns.forEach((column) => {
        const values = table.getColumn(String(column.id))?.getFilterValue() as string[];
        if (values?.length) {
          sqlSpecification[column.id] = values.length === 1 ? values[0] : values;
        }
      });

      // 处理范围过滤字段
      rangeColumns.forEach((column) => {
        const range = table.getColumn(String(column.id))?.getFilterValue() as [string | number, string | number];
        if (range?.length === 2) {
         sqlSpecification[column.id] = range;
        }
      });

      return sqlSpecification;
    } else {
      const columns = table.getAllColumns();
      // ES查询：构建复杂的查询规范
      debugger
      const specification: ESSpecification<TData> = {
        aggs: filterFields.filter(field => field.agg).map(field => field.id),
        conditions: {},
        highlight: filterFields.filter(field => field.highlight).flatMap(field => (field.id.split(',').map(id=>id as CommaKeyOf<TData>))),
        source: columns
          .filter(column => 'accessorKey' in column.columnDef)
          .map(column => (column.columnDef as AccessorKeyColumnDef<TData>).accessorKey as CommaKeyOf<TData>)
      };
      // 处理普通搜索字段
      searchableColumns.forEach((column) => {
        const value = table.getColumn(column.id)?.getFilterValue() as string;
        if (value) {
          if (column.id.includes(',')) {
            // 多字段匹配
            const fields = column.id.split(',') as CommaKeyOf<TData>[];
            specification.conditions[column.id] = {
              multi_match: {
                query: value,
                fields,
              },
            };
          } else {
            // 单字段匹配
            const termCondition: { [K in CommaKeyOf<TData>]?: any } = {};
            termCondition[column.id] = value;
            specification.conditions[column.id] = {
              term: termCondition
            };
          }
        }
      });

      // 处理分面过滤字段
      filterableColumns.forEach((column) => {
        const values = table.getColumn(String(column.id))?.getFilterValue() as string[];
        if (values?.length) {
          const termsCondition: { [K in CommaKeyOf<TData>]?: any[] } = {};
          termsCondition[column.id] = values;
          specification.conditions[column.id] = {
            terms: termsCondition
          };
        }
      });

      // 处理范围过滤字段
      rangeColumns.forEach((column) => {
        const range = table.getColumn(String(column.id))?.getFilterValue() as [string | number, string | number];
        if (range?.length === 2) {
          const rangeCondition :{ [K in CommaKeyOf<TData>]?:  ConditionRange}  = {};
          rangeCondition[column.id] = {
            from: range[0],
            to: range[1]
          };
          specification.conditions[column.id] = {
            range: rangeCondition
          };
        }
      });
      return specification;
    }
  }, [type, searchableColumns, filterableColumns, rangeColumns]);

  // 使用 useMemo 缓存查询参数
  const queryParams = React.useMemo(() => {
    const specification = buildConditions();
    const sorting = table.getState().sorting;
    const pageQuery: PageQuery<Specification<TData>> = {
      page: table.getState().pagination.pageIndex,
      pageSize: table.getState().pagination.pageSize,
      specification,
      sort:sorting.map(sort=>({
        field:sort.id,
        order:sort.desc?'desc':'asc'
      })),
    };
    return pageQuery;
  }, [
    // 只在这些值变化时重新计算
    JSON.stringify(table.getState().columnFilters), // 过滤条件变化
    JSON.stringify(table.getState().sorting),     // 排序变化
    JSON.stringify(table.getState().pagination),
  ]);

  const debounceFilterChange = useDebouncedCallback((params: PageQuery<Specification<TData>>) => {
    if (onFilterChange) {
      onFilterChange(params);
    }
  }, 500);

  // 当查询参数变化时通知父组件
  React.useEffect(() => {
      debounceFilterChange(queryParams);
    }, [queryParams,table.getState().columnFilters]);

  const  isHidden =(column: DataTableFilterField<TData>)=>{
    if (column.hidden  == undefined) return false; else return column.hidden;
  }

  return (
    <div
      role="toolbar"
      aria-orientation="horizontal"
      className={cn(
        "flex w-full items-center justify-between overflow-auto p-1",
        className,
      )}
      {...props}
    >
      <div className="flex flex-1 items-center gap-2">
        {searchableColumns.length > 0 &&
          searchableColumns.filter(column=>!isHidden(column)).map((column) => {
            const tableColumn = table.getColumn(column.id);
            return tableColumn ? (
              <Input
                key={column.id}
                placeholder={column.placeholder}
                value={(tableColumn.getFilterValue() as string) ?? ""}
                onChange={(event) => tableColumn.setFilterValue(event.target.value)}
                className="h-8 w-40 lg:w-64"
              />
            ) : null;
          })}
        {filterableColumns.length > 0 &&
          filterableColumns.filter(column=>!isHidden(column)).map(
            (column) =>
              table.getColumn(String(column.id)) && (
                <DataTableFacetedFilter
                  key={String(column.id)}
                  column={table.getColumn(String(column.id))}
                  title={column.label}
                  options={column.options ?? []}
                />
              ),
          )}
        {rangeColumns.length > 0 &&
          rangeColumns.filter(column=>!isHidden(column)).map(
            (column) =>
              table.getColumn(String(column.id)) && (
                <DataTableRangeFilter
                  key={String(column.id)}
                  column={table.getColumn(String(column.id))}
                  title={column.label}
                  type={column.range!}
                />
              ),
          )}
        {isFiltered && (
          <Button
            aria-label="重置过滤器"
            variant="ghost"
            className="h-8 px-2 lg:px-3"
            onClick={() => {
              table.resetColumnFilters()
            }}
          >
            重置
            <X className="ml-2 h-4 w-4" />
          </Button>
        )}
      </div>
      <div className="flex items-center gap-2">
        {children}
        <DataTableViewOptions table={table} />
      </div>
    </div>
  );
}
