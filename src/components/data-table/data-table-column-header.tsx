/**
 * @fileoverview 数据表格的列头部组件，提供排序和列可见性控制功能
 */

import {SelectIcon} from "@radix-ui/react-select";
import type {Column} from "@tanstack/react-table";
import {ArrowDown, ArrowUp, ChevronsUpDown, EyeOff, X} from "lucide-react";

import {Select, SelectContent, SelectItem, SelectTrigger,} from "@/components/ui/select";
import {cn} from "@/lib/utils";
import React from "react";

/**
 * 列头部组件的属性接口
 * @template TData 表格数据类型
 * @template TValue 列值类型
 */
interface DataTableColumnHeaderProps<TData, TValue>
  extends React.HTMLAttributes<HTMLDivElement> {
  /** 列实例，包含列的配置和状态 */
  column: Column<TData, TValue>;
}

/**
 * 数据表格列头部组件
 * @template TData 表格数据类型
 * @template TValue 列值类型
 * @param props 组件属性
 * @param props.column 列实例
 * @param props.title 列标题
 * @param props.className 自定义CSS类名
 * @returns 渲染的列头部组件，支持排序和显示/隐藏功能
 */
export function DataTableColumnHeader<TData, TValue>({
  column,
  className,
}: DataTableColumnHeaderProps<TData, TValue>) {
  if (!column.getCanSort() && !column.getCanHide()) {
    return <div className={cn(className)}>{column.columnDef.meta?.title || column.id}</div>;
  }

  const noneValue = `${column.id}-none`;
  const ascValue = `${column.id}-asc`;
  const descValue = `${column.id}-desc`;
  const hideValue = `${column.id}-hide`;

  return (
    <div className={cn("flex items-center gap-2", className)}>
      <Select
        value={
          column.getIsSorted() === "desc"
            ? descValue
            : column.getIsSorted() === "asc"
              ? ascValue
              : noneValue
        }
        onValueChange={(value) => {
          if (value === ascValue) column.toggleSorting(false);
          else if (value === descValue) column.toggleSorting(true);
          else if (value === hideValue) column.toggleVisibility(false);
          else if (value === noneValue) column.clearSorting();
        }}
      >
        <SelectTrigger
          aria-label={
            column.getIsSorted() === "desc"
              ? "Sorted descending. Click to sort ascending."
              : column.getIsSorted() === "asc"
                ? "Sorted ascending. Click to sort descending."
                : "Not sorted. Click to sort ascending."
          }
          className="-ml-3 h-8 w-fit border-none text-xs hover:bg-accent hover:text-accent-foreground data-[state=open]:bg-accent [&>svg:last-child]:hidden"
        >
          {column.columnDef.meta?.title || column.id}
          <SelectIcon asChild>
            {column.getCanSort() && column.getIsSorted() === "desc" ? (
              <ArrowDown className="ml-2.5 size-4" aria-hidden="true" />
            ) : column.getIsSorted() === "asc" ? (
              <ArrowUp className="ml-2.5 size-4" aria-hidden="true" />
            ) : (
              <ChevronsUpDown className="ml-2.5 size-4" aria-hidden="true" />
            )}
          </SelectIcon>
        </SelectTrigger>
        <SelectContent align="start">
          {column.getCanSort() && (
            <>
              <SelectItem value={ascValue}>
                <span className="flex items-center">
                  <ArrowUp
                    className="mr-2 size-3.5 text-muted-foreground/70"
                    aria-hidden="true"
                  />
                  Asc
                </span>
              </SelectItem>
              <SelectItem value={descValue}>
                <span className="flex items-center">
                  <ArrowDown
                    className="mr-2 size-3.5 text-muted-foreground/70"
                    aria-hidden="true"
                  />
                  Desc
                </span>
              </SelectItem>
              <SelectItem value={noneValue}>
                <span className="flex items-center gap-2">
                  <X
                    className="size-3.5 text-muted-foreground/70"
                    aria-hidden="true"
                  />
                  Reset
                </span>
              </SelectItem>
            </>
          )}
          {column.getCanHide() && (
            <SelectItem value={hideValue}>
              <span className="flex items-center">
                <EyeOff
                  className="mr-2 size-3.5 text-muted-foreground/70"
                  aria-hidden="true"
                />
                Hide
              </span>
            </SelectItem>
          )}
        </SelectContent>
      </Select>
    </div>
  );
}
