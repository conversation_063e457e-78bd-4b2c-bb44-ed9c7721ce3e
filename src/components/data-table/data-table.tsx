/**
 * @fileoverview 数据表格的主要组件，基于TanStack Table库实现，提供灵活的数据展示功能
 */

import {flexRender, type Table as TanstackTable} from "@tanstack/react-table";
import * as React from "react";
import {useEffect, useRef} from "react";

import {DataTablePagination} from "@/components/data-table/data-table-pagination";
import {Table, TableBody, TableCell, TableHead, TableHeader, TableRow,} from "@/components/ui/table";
import {getCommonPinningStyles} from "@/components/data-table/lib/data-table";
import {cn} from "@/lib/utils";

/**
 * 数据表格组件的属性接口
 * @template TData 表格数据类型
 * @extends React.HTMLAttributes<HTMLDivElement> 继承div元素的HTML属性
 */
interface DataTableProps<TData> extends React.HTMLAttributes<HTMLDivElement> {
  /**
   * The table instance returned from useDataTable hook with pagination, sorting, filtering, etc.
   * @type TanstackTable<TData>
   */
  table: TanstackTable<TData>;

  /**
   * The floating bar to render at the bottom of the table on row selection.
   * @default null
   * @type React.ReactNode | null
   * @example floatingBar={<TasksTableFloatingBar table={table} />}
   */
  floatingBar?: React.ReactNode | null;
  showPagination?: boolean;
  offsetBottom?: number;
}

/**
 * 数据表格组件
 * @template TData 表格数据类型
 * @param showPagination
 * @param offsetBottom
 * @param props 组件属性
 * @param props.table TanStack Table实例，包含表格的所有状态和行为
 * @param props.floatingBar 可选的浮动工具栏，在选择行时显示
 * @param props.children 可选的子元素，通常用于放置工具栏等组件
 * @param props.className 自定义CSS类名
 * @returns 渲染的数据表格组件
 */


export function DataTable<TData>({
  table,
  floatingBar = null,
  children,
  className,
  showPagination = true,
  offsetBottom = 30,
  ...props
}: DataTableProps<TData>) {
  const containerRef = useRef<HTMLDivElement>(null);
  const tableWrapperRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const updateHeight = () => {
      if (containerRef.current && tableWrapperRef.current) {
        const containerRect = containerRef.current.getBoundingClientRect();
        const windowHeight = window.innerHeight;
        const topOffset = containerRect.top;
        const paginationHeight = 140;
        const availableHeight = windowHeight - topOffset - paginationHeight - offsetBottom;
        tableWrapperRef.current.style.height = `${Math.max(availableHeight, 150)}px`;
      }
    };

    updateHeight();
    window.addEventListener('resize', updateHeight);
    return () => window.removeEventListener('resize', updateHeight);
  }, [showPagination]);

  return (
    <div
      ref={containerRef}
      className={cn("w-full space-y-2.5 overflow-auto", className)}
      {...props}
    >
      {children}
      <div className="rounded-md border">
        <div ref={tableWrapperRef} className="overflow-auto">
          <Table className="[&_td]:border-border [&_th]:border-border border-separate border-spacing-0 [&_tfoot_td]:border-t [&_th]:border-b [&_tr]:border-none [&_tr:not(:last-child)_td]:border-b">
            <TableHeader className="bg-background/90 sticky top-0 z-10 backdrop-blur-xs">
              {table.getHeaderGroups().map((headerGroup) => (
                <TableRow className="hover:bg-transparent" key={headerGroup.id}>
                  {headerGroup.headers.filter(header => header.column.columnDef.meta?.expend !== true).map((header) => {
                    return (
                      <TableHead
                        key={header.id}
                        colSpan={header.colSpan}
                        style={{
                          ...getCommonPinningStyles({ column: header.column }),
                        }}
                      >
                        {header.isPlaceholder
                          ? null
                          : flexRender(
                            header.column.columnDef.header,
                            header.getContext(),
                          )}
                      </TableHead>
                    );
                  })}
                </TableRow>
              ))}
            </TableHeader>
            <TableBody >
              {table.getRowModel().rows?.length ? (
                table.getRowModel().rows.map((row) => (
                  <React.Fragment key={row.id}>
                    <TableRow
                      data-state={row.getIsSelected() && "selected"}
                    >
                      {
                        row.getVisibleCells().filter(cell => (cell.column.columnDef.meta?.expend !== true)).map((cell) => (
                          <TableCell
                          key={cell.id}
                          style={{
                            ...getCommonPinningStyles({ column: cell.column }),
                          }}
                        >
                          {flexRender(
                            cell.column.columnDef.cell,
                            cell.getContext(),
                          )}
                        </TableCell>
                      ))}
                    </TableRow>
                    {/* 展开行内容 */}
                    {row.getIsExpanded() && row.getVisibleCells()
                      .filter(cell => cell.column.columnDef.meta?.expend)
                      .map((cell) =>
                        <TableRow  key={`${row.id}_${cell.id}`}>
                          <TableCell
                            key={`${row.id}_${cell.id}`}
                            colSpan={row.getVisibleCells().length}
                          >
                            {flexRender(
                            cell.column.columnDef.cell,
                            cell.getContext()
                            )}
                          </TableCell>
                        </TableRow>

                      )
                    }



                  </React.Fragment>
                ))
              ) : (
                <TableRow>
                  <TableCell
                    colSpan={table.getAllColumns().length}
                    className="text-center"
                  >
                    空
                  </TableCell>
                </TableRow>
              )}

            </TableBody>
          </Table>
        </div>
      </div>
      {showPagination ? (
        <div className="flex flex-col gap-2.5">
          <DataTablePagination table={table} />
          {table.getFilteredSelectedRowModel().rows.length > 0 && floatingBar}
        </div>
      ) : table.getFilteredSelectedRowModel().rows.length > 0 && (
        <div className="flex flex-col gap-2.5">
          {floatingBar}
        </div>
      )}
    </div>
  );
}
