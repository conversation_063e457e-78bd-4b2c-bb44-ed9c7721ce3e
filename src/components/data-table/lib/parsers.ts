import type {ExtendedSortingState} from "@/types";
import type {Row} from "@tanstack/react-table";
import {createParser} from "nuqs/server";
import {z} from "zod";
import {type Parser} from "nuqs";
import type {DateRange} from "react-day-picker";
import {formatDateTimeForUrl, parseDateTimeFromUrl} from "@/lib/date";

export const sortingItemSchema = z.object({
  id: z.string(),
  desc: z.boolean(),
});

/**
 * Creates a parser for TanStack Table sorting state.
 * @param originalRow The original row data to validate sorting keys against.
 * @returns A parser for TanStack Table sorting state.
 */
export const getSortingStateParser = <TData>(
  originalRow?: Row<TData>["original"],
) => {
  const validKeys = originalRow ? new Set(Object.keys(originalRow)) : null;

  return createParser<ExtendedSortingState<TData>>({
    parse: (value) => {
      try {
        const parsed = JSON.parse(value);
        const result = z.array(sortingItemSchema).safeParse(parsed);

        if (!result.success) return null;

        if (validKeys && result.data.some((item) => !validKeys.has(item.id))) {
          return null;
        }

        return result.data as ExtendedSortingState<TData>;
      } catch {
        return null;
      }
    },
    serialize: (value) => JSON.stringify(value),
    eq: (a, b) =>
      a.length === b.length &&
      a.every(
        (item, index) =>
          item.id === b[index]?.id && item.desc === b[index]?.desc,
      ),
  });
};

/**
 * 创建日期时间范围解析器
 */
export function getDateRangeParser(): Parser<DateRange | undefined> {
  return {
    parse: (str: string | null): DateRange | undefined => {
      if (!str) return undefined;
      const [fromStr, toStr] = str.split(',');
      if (!fromStr || !toStr) return undefined;
      
      try {
        return {
          from: parseDateTimeFromUrl(fromStr),
          to: parseDateTimeFromUrl(toStr)
        };
      } catch {
        return undefined;
      }
    },
    serialize: (value: DateRange | undefined): string => {
      if (!value?.from || !value?.to) return '';
      return `${formatDateTimeForUrl(value.from)},${formatDateTimeForUrl(value.to)}`;
    }
  };
}