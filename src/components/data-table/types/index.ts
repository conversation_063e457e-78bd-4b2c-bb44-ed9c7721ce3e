/**
 * @fileoverview 数据表格相关的类型定义
 */

import type {ColumnSort, Row} from "@tanstack/react-table";
import React from "react";
import {CommaKeyOf} from "@/types";

/**
 * 从数据类型中提取字符串类型的键
 */
export type StringKeyOf<TData> = Extract<keyof TData, string>;

/**
 * 基础选项类型
 */
export interface Option {
  /** 选项显示标签 */
  label: string;
  /** 选项值 */
  value: string;
  /** 选项图标组件 */
  icon?: React.ComponentType<{ className?: string }>;
  /** 选项计数 */
  count?: number;

}

/**
 * 扩展的列排序类型
 * 继承自tanstack-table的ColumnSort，但使用类型安全的id
 */
export interface ExtendedColumnSort<TData> extends Omit<ColumnSort, "id"> {
  id: StringKeyOf<TData>;
}

/**
 * 扩展的排序状态类型
 * 使用类型安全的ExtendedColumnSort
 */
export type ExtendedSortingState<TData> = ExtendedColumnSort<TData>[];

/**
 * 行操作类型
 */
export interface DataTableRowAction<TData> {
  /** 操作的行数据 */
  row: Row<TData>;
  /** 操作类型：更新或删除 */
  type: "update" | "delete" | "auth" | "url";
}

export type RangeType = 'date' | 'datetime' | 'number' 

/**
 * 数据表格过滤字段类型
 */
export interface DataTableFilterField<TData> {
  /** 
   * 字段ID
   * 如果包含逗号(如 'title,content')则使用 multi_match 查询
   */
  id: CommaKeyOf<TData>;
  /** 字段显示标签 */
  label: string;
  /** 搜索框占位符 */
  placeholder?: string;
  /** 
   * 过滤选项列表
   * 有options说明是terms查询（分面过滤）
   */
  options?: Option[];
  /**
   * 是否为范围查询
   * range为true时使用范围查询
   * 值会被解析为 [start, end] 数组
   * 例如：日期范围、数值范围等
   */
  range?: RangeType;
  /**
   * 是否为聚合查询
   * agg为true时使用聚合查询
   */
  agg?: boolean;

  virtual?: boolean;
  hidden?: boolean;

  highlight?: boolean;
}
