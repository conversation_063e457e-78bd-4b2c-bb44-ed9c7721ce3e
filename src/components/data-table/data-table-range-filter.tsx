/**
 * @fileoverview 数据表格的范围过滤器组件，提供日期、时间和数值范围过滤功能
 */

import type {Column} from "@tanstack/react-table";
import {PlusCircle} from "lucide-react";
import * as React from "react";
import {format} from "date-fns";
import {zhCN} from "date-fns/locale";

import {Button} from "@/components/ui/button";
import {Popover, PopoverContent, PopoverTrigger,} from "@/components/ui/popover";
import {Separator} from "@/components/ui/separator";
import {Badge} from "@/components/ui/badge";
import {Input} from "@/components/ui/input";
import {Label} from "@/components/ui/label";
import {RangeType} from "@/components/data-table/types";
import {DatePickerWithRange,} from "@/components/ui/date-range-picker";

/**
 * 范围过滤器组件的属性接口
 */
interface DataTableRangeFilterProps<TData, TValue> {
  /** 要过滤的列实例 */
  column?: Column<TData, TValue>;
  /** 过滤器标题 */
  title?: string;
  /** 范围类型：日期、日期时间或数字 */
  type: RangeType;
}

/**
 * 数据表格范围过滤器组件
 */
export function DataTableRangeFilter<TData, TValue>({
  column,
  title,
  type,
}: DataTableRangeFilterProps<TData, TValue>) {
  // 获取当前过滤值
  const columnFilterValue = column?.getFilterValue() as [string, string] | undefined;
  
  // 根据类型格式化显示值
  const formatValue = (value: string) => {
    if (!value) return "";
    
    switch (type) {
      case "date":
        return format(new Date(value), "yyyy-MM-dd", { locale: zhCN });
      case "datetime":
        return format(new Date(value), "yyyy-MM-dd HH:mm", { locale: zhCN });
      case "number":
        return value;
      default:
        return value;
    }
  };

  // 更新过滤值
  const updateFilterValue = (index: 0 | 1, value: string) => {
    const newValue: [string, string] = [...(columnFilterValue || ["", ""])] as [string, string];
    newValue[index] = value;
    column?.setFilterValue(newValue[0] || newValue[1] ? newValue : undefined);
  };

  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button variant="outline" size="sm" className="h-8 border-dashed">
          <PlusCircle className="mr-2 h-4 w-4" />
          {title}
          {columnFilterValue?.some(Boolean) && (
            <>
              <Separator orientation="vertical" className="mx-2 h-4" />
              <Badge variant="secondary" className="rounded-sm px-1 font-normal">
                {formatValue(columnFilterValue[0])} ~ {formatValue(columnFilterValue[1])}
              </Badge>
            </>
          )}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-auto p-4" align="start">
        <div className="space-y-4">
          {(type === "date" || type === "datetime") && (
            <DatePickerWithRange
              value={{
                from: columnFilterValue?.[0] ? new Date(columnFilterValue[0]) : undefined,
                to: columnFilterValue?.[1] ? new Date(columnFilterValue[1]) : undefined
              }}
              onChange={(range) => {
                const newValue: [string, string] = [
                  range?.from?.toISOString() || "",
                  range?.to?.toISOString() || ""
                ];
                column?.setFilterValue(newValue[0] || newValue[1] ? newValue : undefined);
              }}
              showTimePicker={type === "datetime"}
            />
          )}
          
          {type === "number" && (
            <div className="flex items-center gap-2">
              <div className="grid gap-2">
                <Label>最小值</Label>
                <Input
                  type="number"
                  value={columnFilterValue?.[0] ?? ""}
                  onChange={(e) => updateFilterValue(0, e.target.value)}
                  placeholder="最小值"
                  className="h-8 w-[100px]"
                />
              </div>
              <div className="py-2">~</div>
              <div className="grid gap-2">
                <Label>最大值</Label>
                <Input
                  type="number"
                  value={columnFilterValue?.[1] ?? ""}
                  onChange={(e) => updateFilterValue(1, e.target.value)}
                  placeholder="最大值"
                  className="h-8 w-[100px]"
                />
              </div>
            </div>
          )}
          
          {columnFilterValue?.some(Boolean) && (
            <Button
              variant="ghost"
              className="w-full"
              onClick={() => column?.setFilterValue(undefined)}
            >
              重置
            </Button>
          )}
        </div>
      </PopoverContent>
    </Popover>
  );
} 