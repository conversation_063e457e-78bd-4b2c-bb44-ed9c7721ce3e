/**
 * @fileoverview 数据表格的核心Hook，提供完整的表格状态管理和URL同步功能
 */

import type {DataTableFilterField, ExtendedSortingState} from "@/components/data-table/types";
import {
    type ColumnDef,
    type ColumnFiltersState,
    getCoreRowModel,
    getFacetedRowModel,
    getFacetedUniqueValues,
    getFilteredRowModel,
    getPaginationRowModel,
    getSortedRowModel,
    type PaginationState,
    type RowSelectionState,
    type SortingState,
    type TableOptions,
    type TableState,
    type Updater,
    useReactTable,
    type VisibilityState,
} from "@tanstack/react-table";
import {
    parseAsArrayOf,
    parseAsInteger,
    parseAsString,
    type Parser,
    useQueryState,
    type UseQueryStateOptions,
    useQueryStates,
} from "nuqs";
import * as React from "react";
import {useEffect} from "react";

import {useDebouncedCallback} from "@/hooks/use-debounced-callback";
import {getSortingStateParser} from "@/components/data-table/lib/parsers";

/**
 * 数据表格Hook的属性接口
 * @template TData 表格数据类型
 */
interface UseDataTableProps<TData>
  extends Omit<
      TableOptions<TData>,
      | "state"
      | "pageCount"
      | "getCoreRowModel"
      | "manualFiltering"
      | "manualPagination"
      | "manualSorting"
    >,
    Required<Pick<TableOptions<TData>, "pageCount">> {
  /**
   * 定义表格的过滤字段。支持动态分面过滤和搜索过滤。
   * - 当提供options时，渲染为分面过滤器
   * - 否则渲染为搜索过滤器
   */
  filterFields?: DataTableFilterField<TData>[];

  /**
   * 决定查询更新如何影响历史记录
   * push: 创建新的历史记录
   * replace: 替换当前历史记录
   * @default "replace"
   */
  history?: "push" | "replace";

  /**
   * URL变化时是否滚动到页面顶部
   * @default false
   */
  scroll?: boolean;

  /**
   * 浅层模式下查询状态保持在客户端
   * 设为false时会触发带有更新后查询字符串的网络请求
   * @default true
   */
  shallow?: boolean;

  /**
   * URL查询字符串更新之间的最大等待时间(ms)
   * 用于浏览器限流，最小有效值为50ms
   * @default 50
   */
  throttleMs?: number;

  /**
   * 过滤器更新的防抖时间(ms)
   * 用于提高快速输入时的性能
   * @default 300
   */
  debounceMs?: number;

  /**
   * 观察非浅层更新的Server Component加载状态
   * 传入React.useTransition()的startTransition
   * 会自动将shallow设为false
   * shallow: true和startTransition不能同时使用
   */
  startTransition?: React.TransitionStartFunction;

  /**
   * 当状态设置为默认值时清除URL查询键值对
   * 保持URL含义在默认值变化时保持一致
   * @default false
   */
  clearOnDefault?: boolean;

  /**
   * 表格的初始状态
   */
  initialState?: Omit<Partial<TableState>, "sorting"> & {
    sorting?: ExtendedSortingState<TData>;
  };
}

/**
 * 数据表格Hook，提供完整的表格状态管理和URL同步功能
 * @template TData 表格数据类型
 */
export function useDataTable<TData>({
  pageCount = -1,
  filterFields = [],
  history = "replace",
  scroll = false,
  shallow = true,
  throttleMs = 500,
  debounceMs = 300,
  clearOnDefault = false,
  startTransition,
  initialState,
  ...props
}: UseDataTableProps<TData>) {
  // 创建查询状态选项
  const queryStateOptions = React.useMemo<
    Omit<UseQueryStateOptions<string>, "parse">
  >(() => {
    return {
      history,
      scroll,
      shallow,
      throttleMs,
      debounceMs,
      clearOnDefault,
      startTransition,
    };
  }, [
    history,
    scroll,
    shallow,
    throttleMs,
    debounceMs,
    clearOnDefault,
    startTransition,
  ]);

  // 行选择状态
  const [rowSelection, setRowSelection] = React.useState<RowSelectionState>(
    initialState?.rowSelection ?? {},
  );
  
  // 创建虚拟列
  const { virtualColumns, allColumns } = React.useMemo(() => {
    const virtualColumns = filterFields
      .filter(field => field.virtual)
      .map(field => ({
        id: field.id,
        enableSorting: false,
        enableColumnFilter: true,
        enableHiding: false,
      } satisfies ColumnDef<TData>));
    
    return {
      virtualColumns,
      allColumns: [...props.columns, ...virtualColumns]
    };
  }, [props.columns, filterFields]);

  // 初始化列可见性状态，虚拟列默认隐藏
  const defaultColumnVisibility = React.useMemo(() => {
    const visibility = {...(initialState?.columnVisibility ?? {})};
    virtualColumns.forEach(column => {
      visibility[column.id] = false;
    });
    return visibility;
  }, [initialState?.columnVisibility, virtualColumns]);

  // 列可见性状态
  const [columnVisibility, setColumnVisibility] =
    React.useState<VisibilityState>(defaultColumnVisibility);

  // 分页状态 - 与URL同步
  const [page, setPage] = useQueryState(
    "page",
    parseAsInteger.withOptions(queryStateOptions).withDefault(1),
  );
  const [perPage, setPerPage] = useQueryState(
    "perPage",
    parseAsInteger
      .withOptions(queryStateOptions)
      .withDefault(initialState?.pagination?.pageSize ?? 10),
  );

  // 排序状态 - 与URL同步
  const [sorting, setSorting] = useQueryState(
    "sort",
    getSortingStateParser<TData>()
      .withOptions(queryStateOptions)
      .withDefault(initialState?.sorting ?? []),
  );

  // 为每个过滤字段创建解析器
  const filterParsers = React.useMemo(() => {
    return filterFields.reduce<
      Record<string, Parser<string> | Parser<string[]>>
    >((acc, field) => {
      if (field.options) {
        // 分面过滤器使用数组解析器
        acc[field.id] = parseAsArrayOf(parseAsString, ",").withOptions(
          queryStateOptions,
        );
      } else if (field.range) {
        // 范围过滤器使用数组解析器，存储 [start, end]
        acc[field.id] = parseAsArrayOf(parseAsString, ",").withOptions(
          queryStateOptions,
        );
      } else {
        // 搜索过滤器使用字符串解析器
        acc[field.id] = parseAsString.withOptions(queryStateOptions);
      }
      return acc;
    }, {});
  }, [filterFields, queryStateOptions]);

  // 过滤值状态 - 与URL同步
  const [filterValues, setFilterValues] = useQueryStates(filterParsers);

  // 防抖处理过滤值更新
  const debouncedSetFilterValues = useDebouncedCallback(
    (values: typeof filterValues) => {
      void setPage(1);
      void setFilterValues(values);
    },
    debounceMs,
  );

  // 分页状态对象
  const pagination: PaginationState = {
    pageIndex: page - 1, // 转换为从0开始的索引
    pageSize: perPage,
  };

  // 处理分页变更
  function onPaginationChange(updaterOrValue: Updater<PaginationState>) {
    if (typeof updaterOrValue === "function") {
      const newPagination = updaterOrValue(pagination);
      void setPage(newPagination.pageIndex + 1);
      void setPerPage(newPagination.pageSize);
    } else {
      void setPage(updaterOrValue.pageIndex + 1);
      void setPerPage(updaterOrValue.pageSize);
    }
  }

  // 处理排序变更
  function onSortingChange(updaterOrValue: Updater<SortingState>) {
    if (typeof updaterOrValue === "function") {
      const newSorting = updaterOrValue(sorting) as ExtendedSortingState<TData>;
      void setSorting(newSorting);
    }
  }

  // 初始化列过滤器状态
  const initialColumnFilters: ColumnFiltersState = React.useMemo(() => {
    return Object.entries(filterValues).reduce<ColumnFiltersState>(
        (filters, [key, value]) => {
          if (value !== null) {
            const field = filterFields.find(f => f.id === key);
            filters.push({
              id: key,
              value: field?.options || field?.range ? (Array.isArray(value) ? value : [value]) : value,
            });
          }
          return filters;
        },
        [],
    );
  }, [filterValues]);

  useEffect(() => {
    setColumnFilters(initialColumnFilters);
  },[initialColumnFilters]);

  // 列过滤器状态
  const [columnFilters, setColumnFilters] =
    React.useState<ColumnFiltersState>(initialColumnFilters);

  // 缓存可搜索列和可过滤列的计算结果
  const { searchableColumns, filterableColumns, rangeColumns } = React.useMemo(() => {
    return {
      searchableColumns: filterFields.filter((field) => !field.options && !field.range),
      filterableColumns: filterFields.filter((field) => field.options),
      rangeColumns: filterFields.filter((field) => field.range),
    };
  }, [filterFields]);

  // 处理列过滤器变更
  const onColumnFiltersChange = React.useCallback(
    (updaterOrValue: Updater<ColumnFiltersState>) => {
      setColumnFilters((prev) => {
        const next =
          typeof updaterOrValue === "function"
            ? updaterOrValue(prev)
            : updaterOrValue;

        const filterUpdates = next.reduce<
          Record<string, string | string[] | null>
        >((acc, filter) => {
          if (searchableColumns.find((col) => col.id === filter.id)) {
            // 搜索过滤器直接使用值
            acc[filter.id] = filter.value as string;
          } else if (filterableColumns.find((col) => col.id === filter.id)) {
            // 分面过滤器使用值数组
            acc[filter.id] = filter.value as string[];
          }else if (rangeColumns.find((col) => col.id === filter.id)) {
            // 范围过滤器使用值数组
            acc[filter.id] = filter.value as [string, string];
          }
          return acc;
        }, {});

        // 清除已删除的过滤器
        for (const prevFilter of prev) {
          if (!next.some((filter) => filter.id === prevFilter.id)) {
            filterUpdates[prevFilter.id] = null;
          }
        }

        debouncedSetFilterValues(filterUpdates);
        return next;
      });
    },
    [
      debouncedSetFilterValues,
      filterableColumns,
      searchableColumns,
      rangeColumns,
      columnFilters,
    ],
  );

  // 创建表格实例
  const table = useReactTable({
    ...props,
    columns: allColumns,
    initialState,
    pageCount,
    state: {
      pagination,
      sorting,
      columnVisibility,
      rowSelection,
      columnFilters: columnFilters,
    },
    enableRowSelection: true,
    onRowSelectionChange: setRowSelection,
    onPaginationChange,
    onSortingChange,
    onColumnFiltersChange,
    onColumnVisibilityChange: setColumnVisibility,
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFacetedRowModel: getFacetedRowModel(),
    getFacetedUniqueValues: getFacetedUniqueValues(),
    manualPagination: true,
    manualSorting: true,
    manualFiltering: true,
  });

  return { table };
}
