"use client"

import {ChevronRight, type LucideIcon} from "lucide-react"
import {Link, useLocation} from 'umi';

import {Collapsible, CollapsibleContent, CollapsibleTrigger,} from "@/components/ui/collapsible"
import {
    SidebarGroup,
    SidebarGroupLabel,
    SidebarMenu,
    SidebarMenuButton,
    SidebarMenuItem,
    SidebarMenuSub,
    SidebarMenuSubButton,
    SidebarMenuSubItem,
} from "@/components/ui/sidebar"
import {useCallback} from "react";

// 定义导航菜单子项类型
export interface NavMainSubItemType {
  title: string
  url: string
}

// 定义导航菜单项类型
export interface NavMainItemType {
  title: string
  url: string
  icon?: LucideIcon
  isActive?: boolean
  items?: NavMainSubItemType[]
}

export function NavMain({
  items,
}: {
  items: NavMainItemType[]
}) {
  const location = useLocation();

  // 检查当前路由是否匹配菜单项
  const isActive = useCallback((url: string) => {
    // 规范化路径，移除开头和结尾的斜杠
    const normalizePath = (path: string) => path.replace(/^\/+|\/+$/g, '');
    const currentPath = normalizePath(location.pathname);
    const menuPath = normalizePath(url);

    // 如果当前路径和菜单路径完全相等，则匹配
    if (currentPath === menuPath) {
      return true;
    }

    // 如果当前路径以菜单路径开头，并且下一个字符是斜杠，则匹配
    // 例如：/settings/add 匹配 /settings，但 /settingsadd 不匹配
    return currentPath.startsWith(menuPath + '/');
  },[location.pathname]);

  return (
    <SidebarGroup>
      <SidebarGroupLabel>工作区项目</SidebarGroupLabel>
      <SidebarMenu>
        {items.map((item) => {
          return (
            <Collapsible
              key={item.title}
              asChild
              defaultOpen={isActive(item.url)}
              className="group/collapsible"
            >
              <SidebarMenuItem>
                <CollapsibleTrigger asChild>
                  <Link to={item.url}>
                    <SidebarMenuButton 
                      tooltip={item.title}
                      className={isActive(item.url) ? 'bg-accent text-accent-foreground' : ''}
                    >
                      {item.icon && <item.icon />}
                      <span>{item.title}</span>
                      {item.items && item.items.length > 0 && (
                        <ChevronRight className="ml-auto transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90" />
                      )}
                    </SidebarMenuButton>
                  </Link>
                </CollapsibleTrigger>
                {item.items && item.items.length > 0 && (
                  <CollapsibleContent>
                    <SidebarMenuSub>
                      {item.items.map((subItem) => {
                        console.log('渲染子菜单项:', subItem.title, subItem.url);
                        return (
                          <SidebarMenuSubItem key={subItem.title}>
                            <Link to={subItem.url}>
                              <SidebarMenuSubButton className={isActive(subItem.url) ? 'bg-accent text-accent-foreground' : ''}>
                                <span>{subItem.title}</span>
                              </SidebarMenuSubButton>
                            </Link>
                          </SidebarMenuSubItem>
                        );
                      })}
                    </SidebarMenuSub>
                  </CollapsibleContent>
                )}
              </SidebarMenuItem>
            </Collapsible>
          );
        })}
      </SidebarMenu>
    </SidebarGroup>
  )
}
