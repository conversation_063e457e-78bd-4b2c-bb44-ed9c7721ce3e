import * as React from "react"
import {ChevronsUpDown, Plus, Star} from "lucide-react"
import {useModel} from 'umi';


import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuLabel,
    DropdownMenuSeparator,
    DropdownMenuShortcut,
    DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {SidebarMenu, SidebarMenuButton, SidebarMenuItem, useSidebar,} from "@/components/ui/sidebar"
import {NavMainItemType} from "./nav-main"

// 定义工作空间类型
export interface WorkspaceType {
  name: string
  code: string
  logo: React.ElementType
  type: string
  navItems: NavMainItemType[]
}

export function WorkspaceSwitcher({
  workspaces,
  activeWorkspace,
  onChange,
}: {
  workspaces: WorkspaceType[]
  activeWorkspace?: WorkspaceType
  onChange?: (workspace: WorkspaceType) => void
}) {
  const { isMobile } = useSidebar()
  const [selectedWorkspace, setSelectedWorkspace] = React.useState(activeWorkspace || workspaces[0])
  // 获取用户model
  const userModel = useModel('user');

  React.useEffect(() => {
    if (activeWorkspace) {
      setSelectedWorkspace(activeWorkspace)
    }
  }, [activeWorkspace])

  if (!selectedWorkspace) {
    return null
  }

  const handleWorkspaceChange = (workspace: WorkspaceType) => {
    console.log('WorkspaceSwitcher 切换到:', workspace.name);
    setSelectedWorkspace(workspace)
    if (onChange) {
      onChange(workspace)
    }
  }
  
  // 处理星标点击
  const handleStarClick = (e: React.MouseEvent, workspace: WorkspaceType) => {
    e.stopPropagation(); // 阻止事件冒泡，避免触发DropdownMenuItem的点击
    userModel.setPreferredWorkspace(workspace.code);
  }

  // 判断是否为首选工作区
  const isPreferred = (workspace: WorkspaceType) => 
    userModel.preferences.workspace === workspace.code;
    
  // 星标组件，独立处理点击事件
  const StarIcon = ({ workspace }: { workspace: WorkspaceType }) => {
    const preferred = isPreferred(workspace);
    return (
      <div 
        onClick={(e) => {
          e.stopPropagation();
          e.preventDefault();
          handleStarClick(e, workspace);
        }}
        className="flex items-center justify-center p-1"
      >
        <Star 
          className={`h-4 w-4 ${preferred ? 'text-yellow-400' : 'text-muted-foreground opacity-30 hover:opacity-100'}`} 
        />
      </div>
    );
  };

  return (
    <SidebarMenu>
      <SidebarMenuItem>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <SidebarMenuButton
              size="lg"
              className="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
            >
              <div className="bg-sidebar-primary text-sidebar-primary-foreground flex aspect-square size-8 items-center justify-center rounded-lg">
                <selectedWorkspace.logo className="size-4" />
              </div>
              <div className="grid flex-1 text-left text-sm leading-tight">
                <span className="truncate font-medium">{selectedWorkspace.name}</span>
                <span className="truncate text-xs">{selectedWorkspace.type}</span>
              </div>
              {isPreferred(selectedWorkspace) && (
                <Star className="mr-1 h-3.5 w-3.5 text-yellow-400" />
              )}
              <ChevronsUpDown className="ml-auto" />
            </SidebarMenuButton>
          </DropdownMenuTrigger>
          <DropdownMenuContent
            className="w-(--radix-dropdown-menu-trigger-width) min-w-56 rounded-lg"
            align="start"
            side={isMobile ? "bottom" : "right"}
            sideOffset={4}
          >
            <DropdownMenuLabel className="text-muted-foreground text-xs">
              工作空间
            </DropdownMenuLabel>
            {workspaces.map((workspace, index) => (
              <DropdownMenuItem
                key={workspace.name}
                onClick={() => handleWorkspaceChange(workspace)}
                className="gap-2 p-2"
              >
                <div className="flex size-6 items-center justify-center rounded-md border">
                  <workspace.logo className="size-3.5 shrink-0" />
                </div>
                {workspace.name}
                <div className="ml-auto flex items-center">
                  <StarIcon workspace={workspace} />
                  <DropdownMenuShortcut>⌘{index + 1}</DropdownMenuShortcut>
                </div>
              </DropdownMenuItem>
            ))}
            <DropdownMenuSeparator />
            <DropdownMenuItem className="gap-2 p-2">
              <div className="flex size-6 items-center justify-center rounded-md border bg-transparent">
                <Plus className="size-4" />
              </div>
              <div className="text-muted-foreground font-medium">添加工作空间</div>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </SidebarMenuItem>
    </SidebarMenu>
  )
}
