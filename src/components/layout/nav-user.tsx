"use client"

import {Ch<PERSON><PERSON>UpDown, LayoutDashboard, <PERSON>gOut, Star,} from "lucide-react"
import {useModel} from "umi"

import {Avatar, AvatarFallback,} from "@/components/ui/avatar"
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuGroup,
    DropdownMenuItem,
    DropdownMenuLabel,
    DropdownMenuSeparator,
    DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {SidebarMenu, SidebarMenuButton, SidebarMenuItem, useSidebar,} from "@/components/ui/sidebar"


export interface UserType {
  name: string;
  email: string;
  avatar?: string;
  roles: string[];
  permissions: string[];
  preferences?: {
    theme: 'DARK' | 'LIGHT' | 'SYSTEM';
    workspace: string;
  };
}
export function NavUser() {
  const { isMobile } = useSidebar()
  
  // 获取工作空间和用户模型
  const workspaceModel = useModel('workspace')
  const userModel = useModel('user')
  
  // 设置当前工作区为偏好工作区
  const setCurrentAsPreferred = () => {
    if (workspaceModel.activeWorkspace) {
      userModel.setPreferredWorkspace(workspaceModel.activeWorkspace.code);
    }
  }
  
  // 切换到偏好工作区
  const switchToPreferred = () => {
    const preferredWorkspace = userModel.preferences?.workspace;
    if (preferredWorkspace) {
      const workspace = workspaceModel.workspaces.find(
        w => w.code === preferredWorkspace
      );
      if (workspace) {
        workspaceModel.switchWorkspace(workspace);
      }
    }
  }

  return (
    <SidebarMenu>
      <SidebarMenuItem>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <SidebarMenuButton
              size="lg"
              className="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
            >
              <Avatar className="h-8 w-8 rounded-lg">
                <AvatarFallback className="rounded-lg">{userModel.user.name.slice(0, 1)}</AvatarFallback>
              </Avatar>
              <div className="grid flex-1 text-left text-sm leading-tight">
                <span className="truncate font-medium">{userModel.user.name}</span>
              </div>
              <ChevronsUpDown className="ml-auto size-4" />
            </SidebarMenuButton>
          </DropdownMenuTrigger>
          <DropdownMenuContent
            className="w-(--radix-dropdown-menu-trigger-width) min-w-56 rounded-lg"
            side={isMobile ? "bottom" : "right"}
            align="end"
            sideOffset={4}
          >
            <DropdownMenuLabel className="p-0 font-normal">
              <div className="flex items-center gap-2 px-1 py-1.5 text-left text-sm">
                <Avatar className="h-8 w-8 rounded-lg">
                  <AvatarFallback className="rounded-lg">{userModel.user.name.slice(0, 2)}</AvatarFallback>
                </Avatar>
                <div className="grid flex-1 text-left text-sm leading-tight">
                  <span className="truncate font-medium">{userModel.user.name}</span>
                  <span className="truncate text-xs">{userModel.user.email}</span>
                </div>
              </div>
            </DropdownMenuLabel>
            <DropdownMenuSeparator />
            <DropdownMenuGroup>
              <DropdownMenuItem onClick={setCurrentAsPreferred}>
                <Star className="mr-2 h-4 w-4" />
                <span>设为默认工作区</span>
              </DropdownMenuItem>
              <DropdownMenuItem onClick={switchToPreferred} disabled={!userModel.preferences?.workspace}>
                <LayoutDashboard className="mr-2 h-4 w-4" />
                <span>切换到默认工作区</span>
              </DropdownMenuItem>
            </DropdownMenuGroup>
            <DropdownMenuSeparator />
            {/*<DropdownMenuGroup>*/}
            {/*  <DropdownMenuItem>*/}
            {/*    <Settings className="mr-2 h-4 w-4" />*/}
            {/*    /!*<span>账号设置</span>*!/*/}
            {/*  </DropdownMenuItem>*/}
            {/*</DropdownMenuGroup>*/}
            <DropdownMenuSeparator />
            <DropdownMenuItem onClick={userModel.logout}>
              <LogOut className="mr-2 h-4 w-4" />
              <span>退出登录</span>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </SidebarMenuItem>
    </SidebarMenu>
  )
}
