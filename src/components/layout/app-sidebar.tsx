import * as React from "react"
import {WorkspaceSwitcher, WorkspaceType} from "@/components/layout/team-switcher"
import {Sidebar, <PERSON>bar<PERSON>ontent, SidebarFooter, SidebarHeader, SidebarRail,} from "@/components/ui/sidebar"
import {NavMain} from "@/components/layout/nav-main"
import {NavUser} from "@/components/layout/nav-user"

// AppSidebar组件的props类型
export interface AppSidebarProps extends React.ComponentProps<typeof Sidebar> {
  workspaces: WorkspaceType[]
  activeWorkspace: WorkspaceType | undefined
  onWorkspaceChange: (workspace: WorkspaceType) => void
}

export function AppSidebar({ 
  workspaces, 
  activeWorkspace,
  onWorkspaceChange,
  ...props 
}: AppSidebarProps) {
  
  // 定义工作区切换处理函数
  const handleWorkspaceChange = React.useCallback((workspace: WorkspaceType) => {
    if (onWorkspaceChange) {
      onWorkspaceChange(workspace);
    }
  }, [onWorkspaceChange]);

  return (
      activeWorkspace && <Sidebar collapsible="icon" {...props}>
      <SidebarHeader>
        <WorkspaceSwitcher 
          workspaces={workspaces} 
          activeWorkspace={activeWorkspace}
          onChange={handleWorkspaceChange}
        />
      </SidebarHeader>
      <SidebarContent>
        <NavMain items={activeWorkspace.navItems} />
      </SidebarContent>
      <SidebarFooter>
        <NavUser />
      </SidebarFooter>
      <SidebarRail />
    </Sidebar>
  )
}
