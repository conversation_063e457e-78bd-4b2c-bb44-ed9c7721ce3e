'use client';

import React, {useState} from 'react';
import {Maximize2, Minimize2} from 'lucide-react';
import {Button} from '@/components/ui/button';
import {cn} from '@/lib/utils';

interface FilePreviewProps {
    url: string;
    width?: string | number;
    height?: string | number;
    className?: string;
    showFullButton?: boolean;
}

const FilePreview: React.FC<FilePreviewProps> = (
    {
        url,
        width = '100%',
        className = '',
        showFullButton = true
    }) => {
    const [isFullscreen, setIsFullscreen] = useState(false);

    const toggleFullscreen = () => {
        setIsFullscreen(!isFullscreen);
    };

    const renderFullscreenButton = () => (
        <Button
            variant="outline"
            size="icon"
            className="bg-white/80 backdrop-blur-sm hover:bg-white/90 shadow-sm"
            onClick={toggleFullscreen}
        >
            {isFullscreen ? (
                <Minimize2 className="h-4 w-4"/>
            ) : (
                <Maximize2 className="h-4 w-4"/>
            )}
        </Button>
    );

    const renderPreview = () => {
        return (
            <iframe
                src={url}
                style={{
                    width: isFullscreen ? '100vw' : width,
                    height: isFullscreen ? '100vh' :'100%',
                    border: 'none',
                }}
                sandbox="allow-same-origin allow-scripts allow-popups allow-forms"
                className="relative h-full"
            />
        );
    };

    return (
        <div className={cn(
            'relative w-full h-full',
            isFullscreen ? 'fixed inset-0 z-50 bg-background' : '',
            className
        )}>
            <div className="relative w-full h-full">
                {!isFullscreen && showFullButton && (
                    <div className="absolute -top-[52px] right-0">
                        {renderFullscreenButton()}
                    </div>
                )}
                {renderPreview()}
                {isFullscreen && (
                    <div className="absolute right-4 top-4 z-[60]">
                        {renderFullscreenButton()}
                    </div>
                )}
            </div>
        </div>
    );
};

export default FilePreview; 