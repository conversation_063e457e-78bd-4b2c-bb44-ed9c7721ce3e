import React from 'react';
import {
  Archive,
  BarChart,
  Check,
  Database,
  FileAudio,
  FileCode, FileImage,
  FileImage as ImageIcon,
  FileJson,
  FileText,
  FileType,
  FileVideo,
  Folder,
  HelpCircle,
  Key,
  Presentation,
  Settings,
  Sheet,
  X
} from 'lucide-react';
import {cn} from '@/lib/utils';

export interface FileIconProps {
  filename: string;
  type?: 'DIRECTORY' | 'FILE' | string;
  ext?: string;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

/**
 * 通用文件图标组件，根据文件类型和扩展名显示不同的图标
 */
export const FileIcon: React.FC<FileIconProps> = ({
  filename,
  type = 'FILE',
  ext,
  size = 'md',
  className
}) => {
  // 获取文件扩展名（如果没有提供）
  const fileExt = ext || filename.split('.').pop()?.toLowerCase() || '';
  // 根据尺寸确定图标大小
  const sizeClasses = {
    sm: 'h-5 w-5',
    md: 'h-6 w-6',
    lg: 'h-10 w-10'
  };
  
  const iconSize = sizeClasses[size];
  
  // 如果是文件夹
  if (type === 'DIRECTORY' || type === 'folder') {
    return <Folder className={cn(iconSize, 'text-blue-500', className)} />;
  }

  // 图片文件
  if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg'].includes(fileExt)) {
    return <FileImage className={cn(iconSize, 'text-green-500', className)} />;
  }

  // 视频文件
  if (['mp4', 'avi', 'mov', 'wmv', 'flv', 'mkv', 'webm'].includes(fileExt)) {
    return <FileVideo className={cn(iconSize, 'text-purple-500', className)} />;
  }

  // 音频文件
  if (['mp3', 'wav', 'ogg', 'aac', 'm4a', 'flac', 'wma', 'midi', 'aiff'].includes(fileExt)) {
    return <FileAudio className={cn(iconSize, 'text-orange-500', className)} />;
  }

  // 代码文件
  if (['js', 'jsx', 'ts', 'tsx', 'java', 'py', 'cpp', 'c', 'cs', 'php', 'html', 'css', 'scss', 'less', 'rb', 'go', 'rust', 'swift', 'kotlin', 'dart', 'lua'].includes(fileExt)) {
    return <FileCode className={cn(iconSize, 'text-blue-400', className)} />;
  }

  // JSON和配置文件
  if (['json', 'xml', 'yaml', 'yml', 'toml', 'ini', 'config'].includes(fileExt)) {
    return <FileJson className={cn(iconSize, 'text-yellow-500', className)} />;
  }

  // 电子表格
  if (['xlsx', 'xls', 'csv', 'tsv', 'ods'].includes(fileExt)) {
    return <Sheet className={cn(iconSize, 'text-green-600', className)} />;
  }
  
  // 数据分析文件
  if (['dat', 'sav', 'spss', 'sas', 'stata'].includes(fileExt)) {
    return <BarChart className={cn(iconSize, 'text-indigo-500', className)} />;
  }

  // PDF文件
  if (['pdf'].includes(fileExt)) {
    return <FileText className={cn(iconSize, 'text-red-500', className)} />;
  }

  // 压缩文件
  if (['zip', 'rar', '7z', 'tar', 'gz', 'bz2', 'xz', 'iso'].includes(fileExt)) {
    return <Archive className={cn(iconSize, 'text-yellow-600', className)} />;
  }

  // 文本和文档文件
  if (['txt', 'md', 'rtf', 'doc', 'docx', 'odt', 'tex', 'log'].includes(fileExt)) {
    return <FileText className={cn(iconSize, 'text-gray-500', className)} />;
  }
  
  // 演示文稿
  if (['ppt', 'pptx', 'key', 'odp', 'sxi'].includes(fileExt)) {
    return <Presentation className={cn(iconSize, 'text-orange-600', className)} />;
  }
  
  // 数据库文件
  if (['db', 'sqlite', 'mdb', 'accdb', 'sql', 'bak'].includes(fileExt)) {
    return <Database className={cn(iconSize, 'text-blue-600', className)} />;
  }
  
  // 字体文件
  if (['ttf', 'otf', 'woff', 'woff2', 'eot'].includes(fileExt)) {
    return <Check className={cn(iconSize, 'text-purple-600', className)} />;
  }
  
  // 可执行文件
  if (['exe', 'msi', 'app', 'dmg', 'deb', 'rpm'].includes(fileExt)) {
    return <Settings className={cn(iconSize, 'text-gray-600', className)} />;
  }
  
  // 加密和证书文件
  if (['key', 'pem', 'crt', 'cer', 'p12', 'pfx'].includes(fileExt)) {
    return <Key className={cn(iconSize, 'text-yellow-700', className)} />;
  }
  
  // 临时文件
  if (['tmp', 'temp', 'swp', 'bak', 'cache'].includes(fileExt)) {
    return <X className={cn(iconSize, 'text-red-400', className)} />;
  }

  // 未知或其他类型的文件
  if (fileExt) {
    // 有扩展名但不在上面的类别中
    return <HelpCircle className={cn(iconSize, 'text-gray-400', className)} />;
  } else {
    // 没有扩展名的文件
    return <FileType className={cn(iconSize, 'text-gray-400', className)} />;
  }
};

export default FileIcon;
