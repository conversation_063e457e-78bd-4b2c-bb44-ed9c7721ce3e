/**
 * @fileoverview 日期范围选择器组件
 */
"use client"

import * as React from "react"
import {zhCN} from "date-fns/locale"
import {DateRange, DayPicker} from "react-day-picker"

import {cn} from "@/lib/utils"
import {TimePicker} from "@/components/ui/time-picker"
import {formatTimeForDisplay, parseTimeFromDisplay} from "@/lib/date"

// 日期选择器样式
const calendarStyles = {
  months: "flex flex-col sm:flex-row space-y-4 sm:space-x-4 sm:space-y-0",
  month: "space-y-4",
  caption: "flex justify-center pt-1 relative items-center",
  caption_label: "text-sm font-medium",
  nav: "space-x-1 flex items-center",
  nav_button: cn(
    "h-7 w-7 bg-transparent p-0 opacity-50 hover:opacity-100"
  ),
  nav_button_previous: "absolute left-1",
  nav_button_next: "absolute right-1",
  table: "w-full border-collapse space-y-1",
  head_row: "flex",
  head_cell: "text-muted-foreground rounded-md w-9 font-normal text-[0.8rem]",
  row: "flex w-full mt-2",
  cell: "h-9 w-9 text-center text-sm p-0 relative [&:has([aria-selected].day-range-end)]:rounded-r-md [&:has([aria-selected].day-range-start)]:rounded-l-md first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md",
  day: cn(
    "h-9 w-9 p-0 font-normal aria-selected:opacity-100",
    "hover:bg-accent hover:text-accent-foreground",
    "focus-visible:bg-accent focus-visible:text-accent-foreground focus-visible:rounded-sm"
  ),
  day_range_start: "day-range-start",
  day_range_end: "day-range-end",
  day_selected:
    "bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground focus:bg-primary focus:text-primary-foreground",
  day_today: "bg-accent text-accent-foreground",
  day_outside: "text-muted-foreground opacity-50",
  day_disabled: "text-muted-foreground opacity-50",
  day_range_middle:
    "aria-selected:bg-accent aria-selected:text-accent-foreground",
  day_hidden: "invisible",
};

interface DatePickerWithRangeProps {
  /** 日期范围值 */
  value?: DateRange;
  /** 日期范围变化回调 */
  onChange?: (date: DateRange | undefined) => void;
  /** 是否显示时间选择器 */
  showTimePicker?: boolean;
  /** 自定义类名 */
  className?: string;
}

export function DatePickerWithRange({
  value,
  onChange,
  showTimePicker = false,
  className,
}: DatePickerWithRangeProps) {
  const [fromTime, setFromTime] = React.useState<string>(() => {
    if (value?.from) {
      return formatTimeForDisplay(value.from);
    }
    return "00:00";
  });
  
  const [toTime, setToTime] = React.useState<string>(() => {
    if (value?.to) {
      return formatTimeForDisplay(value.to);
    }
    return "23:59";
  });

  // 更新时间状态
  React.useEffect(() => {
    if (value?.from) {
      setFromTime(formatTimeForDisplay(value.from));
    }
    if (value?.to) {
      setToTime(formatTimeForDisplay(value.to));
    }
  }, [value]);

  // 处理时间变化
  const handleTimeChange = React.useCallback((type: 'from' | 'to', timeStr: string) => {
    if (type === 'from') {
      setFromTime(timeStr);
    } else {
      setToTime(timeStr);
    }

    if (!value?.from || !value?.to) return;

    const baseDate = type === 'from' ? value.from : value.to;
    const newDate = parseTimeFromDisplay(timeStr, baseDate);

    onChange?.({
      from: type === 'from' ? newDate : value.from,
      to: type === 'to' ? newDate : value.to
    });
  }, [value, onChange]);

  // 当日期变化时更新值
  const handleDateChange = React.useCallback((range: DateRange | undefined) => {
    if (!range) {
      onChange?.(undefined);
      return;
    }

    const { from, to } = range;
    if (!from || !to) {
      onChange?.(range);
      return;
    }

    if (showTimePicker) {
      const newFrom = parseTimeFromDisplay(fromTime, from);
      const newTo = parseTimeFromDisplay(toTime, to);

      onChange?.({ from: newFrom, to: newTo });
    } else {
      onChange?.(range);
    }
  }, [fromTime, toTime, onChange, showTimePicker]);

  return (
    <div className={cn("grid gap-2", className)}>
      <DayPicker
        mode="range"
        defaultMonth={value?.from}
        selected={value}
        onSelect={handleDateChange}
        numberOfMonths={2}
        locale={zhCN}
        showOutsideDays={false}
        classNames={calendarStyles}
      />
      {showTimePicker && (
        <div className="flex items-center gap-4 px-4 pt-4 border-t">
          <div className="flex-1">
            <TimePicker
              value={fromTime}
              onChange={(time) => handleTimeChange('from', time)}
              label="开始时间"
            />
          </div>
          <div className="text-muted-foreground">~</div>
          <div className="flex-1">
            <TimePicker
              value={toTime}
              onChange={(time) => handleTimeChange('to', time)}
              label="结束时间"
            />
          </div>
        </div>
      )}
    </div>
  );
} 