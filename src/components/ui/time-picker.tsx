/**
 * @fileoverview 时间选择器组件
 */
"use client"

import * as React from "react"
import {Clock} from "lucide-react"
import {Label} from "@/components/ui/label"
import {Input} from "@/components/ui/input"
import {Button} from "@/components/ui/button"
import {Popover, PopoverContent, PopoverTrigger,} from "@/components/ui/popover"
import {cn} from "@/lib/utils"
import {ScrollArea} from "@/components/ui/scroll-area"

interface TimePickerProps {
  /** 时间值（HH:mm格式） */
  value: string;
  /** 时间变化回调 */
  onChange: (value: string) => void;
  /** 标签文本 */
  label?: string;
  /** 自定义类名 */
  className?: string;
  /** 输入框类名 */
  inputClassName?: string;
  /** 是否禁用 */
  disabled?: boolean;
}

export function TimePicker({
  value,
  onChange,
  label,
  className,
  inputClassName,
  disabled = false,
}: TimePickerProps) {
  const [isOpen, setIsOpen] = React.useState(false);
  const containerRef = React.useRef<HTMLDivElement>(null);
  const [width, setWidth] = React.useState<number>(0);
  const [localValue, setLocalValue] = React.useState(value);

  // 解析当前时间值
  const [currentHours, currentMinutes] = React.useMemo(() => {
    const [h = 0, m = 0] = localValue.split(':').map(Number);
    return [h, m];
  }, [localValue]);

  // 同步外部值
  React.useEffect(() => {
    setLocalValue(value);
  }, [value]);

  // 更新宽度
  React.useEffect(() => {
    if (containerRef.current) {
      setWidth(containerRef.current.offsetWidth);
    }
  }, []);

  // 生成小时和分钟选项
  const hourOptions = Array.from({ length: 24 }, (_, i) => i);
  const minuteOptions = Array.from({ length: 60 }, (_, i) => i);

  // 格式化数字为两位数
  const formatNumber = (num: number) => num.toString().padStart(2, '0');

  // 处理时间选择
  const handleTimeChange = React.useCallback((type: 'hours' | 'minutes', value: number) => {
    const newHours = type === 'hours' ? value : currentHours;
    const newMinutes = type === 'minutes' ? value : currentMinutes;
    const newTime = `${formatNumber(newHours)}:${formatNumber(newMinutes)}`;
    
    setLocalValue(newTime);
    onChange(newTime);
    
    // 如果是分钟选择，则关闭弹出框
    if (type === 'minutes') {
      setIsOpen(false);
    }
  }, [currentHours, currentMinutes, onChange]);

  return (
    <div className={cn("grid gap-1.5", className)}>
      {label && <Label>{label}</Label>}
      <Popover open={isOpen} onOpenChange={disabled ? undefined : setIsOpen}>
        <PopoverTrigger asChild>
          <div className="relative" ref={containerRef}>
            <Input
              value={localValue}
              readOnly
              disabled={disabled}
              className={cn(
                "w-full",
                "pl-8",
                "bg-background",
                "text-sm",
                "text-center",
                "ring-offset-background",
                "placeholder:text-muted-foreground",
                "focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",
                disabled && "cursor-not-allowed opacity-50",
                "cursor-pointer",
                inputClassName
              )}
            />
            <Clock className="absolute left-2.5 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground pointer-events-none" />
          </div>
        </PopoverTrigger>
        <PopoverContent 
          className="p-0" 
          align="start"
          style={{ width: width > 0 ? `${width}px` : 'auto' }}
        >
          <div className="grid grid-cols-2 gap-2 p-3">
            <div className="space-y-2">
              <div className="text-xs font-medium text-center text-muted-foreground">小时</div>
              <ScrollArea className="h-40 rounded-md border">
                <div className="p-2">
                  {hourOptions.map((hour) => (
                    <Button
                      key={hour}
                      variant={currentHours === hour ? "secondary" : "ghost"}
                      className="w-full justify-center font-normal"
                      onClick={() => handleTimeChange('hours', hour)}
                    >
                      {formatNumber(hour)}
                    </Button>
                  ))}
                </div>
              </ScrollArea>
            </div>
            <div className="space-y-2">
              <div className="text-xs font-medium text-center text-muted-foreground">分钟</div>
              <ScrollArea className="h-40 rounded-md border">
                <div className="p-2">
                  {minuteOptions.map((minute) => (
                    <Button
                      key={minute}
                      variant={currentMinutes === minute ? "secondary" : "ghost"}
                      className="w-full justify-center font-normal"
                      onClick={() => handleTimeChange('minutes', minute)}
                    >
                      {formatNumber(minute)}
                    </Button>
                  ))}
                </div>
              </ScrollArea>
            </div>
          </div>
        </PopoverContent>
      </Popover>
    </div>
  );
} 