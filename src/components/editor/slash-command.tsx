import { useState, useEffect, useRef } from 'react';
import { Editor } from '@tiptap/react';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import {
  Heading1,
  Heading2,
  Heading3,
  Type,
  List,
  ListOrdered,
  CheckSquare,
  Quote,
  Code,
  Image,
  Table,
  Minus
} from 'lucide-react';

interface SlashCommandProps {
  editor: Editor;
  range: { from: number; to: number };
  onClose: () => void;
}

interface CommandItem {
  title: string;
  description: string;
  icon: React.ReactNode;
  command: () => void;
  keywords: string[];
}

export function SlashCommand({ editor, range, onClose }: SlashCommandProps) {
  const [selectedIndex, setSelectedIndex] = useState(0);
  const [filteredItems, setFilteredItems] = useState<CommandItem[]>([]);
  const menuRef = useRef<HTMLDivElement>(null);

  const commands: CommandItem[] = [
    {
      title: '标题 1',
      description: '大标题',
      icon: <Heading1 className="h-4 w-4" />,
      command: () => {
        editor.chain().focus().deleteRange(range).setHeading({ level: 1 }).run();
      },
      keywords: ['h1', 'heading1', '标题1', '大标题']
    },
    {
      title: '标题 2',
      description: '中标题',
      icon: <Heading2 className="h-4 w-4" />,
      command: () => {
        editor.chain().focus().deleteRange(range).setHeading({ level: 2 }).run();
      },
      keywords: ['h2', 'heading2', '标题2', '中标题']
    },
    {
      title: '标题 3',
      description: '小标题',
      icon: <Heading3 className="h-4 w-4" />,
      command: () => {
        editor.chain().focus().deleteRange(range).setHeading({ level: 3 }).run();
      },
      keywords: ['h3', 'heading3', '标题3', '小标题']
    },
    {
      title: '正文',
      description: '普通文本段落',
      icon: <Type className="h-4 w-4" />,
      command: () => {
        editor.chain().focus().deleteRange(range).setParagraph().run();
      },
      keywords: ['p', 'paragraph', '正文', '段落', '文本']
    },
    {
      title: '无序列表',
      description: '创建无序列表',
      icon: <List className="h-4 w-4" />,
      command: () => {
        editor.chain().focus().deleteRange(range).toggleBulletList().run();
      },
      keywords: ['ul', 'bullet', '无序列表', '列表', '项目符号']
    },
    {
      title: '有序列表',
      description: '创建有序列表',
      icon: <ListOrdered className="h-4 w-4" />,
      command: () => {
        editor.chain().focus().deleteRange(range).toggleOrderedList().run();
      },
      keywords: ['ol', 'ordered', '有序列表', '数字列表', '编号']
    },
    {
      title: '任务列表',
      description: '创建待办事项',
      icon: <CheckSquare className="h-4 w-4" />,
      command: () => {
        editor.chain().focus().deleteRange(range).toggleTaskList().run();
      },
      keywords: ['todo', 'task', '任务', '待办', '清单', 'checkbox']
    },
    {
      title: '引用',
      description: '创建引用块',
      icon: <Quote className="h-4 w-4" />,
      command: () => {
        editor.chain().focus().deleteRange(range).toggleBlockquote().run();
      },
      keywords: ['quote', 'blockquote', '引用', '引述']
    },
    {
      title: '代码块',
      description: '插入代码块',
      icon: <Code className="h-4 w-4" />,
      command: () => {
        editor.chain().focus().deleteRange(range).toggleCodeBlock().run();
      },
      keywords: ['code', 'codeblock', '代码', '代码块', 'pre']
    },
    {
      title: '分割线',
      description: '插入水平分割线',
      icon: <Minus className="h-4 w-4" />,
      command: () => {
        editor.chain().focus().deleteRange(range).setHorizontalRule().run();
      },
      keywords: ['hr', 'divider', '分割线', '水平线', '分隔符']
    },
    {
      title: '图片',
      description: '插入图片',
      icon: <Image className="h-4 w-4" />,
      command: () => {
        const url = window.prompt('请输入图片URL:');
        if (url) {
          editor.chain().focus().deleteRange(range).setImage({ src: url }).run();
        }
      },
      keywords: ['image', 'img', '图片', '图像', '照片']
    }
  ];

  useEffect(() => {
    setFilteredItems(commands);
  }, []);

  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'ArrowDown') {
        event.preventDefault();
        setSelectedIndex((prev) => (prev + 1) % filteredItems.length);
      } else if (event.key === 'ArrowUp') {
        event.preventDefault();
        setSelectedIndex((prev) => (prev - 1 + filteredItems.length) % filteredItems.length);
      } else if (event.key === 'Enter') {
        event.preventDefault();
        if (filteredItems[selectedIndex]) {
          filteredItems[selectedIndex].command();
          onClose();
        }
      } else if (event.key === 'Escape') {
        event.preventDefault();
        onClose();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [filteredItems, selectedIndex, onClose]);

  const filterCommands = (query: string) => {
    if (!query) {
      setFilteredItems(commands);
      return;
    }

    const filtered = commands.filter(item =>
      item.title.toLowerCase().includes(query.toLowerCase()) ||
      item.description.toLowerCase().includes(query.toLowerCase()) ||
      item.keywords.some(keyword => keyword.toLowerCase().includes(query.toLowerCase()))
    );

    setFilteredItems(filtered);
    setSelectedIndex(0);
  };

  return (
    <div
      ref={menuRef}
      className="bg-white border rounded-lg shadow-lg p-2 max-h-80 overflow-y-auto z-50 min-w-64"
    >
      <div className="text-xs text-gray-500 px-2 py-1 border-b mb-2">
        基本块
      </div>
      {filteredItems.length === 0 ? (
        <div className="px-2 py-4 text-sm text-gray-500 text-center">
          没有找到匹配的命令
        </div>
      ) : (
        filteredItems.map((item, index) => (
          <Button
            key={item.title}
            variant="ghost"
            className={cn(
              "w-full justify-start h-auto p-2 text-left",
              index === selectedIndex && "bg-gray-100"
            )}
            onClick={() => {
              item.command();
              onClose();
            }}
          >
            <div className="flex items-center gap-3">
              <div className="flex-shrink-0 text-gray-600">
                {item.icon}
              </div>
              <div className="flex-1 min-w-0">
                <div className="font-medium text-sm">{item.title}</div>
                <div className="text-xs text-gray-500 truncate">{item.description}</div>
              </div>
            </div>
          </Button>
        ))
      )}
    </div>
  );
}
