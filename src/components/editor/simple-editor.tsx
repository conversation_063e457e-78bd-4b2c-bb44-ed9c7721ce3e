import { useEditor, EditorContent } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import Placeholder from '@tiptap/extension-placeholder';
import { cn } from '@/lib/utils';

interface SimpleEditorProps {
  content?: string;
  onChange?: (content: string) => void;
  placeholder?: string;
  readOnly?: boolean;
  className?: string;
}

export function SimpleEditor({
  content = '',
  onChange,
  placeholder = '开始输入...',
  readOnly = false,
  className
}: SimpleEditorProps) {
  const editor = useEditor({
    extensions: [
      StarterKit,
      Placeholder.configure({
        placeholder,
      }),
    ],
    content,
    editable: !readOnly,
    onUpdate: ({ editor }) => {
      const html = editor.getHTML();
      onChange?.(html);
    },
    editorProps: {
      attributes: {
        class: cn(
          'prose prose-sm sm:prose-base focus:outline-none',
          'min-h-[200px] max-w-none px-4 py-2',
          className
        ),
      },
    },
  });

  if (!editor) {
    return <div>Loading editor...</div>;
  }

  return (
    <div className={cn('border rounded-lg overflow-hidden bg-white', className)}>
      <div className="relative">
        <EditorContent 
          editor={editor} 
          className="min-h-[200px] focus-within:outline-none"
        />
      </div>
    </div>
  );
}
