import { useEffect, useRef } from 'react';
import { Editor } from '@tiptap/react';
import { Button } from '@/components/ui/button';
import { GripVertical, Plus } from 'lucide-react';
import { cn } from '@/lib/utils';

interface DragHandleProps {
  editor: Editor;
  className?: string;
}

export function DragHandle({ editor, className }: DragHandleProps) {
  const dragHandleRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!editor || !dragHandleRef.current) return;

    const editorElement = editor.view.dom;
    let currentNode: Element | null = null;

    const showDragHandle = (node: Element) => {
      if (!dragHandleRef.current) return;
      
      const rect = node.getBoundingClientRect();
      const editorRect = editorElement.getBoundingClientRect();
      
      dragHandleRef.current.style.display = 'flex';
      dragHandleRef.current.style.top = `${rect.top - editorRect.top}px`;
      dragHandleRef.current.style.left = '-40px';
    };

    const hideDragHandle = () => {
      if (!dragHandleRef.current) return;
      dragHandleRef.current.style.display = 'none';
    };

    const handleMouseMove = (event: MouseEvent) => {
      const target = event.target as Element;
      const node = target.closest('.ProseMirror > *');
      
      if (node && node !== currentNode) {
        currentNode = node;
        showDragHandle(node);
      } else if (!node) {
        currentNode = null;
        hideDragHandle();
      }
    };

    const handleMouseLeave = () => {
      currentNode = null;
      hideDragHandle();
    };

    editorElement.addEventListener('mousemove', handleMouseMove);
    editorElement.addEventListener('mouseleave', handleMouseLeave);

    return () => {
      editorElement.removeEventListener('mousemove', handleMouseMove);
      editorElement.removeEventListener('mouseleave', handleMouseLeave);
    };
  }, [editor]);

  const handleDragStart = (event: React.DragEvent) => {
    // 这里可以实现拖拽逻辑
    event.dataTransfer.effectAllowed = 'move';
  };

  const handleAddBlock = () => {
    // 在当前位置添加新块
    editor.chain().focus().insertContent('<p></p>').run();
  };

  return (
    <div
      ref={dragHandleRef}
      className={cn(
        'absolute flex items-center gap-1 opacity-0 hover:opacity-100 transition-opacity duration-200 z-10',
        className
      )}
      style={{ display: 'none' }}
    >
      <Button
        variant="ghost"
        size="sm"
        className="h-6 w-6 p-0 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded cursor-grab active:cursor-grabbing"
        title="拖拽移动"
        draggable
        onDragStart={handleDragStart}
      >
        <GripVertical className="h-4 w-4" />
      </Button>
      <Button
        variant="ghost"
        size="sm"
        className="h-6 w-6 p-0 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded"
        title="添加块"
        onClick={handleAddBlock}
      >
        <Plus className="h-4 w-4" />
      </Button>
    </div>
  );
}
