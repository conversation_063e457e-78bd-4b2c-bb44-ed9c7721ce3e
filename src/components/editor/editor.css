/* TipTap Editor Styles - Notion-like */
.ProseMirror {
  outline: none;
  padding: 1.5rem;
  min-height: 200px;
  font-family: ui-sans-serif, -apple-system, BlinkMacSystemFont, "Segoe UI", Helvetica, "Apple Color Emoji", <PERSON><PERSON>, sans-serif, "Segoe UI Emoji", "Segoe UI Symbol";
  line-height: 1.6;
  color: rgb(55, 53, 47);
}

.ProseMirror p.is-editor-empty:first-child::before {
  color: rgba(55, 53, 47, 0.4);
  content: attr(data-placeholder);
  float: left;
  height: 0;
  pointer-events: none;
  font-weight: 400;
}

/* Heading styles - Notion-like */
.ProseMirror h1 {
  font-size: 2.25rem;
  font-weight: 700;
  line-height: 1.2;
  margin-top: 2rem;
  margin-bottom: 0.5rem;
  color: rgb(55, 53, 47);
}

.ProseMirror h2 {
  font-size: 1.875rem;
  font-weight: 600;
  line-height: 1.3;
  margin-top: 1.5rem;
  margin-bottom: 0.5rem;
  color: rgb(55, 53, 47);
}

.ProseMirror h3 {
  font-size: 1.5rem;
  font-weight: 600;
  line-height: 1.4;
  margin-top: 1.25rem;
  margin-bottom: 0.5rem;
  color: rgb(55, 53, 47);
}

.ProseMirror p {
  margin: 0.5rem 0;
  line-height: 1.6;
}

/* List styles */
.ProseMirror ul,
.ProseMirror ol {
  padding-left: 1.5rem;
  margin: 1rem 0;
}

.ProseMirror li {
  margin: 0.25rem 0;
}

.ProseMirror li p {
  margin: 0;
}

/* Task list styles */
.ProseMirror ul[data-type="taskList"] {
  list-style: none;
  padding: 0;
}

.ProseMirror ul[data-type="taskList"] li {
  display: flex;
  align-items: flex-start;
  margin: 0.5rem 0;
}

.ProseMirror ul[data-type="taskList"] li > label {
  flex: 0 0 auto;
  margin-right: 0.5rem;
  user-select: none;
}

.ProseMirror ul[data-type="taskList"] li > div {
  flex: 1 1 auto;
}

.ProseMirror ul[data-type="taskList"] input[type="checkbox"] {
  cursor: pointer;
}

/* Blockquote styles */
.ProseMirror blockquote {
  border-left: 4px solid #e5e7eb;
  padding-left: 1rem;
  margin: 1rem 0;
  font-style: italic;
  color: #6b7280;
}

/* Code styles */
.ProseMirror code {
  background-color: #f3f4f6;
  border-radius: 0.25rem;
  padding: 0.125rem 0.25rem;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.875em;
}

.ProseMirror pre {
  background-color: #1f2937;
  color: #f9fafb;
  border-radius: 0.5rem;
  padding: 1rem;
  margin: 1rem 0;
  overflow-x: auto;
}

.ProseMirror pre code {
  background: none;
  color: inherit;
  font-size: inherit;
  padding: 0;
}

/* Link styles */
.ProseMirror a {
  color: #3b82f6;
  text-decoration: underline;
  cursor: pointer;
}

.ProseMirror a:hover {
  color: #1d4ed8;
}

/* Image styles */
.ProseMirror img {
  max-width: 100%;
  height: auto;
  border-radius: 0.5rem;
  margin: 1rem 0;
}

/* Highlight styles */
.ProseMirror mark {
  background-color: #fef08a;
  border-radius: 0.125rem;
  padding: 0.125rem 0.25rem;
}

/* Selection styles */
.ProseMirror .selectedCell:after {
  z-index: 2;
  position: absolute;
  content: "";
  left: 0; right: 0; top: 0; bottom: 0;
  background: rgba(200, 200, 255, 0.4);
  pointer-events: none;
}

/* Focus styles */
.ProseMirror:focus {
  outline: none;
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .ProseMirror {
    padding: 0.75rem;
  }
  
  .ProseMirror h1 {
    font-size: 1.75rem;
  }
  
  .ProseMirror h2 {
    font-size: 1.375rem;
  }
  
  .ProseMirror h3 {
    font-size: 1.125rem;
  }
}
