import { Condition, Group, OperatorType } from './types';

/**
 * 将操作符转换为 SpEL 表达式操作符
 */
const operatorToSpel = (op: OperatorType, fieldPath: string, value: any): string => {
  switch (op) {
    case 'equal':
      if (typeof value === 'string') {
        return `${fieldPath} == '${value}'`;
      }
      return `${fieldPath} == ${value}`;
    case 'notEqual':
      if (typeof value === 'string') {
        return `${fieldPath} != '${value}'`;
      }
      return `${fieldPath} != ${value}`;
    case 'notEmpty':
      return `${fieldPath} != null and ${fieldPath} != ''`;
    case 'empty':
      return `${fieldPath} == null or ${fieldPath} == ''`;
    case 'startWith':
      return `${fieldPath}.startsWith('${value}')`;
    case 'endWith':
      return `${fieldPath}.endsWith('${value}')`;
    case 'contains':
      return `${fieldPath}.contains('${value}')`;
    case 'notContains':
      return `!${fieldPath}.contains('${value}')`;
    case 'belongTo':
      if (Array.isArray(value)) {
        // 使用正确的 SpEL 语法处理集合包含关系
        const values = value.map(v => typeof v === 'string' ? `'${v}'` : v).join(', ');
        // 使用正确的 SpEL 语法，使用数组定义和 contains 函数
        return `{${values}}.contains(${fieldPath})`;
      }
      return `false`;
    case 'notBelongTo':
      if (Array.isArray(value)) {
        // 使用正确的 SpEL 语法处理非集合包含关系
        const values = value.map(v => typeof v === 'string' ? `'${v}'` : v).join(', ');
        // 使用正确的 SpEL 语法，使用数组定义和 contains 函数的非运算
        return `!{${values}}.contains(${fieldPath})`;
      }
      return `true`;
    case 'less':
      return `${fieldPath} < ${value}`;
    case 'lessOrEqual':
      return `${fieldPath} <= ${value}`;
    case 'greater':
      return `${fieldPath} > ${value}`;
    case 'greaterOrEqual':
      return `${fieldPath} >= ${value}`;
    case 'between':
      if (Array.isArray(value) && value.length === 2) {
        return `${fieldPath} >= ${value[0]} and ${fieldPath} <= ${value[1]}`;
      }
      return `false`;
    case 'notBetween':
      if (Array.isArray(value) && value.length === 2) {
        return `${fieldPath} < ${value[0]} or ${fieldPath} > ${value[1]}`;
      }
      return `true`;
    case 'match':
      // 对于 object 类型，递归处理子条件组
      if (typeof value === 'object' && value !== null) {
        return groupToSpel(value as Group, fieldPath);
      }
      return `false`;
    case 'any':
      // 对于 array 类型，使用 any() 函数检查数组中是否有元素满足条件
      if (Array.isArray(value)) {
        // 当value是数组时，判断数组中是否包含任一元素
        if (value.length === 1) {
          // 如果只有一个值，直接使用contains函数
          const val = typeof value[0] === 'string' ? `'${value[0]}'` : value[0];
          return `${fieldPath}.contains(${val})`;
        } else if (value.length > 1) {
          // 如果有多个值，使用OR连接多个contains条件
          const conditions = value.map(v => {
            const val = typeof v === 'string' ? `'${v}'` : v;
            return `${fieldPath}.contains(${val})`;
          });
          return `(${conditions.join(' or ')})`;
        }
        return 'false';  // 空数组情况
      } else if (typeof value === 'object' && value !== null) {
        // 当value是条件组时，递归处理
        return `${fieldPath}.?[${groupToSpel(value as Group, '#this')}].size() > 0`;
      }
      return `false`;
    case 'all':
      // 对于 array 类型，使用 all() 函数检查数组中是否所有元素都满足条件
      if (Array.isArray(value)) {
        // 当value是数组时，判断数组中是否所有元素都在指定的值列表中
        if (value.length === 1) {
          // 如果只有一个值，检查数组中的所有元素是否都等于该值
          const val = typeof value[0] === 'string' ? `'${value[0]}'` : value[0];
          return `${fieldPath}.size() > 0 and ${fieldPath}.?[#this != ${val}].size() == 0`;
        } else if (value.length > 1) {
          // 如果有多个值，使用AND连接多个条件
          const conditions = value.map(v => {
            const val = typeof v === 'string' ? `'${v}'` : v;
            return `${fieldPath}.contains(${val})`;
          });
          return `${fieldPath}.size() > 0 and (${conditions.join(' and ')})`;
        }
        return 'false';  // 空数组情况
      } else if (typeof value === 'object' && value !== null) {
        // 当value是条件组时，递归处理
        return `${fieldPath}.?[!(${groupToSpel(value as Group, '#this')})].size() == 0 and ${fieldPath}.size() > 0`;
      }
      return `false`;
    default:
      return 'true';
  }
};

/**
 * 将单个条件转换为 SpEL 表达式
 */
const conditionToSpel = (condition: Condition, parentPath: string = ''): string => {
  if (!condition.field?.code || !condition.op || condition.value == "*") {
    return 'true';
  }

  const fieldPath = parentPath 
    ? `${parentPath}.${condition.field.code}` 
    : condition.field.code;

  return operatorToSpel(condition.op, fieldPath, condition.value);
};

/**
 * 将条件组转换为 SpEL 表达式
 */
export const groupToSpel = (group: Group, parentPath: string = ''): string => {
  if (!group) return 'true';
  
  const conditions: string[] = [];
  
  // 处理条件
  if (group.conditions && group.conditions.length > 0) {
    conditions.push(...group.conditions.map(c => conditionToSpel(c, parentPath)));
  }
  
  // 处理子组
  if (group.groups && group.groups.length > 0) {
    conditions.push(...group.groups.map(g => `(${groupToSpel(g, parentPath)})`));
  }
  
  if (conditions.length === 0) {
    return 'true';
  }
  
  // 根据逻辑运算符连接条件
  const logicOp = group.logic === 'AND' ? ' and ' : ' or ';
  return conditions.join(logicOp);
};

/**
 * 将条件组转换为 SpEL 表达式字符串
 */
export const convertToSpel = (group: Group): string => {
  return groupToSpel(group);
};
