import { FieldTypes, Option } from './types';

export const TextOperationOption: Array<Option> = [
  {
    label: '等于',
    value: 'equal',
  },
  {
    label: '不等于',
    value: 'notEqual',
  },
  {
    label: '不为空',
    value: 'notEmpty',
  },
  {
    label: '是空',
    value: 'empty',
  },
  {
    label: '开始于',
    value: 'startWith',
  },
  {
    label: '结束于',
    value: 'endWith',
  },
  {
    label: '包含',
    value: 'contains',
  },
  {
    label: '不包含',
    value: 'notContains',
  },
  {
    label: '属于',
    value: 'belongTo',
  },
  {
    label: '不属于',
    value: 'notBelongTo',
  },
];

export const NumberOperationOption: Array<Option> = [
  {
    label: '等于',
    value: 'equal',
  },
  {
    label: '不等于',
    value: 'notEqual',
  },
  {
    label: '小于',
    value: 'less',
  },
  {
    label: '小于等于',
    value: 'lessOrEqual',
  },
  {
    label: '大于',
    value: 'greater',
  },
  {
    label: '大于等于',
    value: 'greaterOrEqual',
  },
  {
    label: '在区间',
    value: 'between',
  },
  {
    label: '不在区间',
    value: 'notBetween',
  },
];

export const TimeOperationOption: Array<Option> = [
  {
    label: '小于',
    value: 'less',
  },
  {
    label: '小于等于',
    value: 'lessOrEqual',
  },
  {
    label: '大于',
    value: 'greater',
  },
  {
    label: '大于等于',
    value: 'greaterOrEqual',
  },
  {
    label: '在区间',
    value: 'between',
  },
  {
    label: '不在区间',
    value: 'notBetween',
  },
];

export const BooleanOperationOption: Array<Option> = [
  {
    label: '等于',
    value: 'equal',
  },
  {
    label: '不等于',
    value: 'notEqual',
  },
];

export const ObjectOperationOption: Array<Option> = [
  {
    label: '匹配',
    value: 'match',
  },
];

export const ArrayOperationOption: Array<Option> = [
  {
    label: '任意（满足一个）',
    value: 'any',
  },
  {
    label: '全部（满足所有）',
    value: 'all',
  },
];

export const getOptions = (type: FieldTypes) => {
  switch (type) {
    case 'text':
      return TextOperationOption;
    case 'number':
      return NumberOperationOption;
    case 'boolean':
      return BooleanOperationOption;
    case 'date':
    case 'time':
    case 'datetime':
      return TimeOperationOption;
    case 'array':
      return ArrayOperationOption;
    case 'object':
      return ObjectOperationOption;
    case 'select':
    default:
      return [];
  }
};
