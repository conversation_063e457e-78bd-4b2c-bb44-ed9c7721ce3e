'use client';

import { useMemo } from 'react';

import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";

import Term from './term';
import TermLogic from './term-logic';
import { Field, Group, TermType } from './types';

interface FilterGroupProps {
  group: Group;
  fields: Field[];
  level?: number;
  root?: boolean;
  onChange: (group: Group) => void;
  onDeleteGroup?: (index: number) => void;
}

export default function Terms({
  group,
  fields = [],
  level = 0,
  root = false,
  onChange,
  onDeleteGroup
}: FilterGroupProps) {
  
  const addTerms = (type: TermType) => {
    const newGroup = { ...group };
    
    // 确保 conditions 和 groups 属性存在
    if (!newGroup.conditions) newGroup.conditions = [];
    if (!newGroup.groups) newGroup.groups = [];
    
    switch (type) {
      case 'CONDITION':
        newGroup.conditions = [
          ...newGroup.conditions,
          {
            field: {
              type: 'text',
              code: '',
            },
            op: '',
            value: '',
          }
        ];
        break;
      case 'GROUP':
        newGroup.groups = [
          ...newGroup.groups,
          {
            logic: 'AND',
            groups: [],
            conditions: [],
          }
        ];
        break;
      default:
    }
    
    onChange(newGroup);
  };

  const deleteGroup = (index: number) => {
    const newGroup = { ...group };
    newGroup.groups = newGroup.groups?.filter((_, i) => i !== index) || [];
    onChange(newGroup);
  };
  
  const deleteCondition = (index: number) => {
    const newGroup = { ...group };
    newGroup.conditions = newGroup.conditions?.filter((_, i) => i !== index) || [];
    onChange(newGroup);
  };

  const handleLogicChange = (value: 'AND' | 'OR') => {
    onChange({ ...group, logic: value });
  };

  const handleConditionChange = (index: number, condition: any) => {
    const newGroup = { ...group };
    newGroup.conditions = [...newGroup.conditions];
    newGroup.conditions[index] = condition;
    onChange(newGroup);
  };

  const handleSubGroupChange = (index: number, subGroup: Group) => {
    const newGroup = { ...group };
    newGroup.groups = [...newGroup.groups];
    newGroup.groups[index] = subGroup;
    onChange(newGroup);
  };

  const emptyTerms = useMemo(() => {
    return (group?.conditions?.length || 0) === 0 && (group?.groups?.length || 0) === 0;
  }, [group?.conditions?.length, group?.groups?.length]);

  return (
      <Card className={`w-full ${!root ? 'hover:shadow-md' : ''}`}>
        <CardContent className="p-2">
          <div className="flex w-full">
            <div className="logic self-center flex justify-center" style={{ width: '80px' }}>
              <div className="flex justify-center items-center">
                <TermLogic value={group.logic} onChange={handleLogicChange} />
              </div>
            </div>
            
            <div className="terms flex w-full">
              <div className="flex flex-col w-full space-y-1">
                {emptyTerms && (
                  <div className="emptyCondition bg-primary/5 min-h-[30px] text-muted-foreground flex items-center pl-1.5">
                    <div>空</div>
                  </div>
                )}
                
                {group?.conditions?.map((condition, index) => (
                  <div key={index} className="condition flex items-center">
                    <Term
                      condition={condition}
                      fields={fields}
                      index={index}
                      onDeleteCondition={deleteCondition}
                      onChange={(newCondition) => handleConditionChange(index, newCondition)}
                    />
                  </div>
                ))}
                
                {group?.groups?.map((subGroup, index) => (
                  <div key={index}>
                    <Terms
                      group={subGroup}
                      level={index}
                      fields={fields}
                      onChange={(newSubGroup) => handleSubGroupChange(index, newSubGroup)}
                      onDeleteGroup={deleteGroup}
                    />
                  </div>
                ))}
                
                <div className="flex space-x-2">
                  <Button 
                    variant="outline" 
                    size="sm" 
                    onClick={() => addTerms('CONDITION')}
                  >
                    添加条件
                  </Button>
                  <Button 
                    variant="outline" 
                    size="sm" 
                    onClick={() => addTerms('GROUP')}
                  >
                    添加条件组
                  </Button>
                  
                  {!root && onDeleteGroup && (
                    <Popover>
                      <PopoverTrigger asChild>
                        <Button variant="destructive" size="sm">删除组</Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-4">
                        <div className="flex flex-col gap-4">
                          <p>确认删除吗条件组吗,删除条件组后，组内的条件也会被删除！</p>
                          <Button 
                            variant="destructive" 
                            onClick={() => onDeleteGroup(level)}
                          >
                            确认
                          </Button>
                        </div>
                      </PopoverContent>
                    </Popover>
                  )}
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
  );
}
