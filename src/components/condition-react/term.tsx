'use client';

import { find } from 'lodash';
import { useMemo, useState } from 'react';
import { Trash2 } from "lucide-react";

import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Switch } from "@/components/ui/switch";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Label } from "@/components/ui/label";

import { getOptions } from './constants';
import Terms from './terms';
import { Condition, Field, OperatorType } from './types';

interface ConditionItemProps {
  condition: Condition;
  fields: Field[];
  index: number;
  onDeleteCondition: (index: number) => void;
  onChange: (condition: Condition) => void;
}

export default function Term({
  condition, 
  fields, 
  index, 
  onDeleteCondition,
  onChange
}: ConditionItemProps) {
  
  const fieldChange = (value: string) => {
    const fieldObj = find(fields, { fieldCode: value });
    const fieldType = fieldObj?.type || 'text';
    const ops = getOptions(fieldType);
    const firstOp = ops.length > 0 ? ops[0].value as OperatorType : '';
    
    const newCondition: Condition = {
      field: {
        code: value,
        type: fieldType,
      },
      op: firstOp,
      value: fieldType === 'array' || fieldType === 'object' ? {
        conditions: [],
        groups: [],
        logic: 'AND',
      } : '',
    };
    
    onChange(newCondition);
  };

  const operationChange = (value: string) => {
    const updatedCondition: Condition = {
      field: condition.field,
      op: value as OperatorType,
      value: '',
    };
    onChange(updatedCondition);
  };

  const handleValueChange = (value: any) => {
    const updatedCondition: Condition = {
      field: condition.field,
      op: condition.op,
      value,
    };
    onChange(updatedCondition);
  };

  const field = useMemo(() => {
    return find(fields, { fieldCode: condition.field.code });
  }, [fields, condition.field.code]);

  const operationOptions = useMemo(() => {
    return field ? getOptions(field.type) : [];
  }, [field]);

  const showValueInput = useMemo(() => {
    return !(condition.op === 'empty' || condition.op === 'notEmpty');
  }, [condition.op]);

  const tagInput = useMemo(() => {
    // 当类型是 array 且没有children，或者是 text 且操作是 belongTo/notBelongTo 时使用多值输入框
    const field = find(fields, { fieldCode: condition.field.code });
    return (
      (condition.field.type === 'array' && (!field?.children || field.children.length === 0)) || 
      (condition.field.type === 'text' && (condition.op === 'belongTo' || condition.op === 'notBelongTo'))
    );
  }, [condition.field.type, condition.field.code, condition.op, fields]);

  const numberRangeInput = useMemo(() => {
    return (
      condition.field.type === 'number' && (condition.op === 'between' || condition.op === 'notBetween')
    );
  }, [condition.field.type, condition.op]);

  const numberInput = useMemo(() => {
    return condition.field.type === 'number';
  }, [condition.field.type]);

  const switchInput = useMemo(() => {
    return condition.field.type === 'boolean';
  }, [condition.field.type]);

  const conditionBuilderInput = useMemo(() => {
    // 只有当类型是array且有children时，才使用条件构建器
    const field = find(fields, { fieldCode: condition.field.code });
    return (condition.field.type === 'array' && field?.children && field.children.length > 0) || 
           condition.field.type === 'object';
  }, [condition.field.type, condition.field.code, fields]);
  
  // 添加删除弹出框的状态管理
  const [deletePopoverOpen, setDeletePopoverOpen] = useState(false);
  
  // 处理删除条件的函数
  const handleDeleteCondition = () => {
    onDeleteCondition(index);
    setDeletePopoverOpen(false);
  };

  return (
    <Card className="condition-item bg-primary/5 w-full py-2">
      <CardContent className="p-2">
        <div className="grid grid-cols-12 gap-4">
          <div className="col-span-11">
            <div className="flex items-center gap-4">
              <div className="w-[180px]">
                <Select 
                  value={condition.field.code} 
                  onValueChange={fieldChange}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="请选择字段" />
                  </SelectTrigger>
                  <SelectContent>
                    {fields.map((field) => (
                      <SelectItem key={field.fieldCode} value={field.fieldCode}>
                        {field.fieldName}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              
              {condition.field.code && (
                <div className="w-[180px]">
                  <Select 
                    value={condition.op} 
                    onValueChange={operationChange}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="请选择操作" />
                    </SelectTrigger>
                    <SelectContent>
                      {operationOptions.map((op) => (
                        <SelectItem key={op.value as string} value={op.value as string}>
                          {op.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              )}
              
              {condition.op && showValueInput && (
                <div className="flex-1">
                  {tagInput && (
                    <div className="flex flex-wrap gap-2">
                      <Input
                        type="text"
                        value={Array.isArray(condition.value) ? condition.value.join(',') : condition.value}
                        onChange={(e) => {
                          const values = e.target.value.split(',');
                          handleValueChange(values);
                        }}
                        placeholder="请输入多个值，用逗号分隔"
                      />
                    </div>
                  )}
                  
                  {numberRangeInput && (
                    <div className="grid grid-cols-2 gap-2">
                      <Input
                        type="number"
                        value={Array.isArray(condition.value) ? condition.value[0] : ''}
                        onChange={(e) => {
                          const value = [...(Array.isArray(condition.value) ? condition.value : [null, null])];
                          value[0] = e.target.value ? Number(e.target.value) : null;
                          handleValueChange(value);
                        }}
                        placeholder="最小值"
                      />
                      <Input
                        type="number"
                        value={Array.isArray(condition.value) ? condition.value[1] : ''}
                        onChange={(e) => {
                          const value = [...(Array.isArray(condition.value) ? condition.value : [null, null])];
                          value[1] = e.target.value ? Number(e.target.value) : null;
                          handleValueChange(value);
                        }}
                        placeholder="最大值"
                      />
                    </div>
                  )}
                  
                  {numberInput && !numberRangeInput && (
                    <Input
                      type="number"
                      value={condition.value}
                      onChange={(e) => handleValueChange(e.target.value ? Number(e.target.value) : '')}
                    />
                  )}
                  
                  {switchInput && (
                    <div className="flex items-center space-x-2">
                      <Button 
                        variant={condition.value ? "default" : "outline"}
                        className="w-16"
                        onClick={() => handleValueChange(true)}
                      >
                        是
                      </Button>
                      <Button 
                        variant={!condition.value ? "default" : "outline"}
                        className="w-16"
                        onClick={() => handleValueChange(false)}
                      >
                        否
                      </Button>
                    </div>
                  )}
                  
                  {conditionBuilderInput && (
                    <Terms
                      group={condition.value}
                      root={true}
                      fields={field?.children || []}
                      onChange={handleValueChange}
                    />
                  )}
                  
                  {!tagInput && !numberRangeInput && !numberInput && !switchInput && !conditionBuilderInput && (
                    <Input
                      value={condition.value}
                      onChange={(e) => handleValueChange(e.target.value)}
                    />
                  )}
                </div>
              )}
            </div>
          </div>
          
          <div className="col-span-1 flex items-center justify-center">
            <Popover open={deletePopoverOpen} onOpenChange={setDeletePopoverOpen}>
              <PopoverTrigger asChild>
                <Button variant="destructive" size="icon">
                  <Trash2 className="h-4 w-4" />
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-4">
                <div className="flex flex-col gap-4">
                  <p>确认删除吗</p>
                  <Button 
                    variant="destructive" 
                    onClick={handleDeleteCondition}
                  >
                    确认
                  </Button>
                </div>
              </PopoverContent>
            </Popover>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
