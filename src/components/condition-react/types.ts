export type Option={
  value:string,
  label:string
}

export type OperatorType =
  | 'equal'
  | 'notEqual'
  | 'notEmpty'
  | 'empty'
  | 'contains'
  | 'notContains'
  | 'belongTo'
  | 'notBelongTo'
  | 'startWith'
  | 'endWith'
  | 'less'
  | 'lessOrEqual'
  | 'greater'
  | 'greaterOrEqual'
  | 'between'
  | 'notBetween'
  | 'match'
  | 'any'
  | 'all';

export type FieldTypes = 'text' | 'number' | 'boolean' | 'date' | 'time' | 'datetime' | 'select' | 'array' | 'object';

interface BaseField {
  type: FieldTypes;
  fieldName: string;
  fieldCode: string;
  operators?: Array<OperatorType>;
  placeholder?: string;
  children: Field[];
}

interface TextField extends BaseField {
  type: 'text';
  minLength?: number;
  maxLength?: number;
}

interface NumberField extends BaseField {
  type: 'number';
  maximum?: number;
  minimum?: number;
  step?: number;
  precision?: number;
}

interface DateField extends BaseField {
  type: 'date';
  format?: string;
  inputFormat?: string;
  minDate?: any;
  maxDate?: any;
}

interface TimeField extends BaseField {
  type: 'time';
  minTime?: any;
  maxTime?: any;
  format?: string;
  inputFormat?: string;
}

interface DatetimeField extends BaseField {
  type: 'datetime';
  format?: string;
  inputFormat?: string;
  timeFormat?: string;
}

interface SelectField extends BaseField {
  type: 'select';
  multiple?: boolean;
  options?: Array<Option>;
}

interface BooleanField extends BaseField {
  type: 'boolean';
}

interface ArrayField extends BaseField {
  type: 'array';
}

interface ObjectField extends BaseField {
  type: 'object';
}

export type Field =
  | TextField
  | NumberField
  | ObjectField
  | DateField
  | TimeField
  | DatetimeField
  | SelectField
  | BooleanField
  | ArrayField;

export interface Condition {
  field: {
    type: FieldTypes;
    code: string;
  };
  op: OperatorType | '';
  value: any;
}

export interface Group {
  conditions: Condition[];
  groups: Group[];
  logic: 'AND' | 'OR';
}

export type TermType = 'GROUP' | 'CONDITION';
