'use client';

import { Button } from "@/components/ui/button";
import { useState, useEffect } from "react";

interface LogicSwitchProps {
  value: 'AND' | 'OR';
  onChange: (value: 'AND' | 'OR') => void;
}

export default function TermLogic({ value = 'AND', onChange }: LogicSwitchProps) {
  const [logic, setLogic] = useState<'AND' | 'OR'>(value);

  useEffect(() => {
    setLogic(value);
  }, [value]);

  const warpLogic = logic === 'AND' ? '且' : '或';

  const switchLogic = () => {
    const newLogic = logic === 'AND' ? 'OR' : 'AND';
    setLogic(newLogic);
    onChange(newLogic);
  };

  return (
    <div className="logic">
      <Button variant="outline" size="icon" onClick={switchLogic}>
        {warpLogic}
      </Button>
    </div>
  );
}
