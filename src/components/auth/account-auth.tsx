import React, {useEffect} from 'react';
import {OrgTree} from './org-tree';
import {useRequest} from 'umi';
import {fetchAuthorizationAccount} from '@/service/authorization';
import {Account, fetchAccounts} from '@/service/account';
import {DataTable} from "@/components/data-table/data-table";
import {DataTableToolbar} from "@/components/data-table/data-table-toolbar";
import {useLocalDataTable} from "@/components/data-table/hooks/use-local-data-table";
import {DataTableFilterField} from "@/components/data-table/types";
import {ColumnDef, Row} from "@tanstack/react-table";
import {DataTableColumnHeader} from "@/components/data-table/data-table-column-header";
import {Button} from "@/components/ui/button";
import {Plus} from "lucide-react";
import {Page} from "@/types";

interface AccountAuthProps {
  resourceId?: string;
  resourceType?: string;
  onSelectedIdsChange?: (selectedIds: string[]) => void;
}

// 移除不再需要的 ref 接口

const getColumns = (onAdd?: (account: Account) => void): ColumnDef<Account>[] => {
  return [
    {
      accessorKey: "code",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="工号" />
      ),
      cell: ({ row }: { row: Row<Account> }) => <div className="w-[80px] truncate">{row.getValue("code")}</div>,
      size: 80,
    },
    {
      accessorKey: "name",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="姓名" />
      ),
      cell: ({ row }: { row: Row<Account> }) => <div className="w-[160px] truncate">{row.getValue("name")}</div>,
      size: 160,
    },
    ...(onAdd ? [{
      id: "actions",
      cell: ({ row }: { row: Row<Account> }) => {
        return (
          <Button
            variant="ghost"
            size="icon"
            onClick={() => onAdd(row.original)}
          >
            <Plus className="h-4 w-4" />
          </Button>
        );
      },
      size: 50,
    }] : []),
  ];
};

export const AccountAuth = ({ resourceId, resourceType, onSelectedIdsChange }: AccountAuthProps) => {
  const [selectedOrgId, setSelectedOrgId] = React.useState<string | undefined>(undefined);
  const [searchKeyword, setSearchKeyword] = React.useState('');
  const [page, setPage] = React.useState(0);
  const [perPage, setPerPage] = React.useState(20);
  const [accounts, setAccounts] = React.useState<Page<Account>>({
    page: 0,
    pageSize: 20,
    total: 0,
    data: [],
  });
  const [authorizedAccounts, setAuthorizedAccounts] = React.useState<Account[]>([]);

  const handleAddAccount = (account: Account) => {
    setAuthorizedAccounts(prev => {
      if (prev.some(a => a.id === account.id)) {
        return prev;
      }
      return [...prev, account];
    });
  };

  const handleRemoveAccount = (accountId: string) => {
    setAuthorizedAccounts(prev => prev.filter(a => a.id !== accountId));
  };

  const columnsWithAdd = React.useMemo(() => getColumns(handleAddAccount), []);
  const columnsWithRemove = React.useMemo(() => [
    ...getColumns(),
    {
      id: "actions",
      cell: ({ row }: { row: { original: Account } }) => {
        return (
          <Button
            variant="ghost"
            size="icon"
            onClick={() => handleRemoveAccount(row.original.id)}
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
              stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"
              className="h-4 w-4">
              <path d="M3 6h18" />
              <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6" />
              <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2" />
            </svg>
          </Button>
        );
      },
    }
  ], []);

  const filterFields: DataTableFilterField<Account>[] = [
    {
      id: "name",
      label: "姓名",
      placeholder: "搜索人员姓名",
    },
  ];

  const {table: allAccountsTable} = useLocalDataTable({
    pageCount: accounts.total / perPage,
    data: accounts.data,
    columns: columnsWithAdd,
    filterFields,
    initialState: {
      pagination: {
        pageIndex: page,
        pageSize: perPage,
      },
    },
    getRowId: (originalRow: Account) => originalRow.id,
    onPaginationChange: (updaterOrValue) => {
      const newState = typeof updaterOrValue === 'function'
          ? updaterOrValue({pageIndex: page, pageSize: perPage})
          : updaterOrValue;
      setPage(newState.pageIndex);
      setPerPage(newState.pageSize);
    },
  });

  const { table: authorizedTable } = useLocalDataTable({
    data: authorizedAccounts,
    columns: columnsWithRemove,
    pageCount: 1,
    getRowId: (originalRow: Account) => originalRow.id,
  });

  // 获取所有用户列表
  useRequest(
      () => {
        const username = allAccountsTable.getColumn("name")?.getFilterValue() as string;
        return fetchAccounts({
          page: page,
          pageSize: perPage,
          specification: {
            realName: username,
            orgId: selectedOrgId,
          }
        })
      },
      {
        refreshDeps: [page, perPage, allAccountsTable.getState().columnFilters,selectedOrgId],
        onSuccess: (res) => {
          setAccounts(res);
        }
      }
  );

  // 获取授权账号
  useRequest(
      () => {
        if (resourceId) {
          return fetchAuthorizationAccount(resourceId);
        }
        return Promise.resolve({ data: [] });
      },
      {
        onSuccess: (res) => {
          setAuthorizedAccounts(res || []);
        },
        refreshDeps: [resourceId]
      }
  );

  // 当授权账户列表变化时，通知父组件
  useEffect(() => {
    if (onSelectedIdsChange) {
      onSelectedIdsChange(authorizedAccounts.map(account => account.code));
    }
  }, [authorizedAccounts, onSelectedIdsChange]);

  return (
    <div className="flex h-full">
      <div className="w-[300px] border-r pr-4">
        <OrgTree
          selectedOrgId={selectedOrgId}
          onSelect={setSelectedOrgId}
        />
      </div>
      <div className="flex flex-1 gap-4 pl-4">
        <div className="flex-1 min-w-0">
          <h3 className="mb-4 text-lg font-medium">待选用户</h3>
          <div className="overflow-hidden">
            <DataTable table={allAccountsTable}>
              <DataTableToolbar table={allAccountsTable} filterFields={filterFields} />
            </DataTable>
          </div>
        </div>
        <div className="flex-1 min-w-0">
          <h3 className="mb-4 text-lg font-medium">已授权用户 ({authorizedAccounts.length})</h3>
          <div className="overflow-hidden">
            <DataTable table={authorizedTable} showPagination={false}>
              <DataTableToolbar table={authorizedTable} />
            </DataTable>
          </div>
        </div>
      </div>
    </div>
  );
} 