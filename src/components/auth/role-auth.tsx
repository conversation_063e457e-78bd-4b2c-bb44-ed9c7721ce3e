import React, {useEffect, useState} from 'react';
import {DataTable} from "@/components/data-table/data-table";
import {DataTableToolbar} from "@/components/data-table/data-table-toolbar";
import {useLocalDataTable} from "@/components/data-table/hooks/use-local-data-table";
import {DataTableFilterField} from "@/components/data-table/types";
import {ColumnDef} from "@tanstack/react-table";
import {Button} from "@/components/ui/button";
import {DataTableColumnHeader} from "@/components/data-table/data-table-column-header";
import {useRequest} from 'umi';
import {Plus, Trash2} from "lucide-react";
import {Page} from "@/types";
import {fetchRoles, Role} from "@/service/role";
import {AuthBaseProps} from './types';
import {fetchAuthorizationRole} from "@/service/authorization";

interface RoleAuthData {
    selectedIds: string[];
    aclData: any[];
}

interface RoleAuthProps extends AuthBaseProps {
    onChange?: (data: RoleAuthData) => void;
}

export const RoleAuth = ({ resourceId, onChange }: RoleAuthProps) => {
    const [page, setPage] = useState(0);
    const [perPage, setPerPage] = useState(20);
    const [roles, setRoles] = useState<Page<Role>>({
        page: 0,
        pageSize: 20,
        total: 0,
        data: [],
    });
    const [authorizedRoles, setAuthorizedRoles] = useState<Role[]>([]);


    const filterFields: DataTableFilterField<Role>[] = [
        {
            id: 'name',
            label: '角色名称',
            placeholder: '请输入角色名称',
        },
        {
            id: 'code',
            label: '角色编码',
            placeholder: '请输入角色编码',
        }
    ];

    const columns: ColumnDef<Role>[] = [
        {
            accessorKey: "name",
            header: ({ column }) => (
                <DataTableColumnHeader column={column} title="角色名称"/>
            ),
        },
        {
            accessorKey: "code",
            header: ({ column }) => (
                <DataTableColumnHeader column={column} title="角色编码"/>
            ),
        },
        {
            id: "actions",
            cell: ({ row }) => {
                const role = row.original;
                const isAuthorized = authorizedRoles.some(r => r.id === role.id);

                return (
                    <div className="flex items-center gap-2">
                        <Button
                            variant="ghost"
                            className="h-8 w-8 p-0"
                            onClick={() => handleAddRole(role)}
                            disabled={isAuthorized}
                        >
                            <Plus className="h-4 w-4"/>
                        </Button>
                    </div>
                );
            },
        },
    ];

    const authorizedColumns: ColumnDef<Role>[] = [
        {
            accessorKey: "name",
            header: ({ column }) => (
                <DataTableColumnHeader column={column} title="角色名称"/>
            ),
        },
        {
            accessorKey: "code",
            header: ({ column }) => (
                <DataTableColumnHeader column={column} title="角色编码"/>
            ),
        },
        {
            id: "actions",
            cell: ({ row }) => {
                const role = row.original;

                return (
                    <div className="flex items-center gap-2">
                        <Button
                            variant="ghost"
                            className="h-8 w-8 p-0"
                            onClick={() => handleRemoveRole(role.id)}
                        >
                            <Trash2 className="h-4 w-4"/>
                        </Button>
                    </div>
                );
            },
        },
    ];

    const {table:allRolesTable} = useLocalDataTable({
        pageCount:roles.total / perPage,
        data: roles.data,
        columns,
        filterFields,
        getRowId: (originalRow: Role) => originalRow.code,
        onPaginationChange: (updaterOrValue) => {
            const newState = typeof updaterOrValue === 'function'
                ? updaterOrValue({pageIndex: page, pageSize: perPage})
                : updaterOrValue;
            setPage(newState.pageIndex);
            setPerPage(newState.pageSize);
        },
    });

    const {table:authorizedTable} = useLocalDataTable({
        pageCount:-1,
        data: authorizedRoles,
        columns: authorizedColumns,
        filterFields,
    });

    // 获取所有角色列表
    useRequest(() =>  {
        const code = allRolesTable.getColumn("code")?.getFilterValue() as string;
        const name = allRolesTable.getColumn("name")?.getFilterValue() as string;
        return fetchRoles({
            page,
            pageSize: perPage,
            specification: {
                code,
                name
            }
        })},
        {
            refreshDeps: [page, perPage,allRolesTable.getState().columnFilters],
            debounceInterval: 500,
            onSuccess: (res) => {
                setRoles(res);
            }
        }
    );
    useRequest(()=>{
        return  fetchAuthorizationRole(resourceId);
    },{
        onSuccess: (res) => {
            setAuthorizedRoles(res);
        }
    })

    const handleAddRole = (role: Role) => {
        setAuthorizedRoles(prev => {
            if (prev.some(r => r.id === role.id)) {
                return prev;
            }
            return [...prev, role];
        });
    };

    const handleRemoveRole = (roleId: string) => {
        setAuthorizedRoles(prev => prev.filter(r => r.id !== roleId));
    };



    // 当授权角色列表变化时，通知父组件
    useEffect(() => {
        if (onChange) {
            onChange({
                selectedIds: authorizedRoles.map(role => role.code),
                aclData: authorizedRoles.map(role => ({
                    id: role.id,
                    name: role.name,
                    type: 'ROLE'
                }))
            });
        }
    }, [authorizedRoles, onChange]);

    return (
        <div className="flex gap-4 h-full">
            <div className="flex-1 min-w-[600px]">
                <h3 className="mb-4 text-lg font-medium">待选角色</h3>
                <div className="overflow-hidden">
                    <DataTable table={allRolesTable}>
                        <DataTableToolbar table={allRolesTable} filterFields={filterFields}/>
                    </DataTable>
                </div>
            </div>
            <div className="flex-1 min-w-[600px]">
                <h3 className="mb-4 text-lg font-medium">已授权角色 ({authorizedRoles.length})</h3>
                <div className="overflow-hidden">
                    <DataTable table={authorizedTable} showPagination={false}>
                        <DataTableToolbar table={authorizedTable}/>
                    </DataTable>
                </div>
            </div>
        </div>
    );
};