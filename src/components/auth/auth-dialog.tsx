import React, {useCallback, useState} from 'react';
import {But<PERSON>} from "@/components/ui/button";
import {Sheet, SheetContent, SheetDescription, SheetHeader, SheetTitle,} from "@/components/ui/sheet";
import {OrgAuth} from './org-auth';
import {AccountAuth} from './account-auth';
import {RoleAuth} from './role-auth';
import {GroupAuth} from './group-auth';
import {AuthBaseProps} from './types';
import {cn} from "@/lib/utils";
import {useRequest} from 'umi';
import {ACLAuthorization, aclAuthorization, PolicyType, StrategyType} from '@/service/authorization';
import {toast} from "sonner";

interface AuthDialogProps extends AuthBaseProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

type AuthType = 'org' | 'user' | 'role' | 'group';

export function AuthDialog({ 
  open, 
  onOpenChange,
  resourceId,
  resourceType,
  onSuccess
}: AuthDialogProps) {
  const [activeTab, setActiveTab] = useState<AuthType>("org");
  const [orgAuthData, setOrgAuthData] = useState<{ selectedIds: string[], aclData: any[] }>({ selectedIds: [], aclData: [] });
  const [accountAuthData, setAccountAuthData] = useState<{ selectedIds: string[] }>({ selectedIds: [] });
  const [roleAuthData, setRoleAuthData] = useState<{ selectedIds: string[] }>({ selectedIds: [] });
  const [groupAuthData, setGroupAuthData] = useState<{ selectedIds: string[] }>({ selectedIds: [] });
  
  // 使用 useCallback 包装回调函数，避免不必要的重新创建
  const handleOrgAuthChange = useCallback((data: { selectedIds: string[], aclData: any[] }) => {
    setOrgAuthData(data);
  }, []);
  
  const handleAccountAuthChange = useCallback((selectedIds: string[]) => {
    setAccountAuthData({ selectedIds });
  }, []);
  
  const handleRoleAuthChange = useCallback((data: { selectedIds: string[], aclData: any[] }) => {
    setRoleAuthData(data);
  }, []);
  
  const handleGroupAuthChange = useCallback((data: { selectedIds: string[], aclData: any[] }) => {
    setGroupAuthData(data);
  }, []);

  const {run: submitAuthorization } = useRequest(
    (data:ACLAuthorization) => aclAuthorization(data),
    {
      manual: true,
      onSuccess: () => {
        toast.success("授权成功");
        onOpenChange(false);
        onSuccess?.();
      },
      onError: (error) => {
        toast.error("授权失败：" + error.message);
      }
    }
  );

  const handleSubmit = async () => {
    let selectedIds: string[] = [];
    let policyType: PolicyType;
    let strategyType: StrategyType = 'ROOT';
    
    switch (activeTab) {
      case 'org':
        selectedIds = orgAuthData.selectedIds;
        policyType = 'ORGANIZATION';
        break;
      case 'user':
        selectedIds = accountAuthData.selectedIds;
        policyType = 'USER';
        break;
      case 'role':
        selectedIds = roleAuthData.selectedIds;
        policyType = 'ROLE';
        break;
      case 'group':
        selectedIds = groupAuthData.selectedIds;
        policyType = 'GROUP';
        break;
      default:
        return;
    }

    if (selectedIds.length === 0) return;

    await submitAuthorization({
      resourceId,
      resource: resourceType,
      policies: [
        ...selectedIds.map(id => ({
          policyType,
          strategyType,
          value: id,
        })),
      ]
    });
  };

  const renderContent = () => {
    switch (activeTab) {
      case 'org':
        return (
          <OrgAuth
            resourceId={resourceId}
            resourceType={resourceType}
            onChange={handleOrgAuthChange}
          />
        );
      case 'user':
        return (
          <div className="flex h-full">
            <div className="flex-1 pl-6">
              <AccountAuth
                resourceId={resourceId}
                resourceType={resourceType}
                onSelectedIdsChange={handleAccountAuthChange}
              />
            </div>
          </div>
        );
      case 'role':
        return (
          <RoleAuth
            resourceId={resourceId}
            resourceType={resourceType}
            onChange={handleRoleAuthChange}
            onSuccess={onSuccess}
          />
        );
      case 'group':
        return (
          <GroupAuth
            resourceId={resourceId}
            resourceType={resourceType}
            onChange={handleGroupAuthChange}
            onSuccess={onSuccess}
          />
        );
      default:
        return null;
    }
  };

  return (
    <Sheet open={open} onOpenChange={onOpenChange}>
      <SheetContent className="sm:min-w-full">
        <SheetHeader>
          <SheetDescription/>
          <SheetTitle>授权管理</SheetTitle>
        </SheetHeader>
        <div className=" flex flex-col h-full">
          <div className="flex space-x-2 border-b">
            <button
              className={cn(
                "px-4 py-2 text-sm font-medium transition-colors",
                activeTab === "org"
                  ? "border-b-2 border-primary text-primary"
                  : "text-muted-foreground hover:text-primary"
              )}
              onClick={() => setActiveTab("org")}
            >
              组织授权
            </button>
            <button
              className={cn(
                "px-4 py-2 text-sm font-medium transition-colors",
                activeTab === "user"
                  ? "border-b-2 border-primary text-primary"
                  : "text-muted-foreground hover:text-primary"
              )}
              onClick={() => setActiveTab("user")}
            >
              人员授权
            </button>
            <button
              className={cn(
                "px-4 py-2 text-sm font-medium transition-colors",
                activeTab === "role"
                  ? "border-b-2 border-primary text-primary"
                  : "text-muted-foreground hover:text-primary"
              )}
              onClick={() => setActiveTab("role")}
            >
              角色授权
            </button>
            <button
              className={cn(
                "px-4 py-2 text-sm font-medium transition-colors",
                activeTab === "group"
                  ? "border-b-2 border-primary text-primary"
                  : "text-muted-foreground hover:text-primary"
              )}
              onClick={() => setActiveTab("group")}
            >
              用户组授权
            </button>
          </div>

          <div className="flex-1 overflow-hidden">
            {renderContent()}
          </div>
        </div>

        <div className="flex justify-end space-x-2 m-4 mt-0">
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            取消
          </Button>
          <Button onClick={handleSubmit} >
            确定
          </Button>
        </div>
      </SheetContent>
    </Sheet>
  );
} 