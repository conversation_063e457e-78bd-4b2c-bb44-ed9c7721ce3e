import React, {useState} from 'react';
import {ChevronDown, ChevronRight} from "lucide-react";
import {Button} from "@/components/ui/button";
import {useRequest} from 'umi';
import {fetchOrgTree, Org} from '@/service/org';
import {cn} from '@/lib/utils';

interface OrgTreeProps {
  selectedOrgId: string | undefined;
  onSelect: (orgId: string) => void;
}

interface OrgNodeProps extends OrgTreeProps {
  org: Org;
  level: number;
  onUpdateChildren: (orgId: string, children: Org[]) => void;
}

const OrgNode: React.FC<OrgNodeProps> = ({ org, level, selectedOrgId, onSelect, onUpdateChildren }) => {
  const [isExpanded, setIsExpanded] = useState(false);

  const { loading, run } = useRequest((id: string) => fetchOrgTree(id), {
    manual: true,
    onSuccess: (res) => {
      onUpdateChildren(org.id, res);
      setIsExpanded(true);
    }
  });

  const handleExpand = async () => {
    if (!isExpanded && (!org.children || org.children.length === 0)) {
      await run(org.id);
    } else {
      setIsExpanded(!isExpanded);
    }
  };

  return (
    <div>
      <div 
        className={cn(
          "flex items-center py-2 px-2 hover:bg-accent cursor-pointer",
          selectedOrgId === org.id && "bg-accent"
        )}
        style={{ paddingLeft: `${level * 16}px` }}
      >
        <Button
          variant="ghost"
          size="icon"
          className="h-6 w-6"
          onClick={handleExpand}
          disabled={loading}
        >
          {isExpanded ? (
            <ChevronDown className="h-4 w-4" />
          ) : (
            <ChevronRight className="h-4 w-4" />
          )}
        </Button>
        <div 
          className="ml-2 truncate"
          onClick={() => onSelect(org.id)}
        >
          {org.name}
        </div>
      </div>
      {isExpanded && org.children?.map(child => (
        <OrgNode
          key={child.id}
          org={child}
          level={level + 1}
          selectedOrgId={selectedOrgId}
          onSelect={onSelect}
          onUpdateChildren={onUpdateChildren}
        />
      ))}
    </div>
  );
};

export const OrgTree: React.FC<OrgTreeProps> = ({ selectedOrgId, onSelect }) => {
  const [rootOrg, setRootOrg] = useState<Org | null>(null);

  const handleUpdateChildren = (orgId: string, children: Org[]) => {
    if (!rootOrg) return;

    const updateOrgChildren = (org: Org): Org => {
      if (org.id === orgId) {
        return { ...org, children };
      }
      if (org.children) {
        return {
          ...org,
          children: org.children.map(child => updateOrgChildren(child))
        };
      }
      return org;
    };

    setRootOrg(updateOrgChildren(rootOrg));
  };

  const { loading } = useRequest(() => fetchOrgTree(undefined), {
    onSuccess: (res) => {
      setRootOrg(res[0]);
    }
  });

  if (loading || !rootOrg) {
    return <div className="p-4">加载中...</div>;
  }

  return (
    <div className="w-64 border-r overflow-auto">
      <OrgNode
        org={rootOrg}
        level={0}
        selectedOrgId={selectedOrgId}
        onSelect={onSelect}
        onUpdateChildren={handleUpdateChildren}
      />
    </div>
  );
}; 