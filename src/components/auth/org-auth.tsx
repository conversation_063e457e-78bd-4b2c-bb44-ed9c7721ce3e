import React, {useEffect} from 'react';
import {OrgTree} from './org-tree';
import {useRequest} from 'umi';
import {ACL, fetchAuthorizationOrg} from '@/service/authorization';
import {fetchOrgPage, Org} from '@/service/org';
import {DataTable} from "@/components/data-table/data-table";
import {DataTableToolbar} from "@/components/data-table/data-table-toolbar";
import {useLocalDataTable} from "@/components/data-table/hooks/use-local-data-table";
import {DataTableFilterField} from "@/components/data-table/types";
import {ColumnDef, Row} from "@tanstack/react-table";
import {Button} from "@/components/ui/button";
import {DataTableColumnHeader} from "@/components/data-table/data-table-column-header";
import {Plus} from "lucide-react";
import {Page} from "@/types";

interface OrgAuthData {
  selectedIds: string[];
  aclData: ACL[];
}

interface OrgAuthProps {
  resourceId: string;
  resourceType: string;
  onChange?: (data: OrgAuthData) => void;
}

// 移除不再需要的 ref 接口

const getColumns = (onAdd?: (org: Org) => void): ColumnDef<Org>[] => {
  return [
    {
      accessorKey: "code",
      header: ({column}) => (
          <DataTableColumnHeader column={column} title="编码"/>
      ),
      cell: ({row}: { row: Row<Org> }) => <div className="w-[80px] truncate">{row.getValue("code")}</div>,
      size: 80,
    },
    {
      accessorKey: "name",
      header: ({column}) => (
          <DataTableColumnHeader column={column} title="名称"/>
      ),
      cell: ({row}: { row: Row<Org> }) => <div className="w-[160px] truncate">{row.getValue("name")}</div>,
      size: 160,
    },
    ...(onAdd ? [{
      id: "actions",
      cell: ({row}: { row: Row<Org> }) => {
        return (
            <Button
                variant="ghost"
                size="icon"
                onClick={() => onAdd(row.original)}
            >
              <Plus className="h-4 w-4"/>
            </Button>
        )
      },
      size: 50,
    }] : []),
  ];
};

export const OrgAuth = ({resourceId, resourceType, onChange}: OrgAuthProps) => {
  const [selectedOrgId, setSelectedOrgId] = React.useState<string | undefined>(undefined);
  const [page, setPage] = React.useState(0);
  const [perPage, setPerPage] = React.useState(20);
  const [orgs, setOrgs] = React.useState<Page<Org>>({
    page: 0,
    pageSize: 20,
    total: 0,
    data: [],
  });
  const [authorizedOrgs, setAuthorizedOrgs] = React.useState<Org[]>([]);

  const handleAddOrg = (org: Org) => {
    setAuthorizedOrgs(prev => {
      // 检查是否已经存在
      if (prev.some(o => o.id === org.id)) {
        return prev;
      }
      return [...prev, org];
    });
  };

  const handleRemoveOrg = (orgId: string) => {
    setAuthorizedOrgs(prev => prev.filter(o => o.id !== orgId));
  };

  const columnsWithAdd = React.useMemo(() => getColumns(handleAddOrg), []);
  const columnsWithRemove = React.useMemo(() => [
    ...getColumns(),
    {
      id: "actions",
      cell: ({row}: { row: { original: Org } }) => {
        return (
            <Button
                variant="ghost"
                size="icon"
                onClick={() => handleRemoveOrg(row.original.id)}
            >
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                   stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"
                   className="h-4 w-4">
                <path d="M3 6h18"/>
                <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"/>
                <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"/>
              </svg>
            </Button>
        )
      },
    }
  ], []);

  const filterFields: DataTableFilterField<Org>[] = [
    {
      id: "name",
      label: "名称",
      placeholder: "搜索组织名称",
    },
    {
      id: "code",
      label: "编码",
      placeholder: "搜索组织编码",
    },
  ];

  // 左侧表格 - 所有组织
  const {table: allOrgsTable} = useLocalDataTable({
    pageCount: orgs.total / perPage,
    data: orgs.data,
    columns: columnsWithAdd,
    filterFields,
    initialState: {
      pagination: {
        pageIndex: page,
        pageSize: perPage,
      },
    },
    getRowId: (originalRow: Org) => originalRow.id,
    onPaginationChange: (updaterOrValue) => {
      const newState = typeof updaterOrValue === 'function'
          ? updaterOrValue({pageIndex: page, pageSize: perPage})
          : updaterOrValue;
      setPage(newState.pageIndex);
      setPerPage(newState.pageSize);
    }
  });

  // 右侧表格 - 已授权组织
  const {table: authorizedTable} = useLocalDataTable({
    data: authorizedOrgs,
    columns: columnsWithRemove,
    pageCount: 1,
    getRowId: (originalRow: Org) => originalRow.id,
  });

  // 获取已经授权的组织
  useRequest(
      () => fetchAuthorizationOrg(resourceId),
      {
        refreshDeps: [page, perPage, selectedOrgId],
        onSuccess: (res) => {
          setAuthorizedOrgs(res)
        }
      }
  );



  // 获取全部组织列表
  const {data: aclData} = useRequest(
      () => {
        const orgName = allOrgsTable.getColumn("name")?.getFilterValue() as string;
        const orgCode = allOrgsTable.getColumn("code")?.getFilterValue() as string;
        return fetchOrgPage({
        page: page,
        pageSize: perPage,
        specification:{
          parentId: selectedOrgId,
          name: orgName,
          code: orgCode,
        }
      })},
      {
        refreshDeps: [page, perPage, allOrgsTable.getState().columnFilters,selectedOrgId,],
        onSuccess: (res) => {
          setOrgs(res);
        }
      }
  );

  // 当授权组织列表变化时，通知父组件
  useEffect(() => {
    if (onChange) {
      onChange({
        selectedIds: authorizedOrgs.map(org => org.id),
        aclData: []
      });
    }
  }, [authorizedOrgs, onChange]);

  return (
      <div className="flex h-full">
        <div className="w-[300px] border-r pr-4">
          <OrgTree
              selectedOrgId={selectedOrgId}
              onSelect={setSelectedOrgId}
          />
        </div>
        <div className="flex flex-1 gap-4 pl-4">
          <div className="flex-1 min-w-0">
            <h3 className="mb-4 text-lg font-medium">待选组织</h3>
            <div className="overflow-hidden">
              <DataTable table={allOrgsTable}>
                <DataTableToolbar table={allOrgsTable} filterFields={filterFields}/>
              </DataTable>
            </div>
          </div>
          <div className="flex-1 min-w-0">
            <h3 className="mb-4 text-lg font-medium">已授权组织 ({authorizedOrgs.length})</h3>
            <div className="overflow-hidden">
              <DataTable table={authorizedTable} showPagination={false}>
                <DataTableToolbar table={authorizedTable}/>
              </DataTable>
            </div>
          </div>
        </div>
      </div>
  );
};