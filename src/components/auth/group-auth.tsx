import React, {useEffect, useState} from 'react';
import {DataTable} from "@/components/data-table/data-table";
import {DataTableToolbar} from "@/components/data-table/data-table-toolbar";
import {useLocalDataTable} from "@/components/data-table/hooks/use-local-data-table";
import {DataTableFilterField} from "@/components/data-table/types";
import {ColumnDef} from "@tanstack/react-table";
import {Button} from "@/components/ui/button";
import {DataTableColumnHeader} from "@/components/data-table/data-table-column-header";
import {useRequest} from 'umi';
import {Plus, Trash2} from "lucide-react";
import {Page} from "@/types";
import {fetchGroups, Group} from "@/service/group";
import {AuthBaseProps} from './types';
import {fetchAuthorizationGroup} from "@/service/authorization";

interface GroupAuthData {
    selectedIds: string[];
    aclData: any[];
}

interface GroupAuthProps extends AuthBaseProps {
    onChange?: (data: GroupAuthData) => void;
}

export const GroupAuth = ({resourceId, onChange}: GroupAuthProps) => {
    const [page, setPage] = useState(0);
    const [perPage, setPerPage] = useState(20);
    const [groups, setGroups] = useState<Page<Group>>({
        page: 0,
        pageSize: 20,
        total: 0,
        data: [],
    });
    const [authorizedGroups, setAuthorizedGroups] = useState<Group[]>([]);


    const filterFields: DataTableFilterField<Group>[] = [
        {
            id: 'name',
            label: '用户组名称',
        },
        {
            id: 'code',
            label: '用户组编码',
        }
    ];

    const columns: ColumnDef<Group>[] = [
        {
            accessorKey: "name",
            header: ({column}) => (
                <DataTableColumnHeader column={column} title="用户组名称"/>
            ),
        },
        {
            accessorKey: "code",
            header: ({column}) => (
                <DataTableColumnHeader column={column} title="用户组编码"/>
            ),
        },
        {
            id: "actions",
            cell: ({row}) => {
                const group = row.original;
                const isAuthorized = authorizedGroups.some(g => g.id === group.id);

                return (
                    <div className="flex items-center gap-2">
                        <Button
                            variant="ghost"
                            className="h-8 w-8 p-0"
                            onClick={() => handleAddGroup(group)}
                            disabled={isAuthorized}
                        >
                            <Plus className="h-4 w-4"/>
                        </Button>
                    </div>
                );
            },
        },
    ];

    const authorizedColumns: ColumnDef<Group>[] = [
        {
            accessorKey: "name",
            header: ({column}) => (
                <DataTableColumnHeader column={column} title="用户组名称"/>
            ),
        },
        {
            accessorKey: "code",
            header: ({column}) => (
                <DataTableColumnHeader column={column} title="用户组编码"/>
            ),
        },
        {
            id: "actions",
            cell: ({row}) => {
                const group = row.original;

                return (
                    <div className="flex items-center gap-2">
                        <Button
                            variant="ghost"
                            className="h-8 w-8 p-0"
                            onClick={() => handleRemoveGroup(group.id)}
                        >
                            <Trash2 className="h-4 w-4"/>
                        </Button>
                    </div>
                );
            },
        },
    ];

    const {table: allGroupsTable} = useLocalDataTable({
            pageCount: groups.total / perPage,
            data: groups.data,
            columns,
            filterFields,
            initialState: {
                pagination: {
                    pageIndex: page,
                    pageSize: perPage,
                }
            },
            getRowId: (originalRow: Group) => originalRow.code,
            onPaginationChange: (updaterOrValue) => {
                const newState = typeof updaterOrValue === 'function'
                    ? updaterOrValue({pageIndex: page, pageSize: perPage})
                    : updaterOrValue;
                setPage(newState.pageIndex);
                setPerPage(newState.pageSize);
            }
        }
    );

    const {table: authorizedTable} = useLocalDataTable({
        pageCount: -1,
        data: authorizedGroups,
        columns: authorizedColumns,
    });

    // 获取所有用户组列表
    useRequest(
        () => {
            const code = allGroupsTable.getColumn("code")?.getFilterValue() as string;
            const name = allGroupsTable.getColumn("name")?.getFilterValue() as string;
            return fetchGroups({
                page,
                pageSize: perPage,
                specification: {
                    code,
                    name
                }
            })
        },
        {
            refreshDeps: [page, perPage, allGroupsTable.getState().columnFilters],
            debounceInterval: 500,
            onSuccess: (res) => {
                setGroups(res);
            }
        }
    );
    useRequest(
        () => fetchAuthorizationGroup(resourceId), {
            onSuccess: (res) => {
                setAuthorizedGroups(res);
            }
        }
    )

    const handleAddGroup = (group: Group) => {
        setAuthorizedGroups(prev => {
            if (prev.some(g => g.id === group.id)) {
                return prev;
            }
            return [...prev, group];
        });
    };

    const handleRemoveGroup = (groupId: string) => {
        setAuthorizedGroups(prev => prev.filter(g => g.id !== groupId));
    };


    // 当授权组列表变化时，通知父组件
    useEffect(() => {
        if (onChange) {
            onChange({
                selectedIds: authorizedGroups.map(group => group.code),
                aclData: authorizedGroups.map(group => ({
                    id: group.id,
                    name: group.name,
                    type: 'GROUP'
                }))
            });
        }
    }, [authorizedGroups, onChange]);

    return (
        <div className="flex gap-4 h-full">
            <div className="flex-1 min-w-[600px]">
                <h3 className="mb-4 text-lg font-medium">待选用户组</h3>
                <DataTable table={allGroupsTable}>
                    <DataTableToolbar table={allGroupsTable} filterFields={filterFields}/>
                </DataTable>
            </div>
            <div className="flex-1 min-w-[600px]">
                <h3 className="mb-4 text-lg font-medium">已授权用户组 ({authorizedGroups.length})</h3>
                <DataTable table={authorizedTable} showPagination={false}>
                    <DataTableToolbar table={authorizedTable}/>
                </DataTable>
            </div>
        </div>
    );
};