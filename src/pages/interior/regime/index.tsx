import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Plus } from "lucide-react";
import { useRequest, useAccess } from "umi";
import { InteriorRegime, deleteRegime, fetchRegimePage } from "@/service/interior/regime";
import { RegimeTable } from '@/pages/interior/regime/components/regime-table';
import { Page, PageQuery, Specification } from '@/types';
import { DataTableRowAction } from "@/components/data-table/types";
import { Sheet, SheetContent, SheetHeader, SheetTitle } from "@/components/ui/sheet";
import { RegimeForm } from '@/pages/interior/regime/components/regime-form';

export default function NavigationPage() {
  const [pageData, setPageData] = useState<Page<InteriorRegime>>();
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [editingItem, setEditingItem] = useState<InteriorRegime>();
  const access = useAccess();
  const [rowAction, setRowActionState] = useState<DataTableRowAction<InteriorRegime> | null>(null);

  const { run: deleteItem } = useRequest(deleteRegime, {
    manual: true,
    onSuccess: () => {
      // 刷新数据
      handleQuery({
        pageSize: 10,
        page: 0,
        specification: {}
      });
    },
  });

  const handleQuery = async (query: PageQuery<Specification<InteriorRegime>>) => {
    const response = await fetchRegimePage(query);
    setPageData(response.data);
    return response.data;
  };



  // 监听rowAction状态变化，触发相应处理
  useEffect(() => {
    if (rowAction) {
      handleRowAction(rowAction);
    }
  }, [rowAction]);

  const handleRowAction = (action: DataTableRowAction<InteriorRegime> | null) => {
    // 如果为空，直接返回
    if (!action) return;

    // 将row的原始数据转换为完整的CasesNavigation对象
    const rowData = action.row.original;
    switch (action.type) {
      case "update":
        setEditingItem(rowData);
        setIsFormOpen(true);
        break
      case "delete":
        deleteItem(rowData.id!);
        break
      case "auth":
        // 查看详情
        setEditingItem(rowData);
        setIsFormOpen(true);
    }
  };

  const handleSuccess = () => {
    setIsFormOpen(false);
    setEditingItem(undefined);
    // 刷新数据
    handleQuery({
      pageSize: 10,
      page: 0,
      specification: {}
    });
  };

  const handleAdd = () => {
    setEditingItem(undefined);
    setIsFormOpen(true);
  };

  return (
    <div className="container mx-auto py-6">
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-2xl font-bold">制度管理</h1>
        {access.hasAction('INTERIOR:REGIME:SAVE') && (
          <Button onClick={handleAdd}>
            <Plus className="mr-2 h-4 w-4" />
            新增制度
          </Button>
        )}
      </div>

      <div className="w-full">
        <RegimeTable
          onQuery={handleQuery}
          setRowAction={setRowActionState}
          data={pageData}
          onDelete={deleteItem}
        />
      </div>

      <Sheet open={isFormOpen} onOpenChange={setIsFormOpen}>
        <SheetContent className="w-[900px] sm:max-w-[900px]">
          <SheetHeader>
            <SheetTitle>{editingItem ? '编辑项目' : '新增项目'}</SheetTitle>
          </SheetHeader>
          <div className="mt-6 p-4">
            <RegimeForm
              initialData={editingItem}
              onSuccess={handleSuccess}
              onCancel={() => setIsFormOpen(false)}
            />
          </div>
        </SheetContent>
      </Sheet>
    </div>
  );
}
