import {ColumnDef} from "@tanstack/react-table";
import {InteriorRegime} from "@/service/interior/regime";
import {Badge} from "@/components/ui/badge";
import {DataTableColumnHeader} from "@/components/data-table/data-table-column-header";
import {Link} from "@@/exports";
import React from "react";
import {getTagColor} from "@/service/interior/regime";

export const getColumns = ({
                               actions,
                           }: {
    actions: any;
}): ColumnDef<InteriorRegime>[] => {
    const columns: ColumnDef<InteriorRegime>[] = [
        {
            accessorKey: "title",
            header: ({column}) => <DataTableColumnHeader column={column}/>,
            cell: ({row}) => (
                <Link to={`/interior/project-datum?projectId=${row.original.id}&parentId=0`}>
                    <div dangerouslySetInnerHTML={{__html: row.original.title}}/>
                </Link>
            ),
            meta: {
                title: "名称",
            },
            enableSorting: false,
            enableHiding: true,
        },
        {
            accessorKey: "responsibleTeam",
            header: ({column}) => (
                <DataTableColumnHeader column={column} title="项目组"/>
            ),
            enableSorting: true,
            cell: ({row}) => {
                return (
                    <div className="flex space-x-2">
              <span className="max-w-[100px] truncate">
                {row.getValue("responsibleTeam")}
              </span>
                    </div>
                );
            },
            meta: {
                title: "项目组",
            },
        },
        {
            accessorKey: "principal",
            header: ({column}) => (
                <DataTableColumnHeader column={column} title="项目负责人"/>
            ),
            enableSorting: true,
            cell: ({row}) => {
                return (
                    <div className="flex space-x-2">
              <span className="max-w-[100px] truncate">
                {row.getValue("principal")}
              </span>
                    </div>
                );
            },
            meta: {
                title: "项目负责人",
            },
        },
        {
            accessorKey: "tags",
            header: ({column}) => <DataTableColumnHeader column={column}/>,
            cell: ({row}) => {
                const tags = row.getValue("tags") as string[] || [];
                return (
                    <div className="flex gap-1 flex-wrap">
                        {tags.map((tag) => (
                            <Badge key={tag} variant={getTagColor(tag)}>
                                {tag}
                            </Badge>
                        ))}
                    </div>
                );
            },
            meta: {
                title: "标签",
            },
            enableSorting: false,
            enableHiding: true,
        },
        {
            accessorKey: "effectiveDate",
            header: ({column}) => (
                <DataTableColumnHeader column={column} title="生效时间"/>
            ),
            cell: ({row}) => {
                return (
                    <div className="flex space-x-2">
              <span className="max-w-[100px] truncate">
                {row.getValue("effectiveDate")}
              </span>
                    </div>
                );
            },
            meta: {
                title: "生效时间",
            },
        },
        {
            accessorKey: "failureDate",
            header: ({column}) => (
                <DataTableColumnHeader column={column} title="失效时间"/>
            ),
            cell: ({row}) => {
                return (
                    <div className="flex space-x-2">
              <span className="max-w-[100px] truncate">
                {row.getValue("failureDate")}
              </span>
                    </div>
                );
            },
            meta: {
                title: "失效时间",
            },
        },
        {
            accessorKey: "regimeVersion",
            header: ({column}) => (
                <DataTableColumnHeader column={column} title="制度版本号"/>
            ),
            cell: ({row}) => {
                return (
                    <div className="flex space-x-2">
              <span className="max-w-[100px] truncate">
                {row.getValue("regimeVersion")}
              </span>
                    </div>
                );
            },
            meta: {
                title: "制度版本号",
            },
        },
        {
            accessorKey: "status",
            header: ({column}) => (
                <DataTableColumnHeader column={column} title="状态"/>
            ),
            cell: ({row}) => {
                const status = row.getValue("status");
                return (
                    <Badge variant={status === 'ENABLED' ? 'default' : 'secondary'}>
                        {status === 'ENABLED' ? '启用' : '禁用'}
                    </Badge>
                );
            },
            meta: {
                title: "状态",
            },
            filterFn: (row, id, value) => {
                return value.includes(row.getValue(id));

            },
        },
    ]

    if (actions) {
        columns.push(actions);
    }
    return columns;
};
