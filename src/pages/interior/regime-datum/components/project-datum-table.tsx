import React from 'react';
import {DataTableFilterField, DataTableRowAction} from "@/components/data-table/types";
import {useDataTable} from "@/components/data-table/hooks/use-data-table";
import {DataTable} from "@/components/data-table/data-table";
import {DataTableToolbar} from "@/components/data-table/data-table-toolbar";
import {getColumns} from '@/pages/interior/project-datum/components/project-datum-table-columns';
import {InteriorProjectDatum} from '@/service/interior/project-datum';
import {Page, PageQuery, Specification} from '@/types';
import {getFilterFields} from './project-datum-table-filters';
import {getActions} from "@/pages/interior/project-datum/components/project-datum-table-columns-action";
import {useAccess} from "umi";

interface CasesTableProps {
  onQuery: (query: PageQuery<Specification<InteriorProjectDatum>>) => Promise<Page<InteriorProjectDatum>>;
  setRowAction: React.Dispatch<React.SetStateAction<DataTableRowAction<InteriorProjectDatum> | null>>;
  data?: Page<InteriorProjectDatum>;
}

export function ProjectDatumTable({ onQuery, setRowAction, data }: CasesTableProps) {
  const access = useAccess();
  
  const actions = React.useMemo(() => getActions({ setRowAction, access }), [setRowAction, access]);
  const columns = React.useMemo(() => getColumns({ actions }), [actions]);
  
  const [filterFields, setFilterFields] = React.useState<DataTableFilterField<InteriorProjectDatum>[]>(getFilterFields());

  const { table } = useDataTable({
    data: data?.data || [],
    columns,
    pageCount: Math.ceil((data?.total || 0) / (data?.pageSize || 10)),
    filterFields,
    initialState: {
      pagination: {
        pageIndex: 0,
        pageSize: 30
      },
      sorting: [{ id: 'status', desc: true }],
      columnPinning: { right: ["actions"] },
    },
    // getRowId: (originalRow) => originalRow.id,
    clearOnDefault: false,
    shallow:true
  });

  React.useEffect(() => {
    setFilterFields(getFilterFields());
  }, [data]);

  const handleFilterChange = (pageQuery: PageQuery<Specification<InteriorProjectDatum>>) => {
    onQuery(pageQuery);
  };

  return (
    <>
      <DataTable table={table} className="h-fit">
        <DataTableToolbar 
          type={'sql'}
          onFilterChange={handleFilterChange} 
          table={table} 
          filterFields={filterFields} 
        />
      </DataTable>
    </>
  );
} 