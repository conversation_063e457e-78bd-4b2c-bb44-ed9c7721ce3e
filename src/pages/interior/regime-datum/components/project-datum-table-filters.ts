import {DataTableFilterField} from "@/components/data-table/types";
import {InteriorProjectDatum} from "@/service/interior/project-datum";
import {Aggs} from "@/types";

export function getFilterFields(): DataTableFilterField<InteriorProjectDatum>[] {
    return [
        {
            id: "title",
            label: "搜索",
            placeholder: "搜索名称...",
            highlight:true,
        },
        {
            id:'parentId',
            label:'',
            placeholder:'',
            virtual: true,
            hidden:true,
        },
        {
            id:'projectId',
            label:'',
            placeholder:'',
            virtual: true,
            hidden:true,
        },
        {
            id:'stageId',
            label:'',
            placeholder:'',
            virtual: true,
            hidden:true,
        },
    ]
} 