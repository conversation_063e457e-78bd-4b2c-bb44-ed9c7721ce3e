import React from 'react';
import {AuthDialog} from '@/components/auth/auth-dialog';
import {InteriorProjectDatum} from '@/service/interior/project-datum';

interface CasesAuthSheetProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  productCase?: InteriorProjectDatum;
  onSuccess?: () => void;
}

export function ProjectDatumAuthSheet({
  open,
  onOpenChange,
  productCase,
  onSuccess
}: CasesAuthSheetProps) {
  const handleSuccess = () => {
    onSuccess?.();
  };

  return (
    <AuthDialog
      open={open}
      onOpenChange={onOpenChange}
      resourceId={productCase?.id || ''}
      resourceType="CASE"
      onSuccess={handleSuccess}
    />
  );
} 