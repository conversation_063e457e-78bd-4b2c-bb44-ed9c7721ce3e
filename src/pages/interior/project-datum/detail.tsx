import React from 'react';
import {useParams, useRequest} from "umi";
import {getProjectDatumDetail, InteriorProjectDatum} from "@/service/interior/project-datum";
import FilePreview from '@/components/file-preview';
import {Badge} from '@/components/ui/badge';
import {Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger} from "@/components/ui/tabs";
import {Button} from "@/components/ui/button";
import {Download} from "lucide-react";

export default function CaseDetailPage() {
    const {id} = useParams<{ id: string }>();
    const [projectDatumDetail, setProjectDatumDetail] = React.useState<InteriorProjectDatum>();

    // 加载案例详情
    useRequest(() => {
        if (id) {
            return getProjectDatumDetail(id);
        }
        return Promise.reject('案例ID不存在');
    }, {
        onSuccess: (res) => {
            setProjectDatumDetail(res);
        },
        ready: !!id
    });

    if (!projectDatumDetail) {
        return <div className="p-4">加载中...</div>;
    }

    return (
        <div className="h-full flex flex-col">
            {/* 标题和基本信息 */}
            <div className="flex-none py-4 px-4 space-y-2 bg-background/95 sticky top-0 z-10 shadow-sm">
                <h1 className="text-2xl font-bold text-center">{projectDatumDetail.title}</h1>

                <div className="flex items-center justify-center gap-4 flex-wrap">
                    {/* 标签 */}
                    <div className="flex flex-wrap gap-1">
                        {projectDatumDetail.tags.map(tag => (
                            <Badge key={tag}>
                                {tag}
                            </Badge>
                        ))}
                    </div>
                </div>
            </div>

            {/* 文件预览 - 使用Tabs展示多个附件 */}
            <div className="flex-1 overflow-hidden">
                <Tabs defaultValue={projectDatumDetail.file.id} className="h-full">
                    <div className="relative">
                        <div
                            className="overflow-x-auto pb-1 scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-transparent">
                            <TabsList className="px-4 w-max min-w-full">
                                <TabsTrigger
                                    key={projectDatumDetail.file.id}
                                    value={projectDatumDetail.file.id}
                                    className="flex items-center gap-2 pr-2"
                                >
                                    <span className="truncate max-w-[150px]">{projectDatumDetail.file.originalFilename}</span>
                                    <Button
                                        size="icon"
                                        variant="ghost"
                                        className="h-6 w-6 rounded-full hover:bg-gray-200 ml-1"
                                        onClick={(e) => {
                                            e.stopPropagation(); // 阻止触发标签切换
                                            // 使用file.url作为下载链接
                                            if (projectDatumDetail.file.id) {
                                                window.open(`/api/interior/projectDatum/download?fileId=${projectDatumDetail.file.id}`, '_blank');
                                            }
                                        }}
                                        title="下载文件"
                                    >
                                        <Download className="h-3.5 w-3.5"/>
                                    </Button>
                                </TabsTrigger>
                            </TabsList>
                        </div>
                    </div>
                    <TabsContent
                        key={projectDatumDetail.file.id}
                        value={projectDatumDetail.file.id}
                        className="h-[calc(100%-48px)]"
                    >
                        <FilePreview
                            url={projectDatumDetail.file.metadata?.previewUrl || ""}
                            height="100%"
                            className="w-full h-full"
                        />
                    </TabsContent>
                </Tabs>
            </div>
        </div>
    );
} 