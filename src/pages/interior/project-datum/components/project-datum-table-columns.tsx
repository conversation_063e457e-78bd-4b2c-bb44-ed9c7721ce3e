import React from "react";
import {ColumnDef} from "@tanstack/react-table";
import {DataTableColumnHeader} from "@/components/data-table/data-table-column-header";
import {InteriorProjectDatum} from "@/service/interior/project-datum";
import {Badge} from "@/components/ui/badge";
import {Link} from "umi";
import FileIcon from "@/components/feature/FileIcon";

export interface CasesTableColumnProps {
   actions: ColumnDef<InteriorProjectDatum> | undefined;
}

export function getColumns({actions}: CasesTableColumnProps): ColumnDef<InteriorProjectDatum>[] {
    const  columns: ColumnDef<InteriorProjectDatum>[] = [
        {
            accessorKey: "title",
            header: ({column}) => <DataTableColumnHeader column={column}/>,
            cell: ({row}) => (
                row.original.type == 'FILE' ? (
                    <Link to={`/interior/project-datum/${row.original.id}`}>
                        <div className={"flex"}>
                            <FileIcon  size={'sm'} filename={row.original.file.originalFilename}/>  <div className={'pl-1'} dangerouslySetInnerHTML={{__html: row.original.title}}/>
                        </div>
                    </Link>
                ) : (
                    <Link to={`/interior/project-datum?projectId=${row.original.projectId}&parentId=${row.original.id}&stageId=${row.original.stageId}`}>
                        <div className={"flex"}>
                            <FileIcon  size={'sm'} filename={""} type={'DIRECTORY'}/><div className={'pl-1'} dangerouslySetInnerHTML={{__html: row.original.title}}/>
                        </div>
                    </Link>
                )
            ),
            meta: {
                title: "名称",
            },
            enableSorting: false
        },
        {
            accessorKey: "tags",
            header: ({column}) => <DataTableColumnHeader column={column}/>,
            cell: ({row}) => {
                const tags = row.getValue("tags") as string[];
                return (
                    <div className="flex gap-1 flex-wrap">
                        <Badge>
                            {tags}
                        </Badge>
                    </div>
                );
            },
            meta: {
                title: "标签",
            },
            enableSorting: false,
            enableHiding: true,
        },
    ];
    if (actions){
        columns.push(actions);
    }
    return columns;
} 