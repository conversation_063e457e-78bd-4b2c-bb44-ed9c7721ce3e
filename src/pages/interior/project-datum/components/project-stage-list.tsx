import React, {Dispatch, SetStateAction, useEffect, useMemo, useState} from 'react';
import { Search } from "lucide-react";
import { Input } from "@/components/ui/input";
import { InteriorProjectStage } from "@/service/interior/project-stage";
import { ScrollArea } from "@/components/ui/scroll-area";
import {parseAsString, useQueryState} from "nuqs";
import {toast} from "sonner";

interface StateTreeProps {
    data: InteriorProjectStage[];
}

export function ProjectStageList({ data }: StateTreeProps) {
    const [searchKeyword, setSearchKeyword] = useState('');
    const [stageData, setStageData] = useState<InteriorProjectStage[]>(data)

    useEffect(() => {
        if (searchKeyword == null || searchKeyword.length==0){
            setStageData(data);
        }else {
            setStageData(data.filter(stage=>stage.title.includes(searchKeyword)))
        }

    }, [searchKeyword,data]);

    const [stageId, setStageId] = useQueryState(
        "stageId",
        parseAsString,
    );

    const [parentId, setParentId] = useQueryState(
        "parentId",
        parseAsString,
    );

    const selectStageLabel = (id: string)=> {
        if (parentId !== '0') {
            toast.error('子项无法选择流程');
        } else {
            setStageId(id);
        }
    }

    // 高亮显示匹配的文本
    const highlightText = (text: string) => {
        if (!searchKeyword) return text;

        const parts = text.split(new RegExp(`(${searchKeyword})`, 'gi'));
        return (
            <>
                {parts.map((part, i) =>
                    part.toLowerCase() === searchKeyword.toLowerCase() ? (
                        <span key={i} className="bg-yellow-200 text-black rounded px-0.5">{part}</span>
                    ) : (
                        part
                    )
                )}
            </>
        );
    };

    return (
        <div className="w-[300px] border-r flex flex-col h-full">
            <div className="p-4">
                <div className="relative mb-4">
                    <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                    <Input
                        placeholder="搜索阶段..."
                        value={searchKeyword}
                        onChange={(e) => setSearchKeyword(e.target.value)}
                        className="pl-8"
                    />
                </div>
                <ScrollArea className="h-[calc(100vh-180px)]">
                    <div className="border rounded-lg">
                        {stageData.map(stage => {
                            return (
                                <div
                                    key={stage.id}
                                    className={`flex items-center py-3 px-4 hover:bg-accent cursor-pointer  ${stageId as string  === stage.id ? 'bg-accent' : ''}`}
                                    onClick={() => selectStageLabel(stage.id as string)}
                                >
                                    <div className="flex-1">
                                        <div className="font-medium">
                                            {highlightText(stage.title)}
                                        </div>
                                        {/*{stage.code && (*/}
                                        {/*    <div className="text-xs text-muted-foreground mt-1">*/}
                                        {/*        {highlightText(stage.code)}*/}
                                        {/*    </div>*/}
                                        {/*)}*/}
                                    </div>
                                </div>
                            );
                        })}

                        {data.length === 0 && (
                            <div className="py-6 text-center text-muted-foreground">
                                未找到匹配的项目阶段
                            </div>
                        )}
                    </div>
                </ScrollArea>
            </div>
        </div>
    );
}