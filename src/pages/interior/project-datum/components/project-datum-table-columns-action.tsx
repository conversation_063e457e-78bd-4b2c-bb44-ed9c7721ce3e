import {Access, AccessInstance} from "@@/exports";
import {InteriorProjectDatum} from "@/service/interior/project-datum";
import {ColumnDef} from "@tanstack/react-table";
import {Button} from "@/components/ui/button";
import {Lock, Pencil, Trash} from "lucide-react";
import React from "react";
import type {DataTableRowAction} from "@/components/data-table/types";

export interface CasesTableColumnProps {
    setRowAction: React.Dispatch<React.SetStateAction<DataTableRowAction<InteriorProjectDatum> | null>>;
    access:AccessInstance
}

export function getActions({setRowAction,access}:CasesTableColumnProps):ColumnDef<InteriorProjectDatum> |undefined {
    const {hasAction} = access
    if (hasAction('PRODUCT:CASES')) {
        return {
            id: "actions",
            cell: ({row}) => {
                return (
                    <div className="flex items-center gap-2">
                        <Access accessible={hasAction("PROJECT:DATUM:EDIT")}>
                            <Button
                                variant="ghost"
                                size="icon"
                                className="h-8 w-8"
                                onClick={() =>
                                    setRowAction({
                                        type: "update",
                                        row: row,
                                    })
                                }
                            >
                                <Pencil className="h-4 w-4"/>
                                <span className="sr-only">编辑</span>
                            </Button>
                        </Access>
                        <Access accessible={hasAction("PROJECT:DATUM:DELETE")}>
                            <Button
                                variant="ghost"
                                size="icon"
                                className="h-8 w-8"
                                onClick={() =>
                                    setRowAction({
                                        type: "delete",
                                        row: row,
                                    })
                                }
                            >
                                <Trash className="h-4 w-4"/>
                                <span className="sr-only">删除</span>
                            </Button>
                        </Access>
                    </div>
                );
            },
        }
    }
}