import React, {useState} from 'react';
import {Button} from "@/components/ui/button";
import {Card} from "@/components/ui/card";
import {Input} from "@/components/ui/input";
import {Label} from "@/components/ui/label";
import {history, useParams, useRequest} from "umi";
import {
    getProjectDatumDetail,
    ProjectDatumSave,
    saveProjectDatum
} from "@/service/interior/project-datum";
import {fetchProjectStates, InteriorProjectStage} from "@/service/interior/project-stage"
import {ProjectStageList} from '@/pages/interior/project-datum/components/project-stage-list';
import {uploadFile} from '@/service/storage';
import {Badge} from '@/components/ui/badge';
import {X} from 'lucide-react';
import {toast} from 'sonner';
import {parseAsString, useQueryState} from "nuqs";
import {Textarea} from "@/components/ui/textarea";

interface FormState {
  remark?: string;
  title: string;
  stageId: string;
  parentId: string;
  projectId: string;
  tags: string[];
  status: string;
  attachment: {
    fileId: string;
    fileName: string;
  };
  id?: string;
}

export default function EditCasePage() {
  const params = useParams<{ id: string }>();
  const isEdit = !!params.id;

  const [projectId, setProjectId] = useQueryState(
      "projectId",
      parseAsString.withDefault(''),
  );

  const [stageId, setStageId] = useQueryState(
      "stageId",
      parseAsString.withDefault(''),
  );

  const [parentId, setParentId] = useQueryState(
      "parentId",
      parseAsString.withDefault(''),
  );

  const [type, setType] = useQueryState(
      "type",
      parseAsString.withDefault(''),
  );

  console.log('编辑模式:', isEdit, '参数ID:', params.id); // 添加参数日志

  // 分类树数据
  const [InteriorProjectStageTree, setInteriorProjectStageTree] = useState<InteriorProjectStage[]>([]);
  
  // 表单数据
  const [formData, setFormData] = useState<FormState>({
    parentId: "",
    stageId,
    status: "",
    title: '',
    tags: [],
    attachment: {
      fileId: '',
      fileName: ''
    },
    id: undefined,
    projectId,
    remark: ''
  });

  // 标签输入
  const [currentTag, setCurrentTag] = useState('');

  // 加载分类树
  useRequest(fetchProjectStates, {
    onSuccess: (res) => {
      if (res) {
        setInteriorProjectStageTree(res);
      }
    }
  });

  // 加载案例数据
  useRequest(() => {
    console.log('准备加载案例数据, ID:', params.id); // 添加加载日志
    if (!isEdit) return Promise.reject('不是编辑模式');
    return getProjectDatumDetail(params.id);
  }, {
    ready: isEdit,
    onSuccess: (res) => {
      console.log('加载文档数据成功:', res); // 添加成功日志
      if (res) {
        setType(res.type);
        setStageId(res.stageId);
        setProjectId(res.projectId);
        setParentId(res.parent.id);
        setFormData({
          attachment: {
            fileId: res.file ? res.file.id : '',
            fileName: res.file ? res.file.originalFilename : ''
          },
          projectId: res.projectId,
          stageId: res.stageId,
          title: res.title,
          tags: res.tags,
          status: res.status,
          id: res.id,
          parentId: res.parentId,
          remark: res.remark
        });
      }
    },
    onError: (err) => {
      console.error('加载文档数据失败:', err); // 添加错误日志
      toast.error('加载文档数据失败');
    }
  });

  // 保存案例
  const { run: saveProductCase, loading } = useRequest(saveProjectDatum, {
    manual: true,
    onSuccess: () => {
      toast.success(isEdit ? '编辑成功' : '创建成功');
      history.back();
    }
  });

  // 处理文件上传
  const handleFileUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    for (const file of files) {
      const formData = new FormData();
      formData.append('file', file);
      const res = await uploadFile(formData);
      if (res?.data) {
        setFormData(prev => ({
          ...prev,
          title: file.name,
          attachment: {
            fileId: res.data,
            fileName: file.name
          }
        }));
      }
    }
  };

  // 移除附件
  const removeAttachment = (fileId: string) => {
    setFormData(prev => ({
      ...prev,
      attachment: {
        fileId: '',
        fileName: ''
      },
    }));
  };

  // 添加标签
  const addTag = (value: string) => {
    const tag = value.trim();
    if (tag && !formData.tags.includes(tag)) {
      setFormData(prev => ({
        ...prev,
        tags: [...prev.tags, tag]
      }));
    }
    setCurrentTag('');
  };

  // 处理标签输入
  const handleTagInput = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter' && currentTag.trim()) {
      e.preventDefault();
      addTag(currentTag);
    } else if (e.key === 'Backspace' && !currentTag) {
      e.preventDefault();
      setFormData(prev => ({
        ...prev,
        tags: prev.tags.slice(0, -1)
      }));
    }
  };

  // 处理失去焦点
  const handleBlur = () => {
    if (currentTag.trim()) {
      addTag(currentTag);
    }
  };

  // 移除标签
  const removeTag = (tagToRemove: string) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags.filter(tag => tag !== tagToRemove)
    }));
  };

  // 处理表单字段变化
  const handleFieldChange = (field: keyof FormState, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // 提交表单
  const handleSubmit = () => {
    if (!formData.title) {
      toast.error('请输入标题');
      return;
    }
    if (!stageId) {
      toast.error('请选择分类');
      return;
    }
    if (type === 'FILE' && formData.attachment.fileId === '') {
      toast.error('请上传至少一个附件');
      return;
    }
    const submitData: ProjectDatumSave = {
      title: formData.title,
      tags: formData.tags,
      projectId,
      id: formData.id,
      type: type,
      stageId,
      parentId,
      status: formData.status,
      fileId: formData.attachment.fileId
    };
    saveProductCase(submitData);
  };

  return (
    <div className="flex h-full">
      {/* 左侧分类树 */}
      <ProjectStageList
        data={InteriorProjectStageTree}
      />

      {/* 右侧表单 */}
      <div className="flex-1 p-4 overflow-auto">
        <Card className="p-6">
          <h1 className="text-2xl font-bold mb-6">{isEdit ? '编辑项目文档' : '新建项目文档'}</h1>
          <div className="space-y-6">
            <div className="space-y-2">
              <Label>标题</Label>
              <Input
                value={formData.title}
                onChange={(e) => handleFieldChange('title', e.target.value)}
                placeholder="请输入标题"
              />
            </div>


            <div className="space-y-2">
              <Label>标签</Label>
              <div className="flex flex-wrap gap-2 p-2 border rounded-lg">
                <div className="flex flex-wrap gap-2">
                  {formData.tags.map(tag => (
                    <Badge key={tag} variant="secondary" className="flex items-center gap-1">
                      {tag}
                      <button
                        type="button"
                        onClick={() => removeTag(tag)}
                        className="h-3 w-3 rounded-full"
                      >
                        <X className="h-3 w-3 cursor-pointer" />
                      </button>
                    </Badge>
                  ))}
                </div>
                <Input
                  value={currentTag}
                  onChange={(e) => setCurrentTag(e.target.value)}
                  onKeyDown={handleTagInput}
                  onBlur={handleBlur}
                  placeholder="输入标签后按回车添加"
                  className="border-0 flex-1 p-0 focus-visible:ring-0 placeholder:text-muted-foreground min-w-[200px]"
                />
              </div>
            </div>

            {type === 'FILE' && (<div className="space-y-2">
              <Label>附件</Label>
              <div className="border rounded-lg p-4">
                <Input
                  type="file"
                  onChange={handleFileUpload}
                  className="mb-2"
                  multiple
                />
                <div className="space-y-2">
                    <div key={formData.attachment.fileId} className="flex items-center gap-2 p-2 border rounded">
                      <div className="flex-1 truncate">{formData.attachment.fileName}</div>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => removeAttachment(formData.attachment.fileId)}
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </div>
                </div>
              </div>
            </div>)}

            <div className="space-y-2">
              <Label>说明</Label>
              <Textarea
                  value={formData.remark}
                  onChange={(e) => handleFieldChange('remark', e.target.value)}
                  placeholder="请输入说明信息"
              />
            </div>

            <div className="flex justify-end gap-4">
              <Button variant="outline" onClick={() => history.back()}>
                取消
              </Button>
              <Button onClick={handleSubmit} disabled={loading}>
                {loading ? '保存中...' : '保存'}
              </Button>
            </div>
          </div>
        </Card>
      </div>
    </div>
  );
} 