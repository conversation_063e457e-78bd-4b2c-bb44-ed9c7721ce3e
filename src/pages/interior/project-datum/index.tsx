import React, {useEffect, useState} from 'react';
import {But<PERSON>} from "@/components/ui/button";
import {ArrowUpFromLine, Plus} from "lucide-react";
import {Access, history, useAccess, useRequest} from "umi";
import {fetchProjectDatumPage, InteriorProjectDatum, deleteProjectDatum} from "@/service/interior/project-datum";
import {InteriorProjectStage, fetchProjectStates} from "@/service/interior/project-stage";
import {ProjectDatumTable} from '@/pages/interior/project-datum/components/project-datum-table';
import {DataTableRowAction} from "@/components/data-table/types";
import {ProjectDatumAuthSheet} from './components/project-datum-auth-sheet';
import {Page, PageQuery, Specification} from '@/types';
import {ProjectStageList} from "@/pages/interior/project-datum/components/project-stage-list";
import {parseAsString, useQueryState} from "nuqs";

export default function CasesPage() {
    const [data, setData] = useState<InteriorProjectStage[]>([]);
    const [rowAction, setRowAction] = useState<DataTableRowAction<InteriorProjectDatum> | null>(null);
    const [isAuthSheetOpen, setIsAuthSheetOpen] = useState(false);
    const [pageData, setPageData] = useState<Page<InteriorProjectDatum>>();
    const access = useAccess();

    var fileType = 'FILE'

    const [projectId] = useQueryState(
        "projectId",
        parseAsString,
    );

    const [stageId] = useQueryState(
        "stageId",
        parseAsString,
    );

    const [parentId] = useQueryState(
        "parentId",
        parseAsString,
    );

    useRequest(fetchProjectStates, {
        onSuccess: (res) => {
            if (res) {
                setData(res);
            }
        }
    });

    const {run: fetchCasesRun} = useRequest(fetchProjectDatumPage, {
        manual: true
    });

    // 跳转到新增页面
    const handleAdd = () => {
        if (!stageId) {
            history.push(`/interior/project-datum/new?projectId=${projectId}&type=${fileType}&parentId=${parentId}`);
        } else {
            history.push(`/interior/project-datum/new?projectId=${projectId}&type=${fileType}&parentId=${parentId}&stageId=${stageId}`);
        }
    };

    const handleAddDirectory = () => {
        fileType = 'DIRECTORY'
        handleAdd()
    }

    const handleAddFile = () => {
        fileType = 'FILE'
        handleAdd()
    }

    const goBack = () => {
        history.back()
    }
    const { run: deleteItem } = useRequest(deleteProjectDatum, {
        manual: true,
        onSuccess: () => {
            // 刷新数据
            handleQuery({
                pageSize: 10,
                page: 0,
                specification: {
                    projectId: projectId,
                    stageId: stageId
                }
            });
        },
    });

    // 处理表格操作
    useEffect(() => {
        // 如果为空，直接返回
        if (!rowAction) return;
        // 将row的原始数据转换为完整的CasesNavigation对象
        const rowData = rowAction.row.original;
        switch (rowAction?.type) {
            case "update":
                history.push(`/interior/project-datum/edit/${rowData.id}?parentId=${parentId}`);
                break
            case "delete":
                deleteItem(rowData.id!)
        }
    }, [rowAction]);

    // 处理查询操作
    const handleQuery = (query: PageQuery<Specification<InteriorProjectDatum>>) => {
        return fetchCasesRun(query).then(res => {
            setPageData(res);
            return res;
        });
    };

    return (
        <div className="flex h-full">
            {/* 左侧分类树 */}
            <ProjectStageList
                data={data}
            />


            {/* 右侧表格 */}
            <div className="flex-1 p-4 overflow-auto">
                <div className="flex justify-between items-center mb-4">
                    <h1 className="text-2xl font-bold">项目文档管理</h1>
                    <Access accessible={access.hasAction("PROJECT:DATUM:ADD")}>
                        <div className="flex space-x-2">
                            <Button
                                variant="ghost"
                                className="border"
                                onClick={handleAddDirectory}>
                                <Plus className="h-4 w-4 mr-2"/>
                                新建文件夹
                            </Button>
                            <Button
                                onClick={handleAddFile}
                                variant="ghost"
                                className="border">
                                <Plus className="h-4 w-4 mr-2"/>
                                新建文档
                            </Button>
                            <Button
                                onClick={goBack}
                                variant="ghost"
                                className="border">
                                <ArrowUpFromLine className="h-4 w-4 mr-2"/>
                                返回上一级
                            </Button>
                        </div>
                    </Access>
                </div>
                <div className="border p-2 rounded-xl text-sm">
                    <span className="text-blue-600 font-bold">🅘 上传前说明：</span>
                    <span className="font-bold">核心文档(必须上传)，</span>辅助文档(选择性上传)；
                    <span className="font-bold">提交时限：</span>各环节完成7个工作日；
                    <span className="font-bold">格式要求：</span>PDF、Word、Excel等；
                    <br />
                    &nbsp;&nbsp;&nbsp;&nbsp;<span className="font-bold">命名规则：</span>采用统一命名结构(文件名称)(版本)：实例：招标文件_V1.0
                    <br />
                    &nbsp;&nbsp;&nbsp;&nbsp;<span className="font-bold">上传流程：</span>1.文档准备 —{'>'} 2.格式检查 —{'>'} 3.系统上传 —{'>'} 4.规范命名
                </div>
                <ProjectDatumTable
                    onQuery={handleQuery}
                    setRowAction={setRowAction}
                    data={pageData}
                />

                {rowAction?.type === 'auth' && (
                    <ProjectDatumAuthSheet
                        open={isAuthSheetOpen}
                        onOpenChange={setIsAuthSheetOpen}
                        productCase={rowAction.row.original}
                        onSuccess={() => {
                            setIsAuthSheetOpen(false);
                            setRowAction(null);
                        }}
                    />
                )}
            </div>

        </div>
    );
}