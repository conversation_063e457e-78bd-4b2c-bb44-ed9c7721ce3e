import {Access, AccessInstance} from "@@/exports";
import {ColumnDef} from "@tanstack/react-table";
import {Button} from "@/components/ui/button";
import {Pencil, Trash} from "lucide-react";
import React from "react";
import type {DataTableRowAction} from "@/components/data-table/types";
import {InteriorProject} from "@/service/interior/project";

export interface ProjectTableColumnProps {
    setRowAction: React.Dispatch<React.SetStateAction<DataTableRowAction<InteriorProject> | null>>;
    access:AccessInstance
}

export function getActions({setRowAction,access}:ProjectTableColumnProps):ColumnDef<InteriorProject> |undefined {
    const {hasAction} = access
    if (hasAction('INTERIOR:PROJECT')) {
        return {
            id: "actions",
            cell: ({row}) => {
                return (
                    <div className="flex items-center gap-2">
                        <Access accessible={hasAction("INTERIOR:PROJECT:EDIT")}>
                            <Button
                                variant="ghost"
                                size="icon"
                                className="h-8 w-8"
                                onClick={() =>
                                    setRowAction({
                                        type: "update",
                                        row: row,
                                    })
                                }
                            >
                                <Pencil className="h-4 w-4"/>
                                <span className="sr-only">编辑</span>
                            </Button>
                        </Access>
                        <Access accessible={hasAction("INTERIOR:PROJECT:DELETE")}>
                            <Button
                                variant="ghost"
                                size="icon"
                                className="h-8 w-8"
                                onClick={() =>
                                    setRowAction({
                                        type: "delete",
                                        row: row,
                                    })
                                }
                            >
                                <Trash className="h-4 w-4"/>
                                <span className="sr-only">删除</span>
                            </Button>
                        </Access>
                    </div>
                );
            },
        }
    }
}