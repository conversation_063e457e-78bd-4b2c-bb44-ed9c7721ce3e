import {ColumnDef} from "@tanstack/react-table";
import {InteriorProject} from "@/service/interior/project";
import {Badge} from "@/components/ui/badge";
import {DataTableColumnHeader} from "@/components/data-table/data-table-column-header";
import {Link} from "@@/exports";
import React from "react";
import {getTagColor} from "@/service/interior/project";

export const getColumns = ({
                               actions,
                           }: {
    actions: any;
}): ColumnDef<InteriorProject>[] => {
    const columns: ColumnDef<InteriorProject>[] = [
        {
            accessorKey: "title",
            header: ({column}) => <DataTableColumnHeader column={column}/>,
            cell: ({row}) => (
                <Link to={`/interior/project-datum?projectId=${row.original.id}&parentId=0`}>
                    <div dangerouslySetInnerHTML={{__html: row.original.title}}/>
                </Link>
            ),
            meta: {
                title: "名称",
            },
            enableSorting: false,
            enableHiding: true,
        },
        {
            accessorKey: "principal",
            header: ({column}) => (
                <DataTableColumnHeader column={column} title="项目负责人"/>
            ),
            enableSorting: true,
            cell: ({row}) => {
                return (
                    <div className="flex space-x-2">
              <span className="max-w-[100px] truncate">
                {row.getValue("principal")}
              </span>
                    </div>
                );
            },
            meta: {
                title: "项目负责人",
            },
        },
        {
            accessorKey: "member",
            header: ({column}) => (
                <DataTableColumnHeader column={column} title="项目成员"/>
            ),
            enableSorting: true,
            cell: ({row}) => {
                return (
                    <div className="flex space-x-2">
              <span className="max-w-[100px] truncate">
                {row.getValue("member")}
              </span>
                    </div>
                );
            },
            meta: {
                title: "项目成员",
            },
        },
        {
            accessorKey: "tags",
            header: ({column}) => <DataTableColumnHeader column={column}/>,
            cell: ({row}) => {
                const tags = row.getValue("tags") as string[] || [];
                return (
                    <div className="flex gap-1 flex-wrap">
                        {tags.map((tag) => (
                            <Badge key={tag} variant={getTagColor(tag)}>
                                {tag}
                            </Badge>
                        ))}
                    </div>
                );
            },
            meta: {
                title: "标签",
            },
            enableSorting: false,
            enableHiding: true,
        },
        {
            accessorKey: "projectCost",
            header: ({column}) => (
                <DataTableColumnHeader column={column} title="项目成本"/>
            ),
            cell: ({row}) => {
                return (
                    <div className="flex space-x-2">
              <span className="max-w-[100px] truncate">
                {row.getValue("projectCost")}
              </span>
                    </div>
                );
            },
            meta: {
                title: "项目成本",
            },
        },
        {
            accessorKey: "startTime",
            header: ({column}) => (
                <DataTableColumnHeader column={column} title="开始时间"/>
            ),
            cell: ({row}) => {
                return (
                    <div className="flex space-x-2">
              <span className="max-w-[100px] truncate">
                {row.getValue("startTime")}
              </span>
                    </div>
                );
            },
            meta: {
                title: "开始时间",
            },
        },
        {
            accessorKey: "endTime",
            header: ({column}) => (
                <DataTableColumnHeader column={column} title="结束时间"/>
            ),
            cell: ({row}) => {
                return (
                    <div className="flex space-x-2">
              <span className="max-w-[100px] truncate">
                {row.getValue("endTime")}
              </span>
                    </div>
                );
            },
            meta: {
                title: "结束时间",
            },
        },
        {
            accessorKey: "planningCycle",
            header: ({column}) => (
                <DataTableColumnHeader column={column} title="计划工期"/>
            ),
            cell: ({row}) => {
                return (
                    <div className="flex space-x-2">
              <span className="max-w-[100px] truncate">
                {row.getValue("planningCycle")}
              </span>
                    </div>
                );
            },
            meta: {
                title: "计划工期",
            },
        },
        // {
        //   accessorKey: "status",
        //   header: ({ column }) => (
        //     <DataTableColumnHeader column={column} title="状态" />
        //   ),
        //   cell: ({ row }) => {
        //     const status = row.getValue("status");
        //     return (
        //       <Badge variant={status === 'ENABLED' ? 'default' : 'secondary'}>
        //         {status === 'ENABLED' ? '启用' : '禁用'}
        //       </Badge>
        //     );
        //   },
        //   meta: {
        //     title: "状态",
        //   },
        //   filterFn: (row, id, value) => {
        //     return value.includes(row.getValue(id));
        //
        //     },
        // },
    ]

    if (actions) {
        columns.push(actions);
    }
    return columns;
};
