import React, { useEffect } from 'react';
import {DataTableFilterField, DataTableRowAction} from "@/components/data-table/types";
import {useDataTable} from "@/components/data-table/hooks/use-data-table";
import {DataTable} from "@/components/data-table/data-table";
import {DataTableToolbar} from "@/components/data-table/data-table-toolbar";
import {getColumns} from './project-table-columns';
import {InteriorProject} from '@/service/interior/project';
import {Page, PageQuery, Specification} from '@/types';
import {getFilterFields} from './project-table-filters';
import {useAccess} from "umi";
import {getActions} from './project-table-columns-action';

interface ProjectTableProps {
  onQuery: (query: PageQuery<Specification<InteriorProject>>) => Promise<Page<InteriorProject>>;
  setRowAction: React.Dispatch<React.SetStateAction<DataTableRowAction<InteriorProject> | null>>;
  data?: Page<InteriorProject>;
  onDelete: (id: string) => void;
}

export function ProjectTable({ onQuery, setRowAction, data, onDelete }: ProjectTableProps) {
  const access = useAccess();

  const actions = React.useMemo(
    () => getActions({ setRowAction, access }),
    [setRowAction, onDelete, access]
  );

  const columns = React.useMemo(() => getColumns({ actions }), [actions]);
  const [filterFields] = React.useState<DataTableFilterField<InteriorProject>[]>(getFilterFields());



  const { table } = useDataTable({
    data: data?.data || [],
    columns,
    pageCount: Math.ceil((data?.total || 0) / (data?.pageSize || 10)),
    filterFields,
    initialState: {
      pagination: {
        pageIndex: 0,
        pageSize: 10,
      },
      columnPinning: { right: ["actions"] },
      // columnVisibility:{
      //   'member':false,
      //   'projectCost':false,
      //   'planningCycle':false
      // }
    },
    getRowId: (originalRow) => originalRow.id!,
  });

  // 监听表格状态变化，触发查询
  useEffect(() => {
    const pagination = table.getState().pagination;
    const query: PageQuery<Specification<InteriorProject>> = {
      page: pagination.pageIndex,
      pageSize: pagination.pageSize,
      specification: {},
    };
    onQuery(query);
  }, [table.getState().pagination.pageIndex, table.getState().pagination.pageSize]);

  return (
    <div className="space-y-4">
      <DataTableToolbar table={table} filterFields={filterFields} onFilterChange={onQuery} />
      <DataTable table={table} />
    </div>
  );
}
