import React, {useState} from 'react';
import { useForm, SubmitHandler } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { ProjectSave, saveProject } from '@/service/interior/project';
import { useRequest } from 'umi';
import {Label} from "@/components/ui/label";
import {Badge} from "@/components/ui/badge";
import {X} from "lucide-react";

const formSchema = z.object({
    id: z.string().optional(),
    title: z.string().min(1, '请输入项目名称'),
    principal: z.string().min(1, '请输入项目负责人'),
    member: z.string().min(1, '请输入项目成员'),
    tags: z.array(z.string()),
    projectCost: z.string().min(1, '请输入项目成本'),
    startTime: z.string().min(1, '请选择项目开始时间'),
    endTime: z.string().min(1, '请选择项目结束时间'),
    planningCycle: z.string().min(1, '请输入项目计划工期'),
    remark: z.string().optional(),
    // status: z.enum(['ENABLED', 'DISABLED']).default('ENABLED'),
});

type ProjectFormValues = z.infer<typeof formSchema>;

interface ProjectFormProps {
  initialData?: ProjectSave;
  onSuccess?: () => void;
  onCancel?: () => void;
  parentId?: string;
}

export function ProjectForm({ initialData, onSuccess, onCancel }: ProjectFormProps) {
  const isEdit = !!initialData?.id;
  const defaultValues: ProjectFormValues = {
    title: initialData?.title || '',
    principal: initialData?.principal || '',
    member: initialData?.member || '',
    tags: initialData?.tags || [],
    projectCost: initialData?.projectCost || '',
    startTime: initialData?.startTime || '',
    endTime: initialData?.endTime || '',
    planningCycle: initialData?.planningCycle || '',
    remark: initialData?.remark || '',
    // status: (initialData?.status as 'ENABLED' | 'DISABLED') || 'ENABLED',
  };
  const form = useForm<ProjectFormValues>({
    resolver: zodResolver(formSchema),
    defaultValues,
  });

    // 表单数据
    const [formData, setFormData] = useState<ProjectFormValues>({
        endTime: "",
        id: undefined,
        member: "",
        planningCycle: "",
        principal: "",
        projectCost: "",
        remark: undefined,
        startTime: "",
        // status: "ENABLED",
        title: "",
        tags: form.getValues("tags")
    });

    // 标签输入
    const [currentTag, setCurrentTag] = useState('');

    // 添加标签
    const addTag = (value: string) => {
        const tag = value.trim();
        if (tag && !formData.tags.includes(tag)) {
            setFormData(prev => ({
                ...prev,
                tags: [...prev.tags, tag]
            }));
        }
        setCurrentTag('');
    };

    // 处理标签输入
    const handleTagInput = (e: React.KeyboardEvent<HTMLInputElement>) => {
        if (e.key === 'Enter' && currentTag.trim()) {
            e.preventDefault();
            addTag(currentTag);
        } else if (e.key === 'Backspace' && !currentTag) {
            e.preventDefault();
            setFormData(prev => ({
                ...prev,
                tags: prev.tags.slice(0, -1)
            }));
        }
    };

    // 处理失去焦点
    const handleBlur = () => {
        if (currentTag.trim()) {
            addTag(currentTag);
        }
    };

    // 移除标签
    const removeTag = (tagToRemove: string) => {
        setFormData(prev => ({
            ...prev,
            tags: prev.tags.filter(tag => tag !== tagToRemove)
        }));
    };


  const { loading: saveLoading, run: runSave } = useRequest(saveProject, {
    manual: true,
    onSuccess: () => {
      onSuccess?.();
    },
  });

  const { loading: updateLoading, run: runUpdate } = useRequest(saveProject, {
    manual: true,
    onSuccess: () => {
      onSuccess?.();
    },
  });

  const onSubmit: SubmitHandler<ProjectFormValues> = (data) => {
    if (isEdit && initialData?.id) {
      runUpdate({
          ...data,
          id: initialData.id,
          tags: formData.tags
      });
    } else {
      // 确保parentId是字符串或undefined，不是null
      const formDataSave = {
        ...data,
          tags: formData.tags
      };
      runSave(formDataSave);
    }
  };

  return (
      <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              <div className="flex space-x-4">
                  <FormField
                      control={form.control}
                      name="title"
                      render={({ field }) => (
                          <FormItem className="flex-1">
                              <FormLabel>项目名称</FormLabel>
                              <FormControl>
                                  <Input placeholder="请输入" {...field} />
                              </FormControl>
                              <FormMessage />
                          </FormItem>
                      )}
                  />

                  <FormField
                      control={form.control}
                      name="principal"
                      render={({ field }) => (
                          <FormItem className="flex-1">
                              <FormLabel>项目负责人</FormLabel>
                              <FormControl>
                                  <Input placeholder="请输入" {...field} />
                              </FormControl>
                              <FormMessage />
                          </FormItem>
                      )}
                  />
              </div>
              <div className="flex space-x-4">
                  <FormField
                      control={form.control}
                      name="planningCycle"
                      render={({ field }) => (
                          <FormItem className="flex-1">
                              <FormLabel>计划工期</FormLabel>
                              <FormControl>
                                  <Input placeholder="请输入" {...field} />
                              </FormControl>
                              <FormMessage />
                          </FormItem>
                      )}
                  />

                  <FormField
                      control={form.control}
                      name="projectCost"
                      render={({ field }) => (
                          <FormItem className="flex-1">
                              <FormLabel>项目成本</FormLabel>
                              <FormControl>
                                  <Input placeholder="请输入" {...field} />
                              </FormControl>
                              <FormMessage />
                          </FormItem>
                      )}
                  />
              </div>


              <div className="flex space-x-4">
                  <FormField
                      control={form.control}
                      name="startTime"
                      render={({ field }) => (
                          <FormItem className="flex-1">
                              <FormLabel>开始时间</FormLabel>
                              <FormControl>
                                  <Input
                                      type="date"
                                      placeholder="请选择"
                                      {...field}
                                  />
                              </FormControl>
                              <FormMessage />
                          </FormItem>
                      )}
                  />

                  <FormField
                      control={form.control}
                      name="endTime"
                      render={({ field }) => (
                          <FormItem className="flex-1">
                              <FormLabel>结束时间</FormLabel>
                              <FormControl>
                                  <Input
                                      type="date"
                                      placeholder="请选择"
                                      {...field}
                                  />
                              </FormControl>
                              <FormMessage />
                          </FormItem>
                      )}
                  />
              </div>

              <div className="flex space-x-4">
                  <FormField
                      control={form.control}
                      name="member"
                      render={({ field }) => (
                          <FormItem className="flex-1">
                              <FormLabel>项目成员</FormLabel>
                              <FormControl>
                                  <Input placeholder="请输入" {...field} />
                              </FormControl>
                              <FormMessage />
                          </FormItem>
                      )}
                  />
                  {/*<FormField*/}
                  {/*    control={form.control}*/}
                  {/*    name="status"*/}
                  {/*    render={({ field }) => (*/}
                  {/*        <FormItem className="flex-1">*/}
                  {/*            <FormLabel>状态</FormLabel>*/}
                  {/*            <Select onValueChange={field.onChange} defaultValue={field.value}>*/}
                  {/*                <FormControl>*/}
                  {/*                    <SelectTrigger>*/}
                  {/*                        <SelectValue placeholder="请选择状态" />*/}
                  {/*                    </SelectTrigger>*/}
                  {/*                </FormControl>*/}
                  {/*                <SelectContent>*/}
                  {/*                    <SelectItem value="ENABLED">启用</SelectItem>*/}
                  {/*                    <SelectItem value="DISABLED">禁用</SelectItem>*/}
                  {/*                </SelectContent>*/}
                  {/*            </Select>*/}
                  {/*            <FormMessage />*/}
                  {/*        </FormItem>*/}
                  {/*    )}*/}
                  {/*/>*/}
              </div>
              <div className="space-y-2">
                  <Label>标签</Label>
                  <div className="flex flex-wrap gap-2 p-2 border rounded-lg">
                      <div className="flex flex-wrap gap-2">
                          {formData.tags.map(tag => (
                              <Badge key={tag} variant="secondary" className="flex items-center gap-1">
                                  {tag}
                                  <button
                                      type="button"
                                      onClick={() => removeTag(tag)}
                                      className="h-3 w-3 rounded-full"
                                  >
                                      <X className="h-3 w-3 cursor-pointer" />
                                  </button>
                              </Badge>
                          ))}
                      </div>
                      <Input
                          value={currentTag}
                          onChange={(e) => setCurrentTag(e.target.value)}
                          onKeyDown={handleTagInput}
                          onBlur={handleBlur}
                          placeholder="输入标签后按回车添加"
                          className="border-0 flex-1 p-0 focus-visible:ring-0 placeholder:text-muted-foreground min-w-[200px]"
                      />
                  </div>
              </div>
              <FormField
                  control={form.control}
                  name="remark"
                  render={({ field }) => (
                      <FormItem>
                          <FormLabel>说明</FormLabel>
                          <FormControl>
                              <Textarea placeholder="请输入说明" {...field} />
                          </FormControl>
                          <FormMessage />
                      </FormItem>
                  )}
              />

              <div className="flex justify-end space-x-4">
                  <Button type="button" variant="outline" onClick={onCancel}>
                      取消
                  </Button>
                  <Button type="submit" disabled={saveLoading || updateLoading}>
                      {isEdit ? '保存' : '创建'}
                  </Button>
              </div>
          </form>
      </Form>
  );
}
