import React, { useState, useEffect } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Plus } from "lucide-react";
import { useRequest, useAccess } from "umi";
import { InteriorProject, deleteProject, fetchProjectPage } from "@/service/interior/project";
import { ProjectTable } from '@/pages/interior/project/components/project-table';
import { Page, PageQuery, Specification } from '@/types';
import { DataTableRowAction } from "@/components/data-table/types";
import { Sheet, Sheet<PERSON>ontent, SheetHeader, SheetTitle } from "@/components/ui/sheet";
import { ProjectForm } from '@/pages/interior/project/components/project-form';

export default function NavigationPage() {
  const [pageData, setPageData] = useState<Page<InteriorProject>>();
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [editingItem, setEditingItem] = useState<InteriorProject>();
  const access = useAccess();
  const [rowAction, setRowActionState] = useState<DataTableRowAction<InteriorProject> | null>(null);

  const { run: deleteItem } = useRequest(deleteProject, {
    manual: true,
    onSuccess: () => {
      // 刷新数据
      handleQuery({
        pageSize: 10,
        page: 0,
        specification: {}
      });
    },
  });

  const handleQuery = async (query: PageQuery<Specification<InteriorProject>>) => {
    const response = await fetchProjectPage(query);
    setPageData(response.data);
    return response.data;
  };



  // 监听rowAction状态变化，触发相应处理
  useEffect(() => {
    if (rowAction) {
      handleRowAction(rowAction);
    }
  }, [rowAction]);

  const handleRowAction = (action: DataTableRowAction<InteriorProject> | null) => {
    // 如果为空，直接返回
    if (!action) return;

    // 将row的原始数据转换为完整的CasesNavigation对象
    const rowData = action.row.original;
    switch (action.type) {
      case "update":
        setEditingItem(rowData);
        setIsFormOpen(true);
        break
      case "delete":
        deleteItem(rowData.id!);
        break
      case "auth":
        // 查看详情
        setEditingItem(rowData);
        setIsFormOpen(true);
    }
  };

  const handleSuccess = () => {
    setIsFormOpen(false);
    setEditingItem(undefined);
    // 刷新数据
    handleQuery({
      pageSize: 10,
      page: 0,
      specification: {}
    });
  };

  const handleAdd = () => {
    setEditingItem(undefined);
    setIsFormOpen(true);
  };

  return (
    <div className="container mx-auto py-6">
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-2xl font-bold">项目管理</h1>
        {access.hasAction('INTERIOR:PROJECT:SAVE') && (
          <Button onClick={handleAdd}>
            <Plus className="mr-2 h-4 w-4" />
            新增项目
          </Button>
        )}
      </div>

      <div className="w-full">
        <ProjectTable
          onQuery={handleQuery}
          setRowAction={setRowActionState}
          data={pageData}
          onDelete={deleteItem}
        />
      </div>

      <Sheet open={isFormOpen} onOpenChange={setIsFormOpen}>
        <SheetContent className="w-[900px] sm:max-w-[900px]">
          <SheetHeader>
            <SheetTitle>{editingItem ? '编辑项目' : '新增项目'}</SheetTitle>
          </SheetHeader>
          <div className="mt-6 p-4">
            <ProjectForm
              initialData={editingItem}
              onSuccess={handleSuccess}
              onCancel={() => setIsFormOpen(false)}
            />
          </div>
        </SheetContent>
      </Sheet>
    </div>
  );
}
