import { useState } from 'react';
import { NotionEditor } from '@/components/editor';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';

export default function NotionTest() {
  const [content, setContent] = useState(`
    <h1>欢迎使用 Notion 风格编辑器！</h1>
    <p>这是一个功能丰富的富文本编辑器，具有以下特性：</p>
    <ul>
      <li><strong>浮动菜单</strong>：在空行时显示，可以快速插入不同类型的内容</li>
      <li><strong>气泡菜单</strong>：选中文本时显示，提供格式化选项</li>
      <li><strong>拖拽手柄</strong>：悬停时显示，可以拖拽移动内容块</li>
    </ul>
    <h2>使用说明</h2>
    <p>试试以下操作：</p>
    <ol>
      <li>将光标放在空行，查看浮动菜单</li>
      <li>选中一些文本，查看气泡菜单</li>
      <li>悬停在段落上，查看左侧的拖拽手柄</li>
      <li>输入 "/" 来查看更多命令</li>
    </ol>
    <blockquote>
      <p>这是一个引用块，展示了编辑器的样式效果。</p>
    </blockquote>
    <p>你还可以添加<mark>高亮文本</mark>、<code>行内代码</code>、<a href="#">链接</a>等。</p>
  `);

  const handleContentChange = (newContent: string) => {
    setContent(newContent);
    console.log('Content changed:', newContent);
  };

  const handleSave = () => {
    console.log('Saving content:', content);
    alert('内容已保存到控制台');
  };

  const loadSampleContent = () => {
    setContent(`
      <h1>示例文档</h1>
      <p>这是一个示例文档，展示了编辑器的各种功能。</p>
      
      <h2>文本格式</h2>
      <p>支持<strong>粗体</strong>、<em>斜体</em>、<u>下划线</u>、<s>删除线</s>和<code>行内代码</code>。</p>
      
      <h3>列表</h3>
      <ul>
        <li>无序列表项 1</li>
        <li>无序列表项 2
          <ul>
            <li>嵌套列表项</li>
          </ul>
        </li>
      </ul>
      
      <ol>
        <li>有序列表项 1</li>
        <li>有序列表项 2</li>
      </ol>
      
      <h3>任务列表</h3>
      <ul data-type="taskList">
        <li data-type="taskItem" data-checked="true">已完成的任务</li>
        <li data-type="taskItem" data-checked="false">待完成的任务</li>
      </ul>
      
      <h3>引用</h3>
      <blockquote>
        <p>这是一个引用块，可以用来突出显示重要内容。</p>
      </blockquote>
      
      <h3>代码块</h3>
      <pre><code>function hello() {
  console.log("Hello, World!");
}</code></pre>
      
      <p>试试选中文本查看气泡菜单，或者在空行查看浮动菜单！</p>
    `);
  };

  return (
    <div className="container mx-auto p-6 max-w-5xl">
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* 编辑器区域 */}
        <div className="lg:col-span-2">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                Notion 风格编辑器
                <Badge variant="secondary">增强版</Badge>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <NotionEditor
                content={content}
                onChange={handleContentChange}
                placeholder="输入 '/' 来查看命令，或者开始输入..."
                className="min-h-[500px]"
              />
            </CardContent>
          </Card>
        </div>

        {/* 控制面板 */}
        <div className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>操作面板</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <Button onClick={handleSave} className="w-full">
                保存内容
              </Button>
              <Button 
                variant="outline" 
                onClick={() => setContent('')}
                className="w-full"
              >
                清空内容
              </Button>
              <Button 
                variant="outline" 
                onClick={loadSampleContent}
                className="w-full"
              >
                加载示例内容
              </Button>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>功能说明</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2 text-sm">
              <div className="space-y-1">
                <h4 className="font-medium">浮动菜单</h4>
                <p className="text-gray-600">在空行时显示，提供快速插入选项</p>
              </div>
              <div className="space-y-1">
                <h4 className="font-medium">气泡菜单</h4>
                <p className="text-gray-600">选中文本时显示，提供格式化选项</p>
              </div>
              <div className="space-y-1">
                <h4 className="font-medium">拖拽手柄</h4>
                <p className="text-gray-600">悬停时显示，可以拖拽移动内容</p>
              </div>
              <div className="space-y-1">
                <h4 className="font-medium">快捷键</h4>
                <p className="text-gray-600">
                  Ctrl+B 粗体<br/>
                  Ctrl+I 斜体<br/>
                  Ctrl+U 下划线<br/>
                  Ctrl+Z 撤销<br/>
                  Ctrl+Y 重做
                </p>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>内容预览</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-xs">
                <p className="text-gray-600 mb-2">字符数: {content.length}</p>
                <pre className="bg-gray-100 p-2 rounded text-xs overflow-auto max-h-32 whitespace-pre-wrap">
                  {content.substring(0, 200)}...
                </pre>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
