import React, { useState } from 'react';
import { useRequest } from 'umi';
import { LimitRecord, fetchLimitRecords } from '@/service/cases/download';
import { DownloadRecordTable } from './components/download-record-table';
import { Page } from '@/types';

export default function DownloadRecordPage() {
    const [pageData, setPageData] = useState<Page<LimitRecord>>();

    const { run: fetchLimitRecordsRun } = useRequest(fetchLimitRecords, {
        manual: true,
        onSuccess(res) {
            setPageData(res);
        }
    });

    return (
        <div className="p-4">
            <div className="flex justify-between items-center mb-4">
                <h1 className="text-2xl font-bold">下载记录列表</h1>
            </div>
            <DownloadRecordTable
                onQuery={fetchLimitRecordsRun}
                data={pageData}
            />
        </div>
    );
}
