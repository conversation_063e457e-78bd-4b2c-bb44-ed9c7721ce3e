import React from 'react';
import { DataTableFilterField } from '@/components/data-table/types';
import { LimitRecord } from '@/service/cases/download';
import { fetchTags } from '@/service/cases/download';

export function getFilterFields(): DataTableFilterField<LimitRecord>[] {
  return [
    {
      id: 'tag',
      label: '标签',
      placeholder:'输入标签搜索',
    },
    {
      id: 'accountCode',
      label: '用户账号',
      placeholder:'输入用户账号搜索',
      virtual: true
    },
    {
      id: 'accountName',
      label: '用户名称',
      placeholder:'输入用户名称搜索',
      virtual: true
    },
  ];
}
