import React from 'react';
import { ColumnDef } from '@tanstack/react-table';
import { LimitRecord } from '@/service/cases/download';
import { Badge } from '@/components/ui/badge';
import { FileIcon, ChevronDown, ChevronRight } from 'lucide-react';
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { AttachmentPreviewSheet } from './attachment-preview-sheet';

export function getColumns(): ColumnDef<LimitRecord>[] {
  return [
    {
      id: 'expander',
      header: () => null,
      cell: ({ row }) => {
        return (
          <Button
            variant="ghost"
            onClick={() => row.toggleExpanded()}
            className="p-0 h-8 w-8"
          >
            {row.getIsExpanded() ? (
              <ChevronDown className="h-4 w-4" />
            ) : (
              <ChevronRight className="h-4 w-4" />
            )}
          </Button>
        );
      },
    },
    {
      accessorKey: 'tag',
      header: '标签',
      cell: ({ row }) => {
        const tag = row.getValue('tag') as string;
        return (
          <Badge variant="outline" className="px-2 py-1">
            {tag}
          </Badge>
        );
      },
    },
    {
      accessorKey: 'user.name',
      header: '用户名称',
      cell: ({ row }) => {
        const user = row.original.account.user;
        return user ? user.name : '-';
      },
    },
    {
      accessorKey: 'user.code',
      header: '用户账号',
      cell: ({ row }) => {
        const user = row.original.account.user;
        return user ? user.code : '-';
      },
    },
    {
      accessorKey: 'counts',
      header: '下载次数',
      cell: ({ row }) => row.getValue('counts'),
    },
    {
      accessorKey: 'attachments',
      header: ({ column }) => null,
      cell: ({ row }) => {
        const attachments = row.original.attachments;
        
        if (!attachments || attachments.length === 0) {
          return <div className="text-gray-500 italic">无附件</div>;
        }
        
        return (
          <Card className="w-full">
            <CardContent className="p-4">
              <AttachmentPreviewSheet attachments={attachments} />
            </CardContent>
          </Card>
        );
      },
      meta: {
        title: "附件信息",
        expend: true
      },
      enableSorting: false,
      enableHiding: true,
    },
  ];
}
