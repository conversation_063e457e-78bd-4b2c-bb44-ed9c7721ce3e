import React from 'react';
import { useDataTable } from '@/components/data-table/hooks/use-data-table';
import { DataTable } from '@/components/data-table/data-table';
import { DataTableToolbar } from '@/components/data-table/data-table-toolbar';
import { DataTableFilterField } from '@/components/data-table/types';
import { LimitRecord, LimitRecordSpecification } from '@/service/cases/download';
import { Page, PageQuery, Specification } from '@/types';
import { getColumns } from './download-record-table-columns';
import { getFilterFields } from './download-record-table-filters';

interface DownloadRecordTableProps {
  onQuery: (query: PageQuery<Specification<LimitRecord>>) => Promise<Page<LimitRecord>>;
  data?: Page<LimitRecord>;
}

export function DownloadRecordTable({ onQuery, data }: DownloadRecordTableProps) {
  const columns = React.useMemo(() => getColumns(), []);
  
  const [filterFields, setFilterFields] = React.useState<DataTableFilterField<LimitRecord>[]>(getFilterFields());

  const { table } = useDataTable({
    data: data?.data || [],
    columns,
    pageCount: Math.ceil((data?.total || 0) / (data?.pageSize || 10)),
    filterFields,
    initialState: {
      pagination: {
        pageIndex: 0,
        pageSize: 30
      },
      sorting: [{ id: 'id', desc: true }],
    },
    getRowId: (originalRow) => originalRow.id,
    clearOnDefault: false,
    shallow: true,
  });

  React.useEffect(() => {
    setFilterFields(getFilterFields());
  }, [data]);

  const handleFilterChange = (pageQuery: PageQuery<Specification<LimitRecord>>) => {
    onQuery(pageQuery);
  };

  return (
    <>
      <DataTable table={table} className="h-fit">
        <DataTableToolbar 
          type={'sql'}
          onFilterChange={handleFilterChange} 
          table={table} 
          filterFields={filterFields} 
        />
      </DataTable>
    </>
  );
}
