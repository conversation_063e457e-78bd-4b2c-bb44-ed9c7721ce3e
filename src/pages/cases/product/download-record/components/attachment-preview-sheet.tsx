import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from '@/components/ui/sheet';
import { Button } from '@/components/ui/button';
import { FileIcon } from 'lucide-react';
import FilePreview from '@/components/file-preview';

interface AttachmentFile {
  id: string;
  originalFilename: string;
  metadata?: {
    previewUrl: string;
  };
}

interface Attachment {
  file: AttachmentFile;
}

interface AttachmentPreviewSheetProps {
  attachments: Attachment[];
}

export function AttachmentPreviewSheet({ attachments }: AttachmentPreviewSheetProps) {
  const [open, setOpen] = useState(false);
  const [selectedFile, setSelectedFile] = useState<AttachmentFile | null>(null);

  const handleOpenPreview = (file: AttachmentFile) => {
    setSelectedFile(file);
    setOpen(true);
  };

  return (
    <>
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">附件列表 ({attachments.length})</h3>
        
        <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
          {attachments.map((attachment, index) => {
            if (!attachment.file) return null;
            
            const file = attachment.file;
            const hasPreview = !!file.metadata?.previewUrl;
            
            return (
              <div 
                key={file.id || index} 
                className={`border rounded-lg p-4 flex flex-col ${hasPreview ? 'cursor-pointer hover:border-blue-300 hover:shadow-md transition-all' : ''}`}
                onClick={hasPreview ? () => handleOpenPreview(file) : undefined}
              >
                <div className="flex items-center mb-2">
                  <FileIcon className="h-5 w-5 mr-2 text-blue-500 flex-shrink-0" />
                  <span className="font-medium truncate">{file.originalFilename || '未知文件'}</span>
                </div>
                
                {hasPreview && (
                  <div className="mt-auto pt-2 text-sm text-blue-500">
                    点击预览
                  </div>
                )}
              </div>
            );
          })}
        </div>
      </div>

      <Sheet open={open} onOpenChange={setOpen}>
        <SheetContent className="sm:min-w-full sm:min-h-full">
          <SheetHeader className="p-6 border-b">
            <SheetTitle>
              {selectedFile?.originalFilename || '文件预览'}
            </SheetTitle>
          </SheetHeader>
          <div className="flex-1 overflow-hidden">
            {selectedFile?.metadata?.previewUrl && (
              <FilePreview 
                url={selectedFile.metadata.previewUrl} 
                width="100%" 
                height="100%" 
                className="w-full h-full"
              />
            )}
          </div>
        </SheetContent>
      </Sheet>
    </>
  );
}
