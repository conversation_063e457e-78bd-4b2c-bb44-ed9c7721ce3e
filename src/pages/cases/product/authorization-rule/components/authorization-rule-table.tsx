import React from 'react';
import {DataTable} from "@/components/data-table/data-table";
import {DataTableToolbar} from "@/components/data-table/data-table-toolbar";
import {useLocalDataTable} from "@/components/data-table/hooks/use-local-data-table";
import {DataTableRowAction} from "@/components/data-table/types";
import {Page, PageQuery, Specification} from "@/types";
import {AuthorizationRule} from "@/service/cases/authorization-rule";
import {filterFields, getColumns} from "./authorization-rule-table-columns";
import {useDataTable} from "@/components/data-table/hooks/use-data-table";

interface AuthorizationRuleTableProps {
    data?: Page<AuthorizationRule>;
    onQuery: (query: PageQuery<Specification<AuthorizationRule>>) => void;
    setRowAction: (action: DataTableRowAction<AuthorizationRule>) => void;
}

export function AuthorizationRuleTable({data, onQuery, setRowAction}: AuthorizationRuleTableProps) {
    // 使用外部定义的列和过滤字段
    const columns = getColumns(setRowAction);

    const {table} = useDataTable({
        pageCount: data ? Math.ceil(data.total / data.pageSize) : 0,
        data: data?.data || [],
        columns,
        filterFields,
        initialState: {
            sorting: [{id: 'createdTime', desc: true}],
        }
    });

    return (
        <DataTable table={table}>
            <DataTableToolbar onFilterChange={onQuery} table={table} filterFields={filterFields}/>
        </DataTable>
    );
}
