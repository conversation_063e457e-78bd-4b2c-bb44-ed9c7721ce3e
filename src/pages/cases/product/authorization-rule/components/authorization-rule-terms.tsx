import React, { useState, useEffect, useCallback, memo } from 'react';
import { ScrollArea } from "@/components/ui/scroll-area";
import { Skeleton } from "@/components/ui/skeleton";
import Terms from "@/components/condition-react/terms";
import { Field, Group as ConditionGroup } from "@/components/condition-react/types";

// 定义字段
const sampleFields: Field[] = [
    {
        fieldName: "标签",
        fieldCode: "tags",
        type: "array",
        children: [],
    },
];

// 接口定义
export interface AutoConditionData {
    conditionGroup: ConditionGroup;
}

interface AutoConditionProps {
    value?: ConditionGroup;
    onChange: (data: AutoConditionData) => void;
}

// 使用memo包装组件防止不必要的重渲染
const AuthorizationRuleTermsComponent = ({ value, onChange }: AutoConditionProps) => {
    // 状态管理
    const [loading, setLoading] = useState(false);
    const [localGroup, setLocalGroup] = useState<ConditionGroup>(value || {
        conditions: [],
        groups: [],
        logic: "AND"
    });
    
    // 监听 value 变化
    useEffect(() => {
        if (value) {
            console.log('条件组件收到新的value:', value);
            
            // 确保数据结构完整
            const normalizedGroup: ConditionGroup = {
                conditions: Array.isArray(value.conditions) ? value.conditions : [],
                groups: Array.isArray(value.groups) ? value.groups : [],
                logic: value.logic || 'AND'
            };
            
            setLocalGroup(normalizedGroup);
        }
    }, [value]);
    
    // 使用useCallback缓存回调函数
    const handleConditionChange = useCallback((group: ConditionGroup) => {
        setLocalGroup(group);
        onChange({
            conditionGroup: group
        });
    }, [onChange]);
    
    // 加载状态显示
    if (loading) {
        return <Skeleton className="w-full h-[200px]"/>;
    }

    return (
        <ScrollArea className="w-full">
            <div className="p-2 bg-muted/20 rounded-md">
                <Terms
                    group={localGroup}
                    fields={sampleFields}
                    root={true}
                    onChange={handleConditionChange}
                />
            </div>
        </ScrollArea>
    );
};

// 导出组件
export const AuthorizationRuleTerms = memo(AuthorizationRuleTermsComponent);
