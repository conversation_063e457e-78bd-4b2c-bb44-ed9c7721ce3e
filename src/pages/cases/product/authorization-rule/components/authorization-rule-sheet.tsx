import React, { useState, useEffect, useCallback } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, SheetFooter } from '@/components/ui/sheet';
import { Button } from '@/components/ui/button';
import { AuthorizationRuleForm, AutoAuthorizationData } from './authorization-rule-form';
import { AuthorizationRule, AuthorizationRuleSave, saveRule } from '@/service/cases/authorization-rule';
import { toast } from '@/components/ui/use-toast';
import { convertToSpel } from '@/components/condition-react/spel-converter';

interface AuthorizationRuleSheetProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  selectedRule: AuthorizationRule | null;
  onSubmit: (data: AutoAuthorizationData) => void;
  onCancel: () => void;
}

export function AuthorizationRuleSheet({
  open,
  onOpenChange,
  selectedRule,
  onSubmit,
  onCancel
}: AuthorizationRuleSheetProps) {
  const [formData, setFormData] = useState<AutoAuthorizationData | null>(null);
  const [submitting, setSubmitting] = useState(false);

  // 处理表单数据变化
  const handleFormDataChange = useCallback((data: AutoAuthorizationData) => {
    setFormData(data);
  }, []);

  // 处理取消
  const handleCancel = useCallback(() => {
    onCancel();
  }, [onCancel]);

  // 处理提交
  const handleSubmit = useCallback(async () => {
    if (!formData) return;
    
    try {
      setSubmitting(true);
      
      // 准备保存数据 - 注意新建时id传递undefined
      const saveData: AuthorizationRuleSave = {
        // 新建时传递undefined，而不是空字符串
        id: selectedRule?.id,
        descriptions: formData.description || '',
        terms: formData.conditionData.conditionGroup,
        expression: convertToSpel(formData.conditionData.conditionGroup),
        groups: formData.groupAuthData.selectedIds.map(id => ({ id }))
      };
      
      // 调用保存API
      await saveRule(saveData);
      
      toast({
        title: '保存成功',
        description: '自动授权规则已成功保存'
      });
      
      // 通知父组件
      onSubmit(formData);
    } catch (error: any) {
      toast({
        title: '保存失败',
        description: error?.message || '请稍后重试',
        variant: 'destructive'
      });
    } finally {
      setSubmitting(false);
    }
  }, [formData, selectedRule, onSubmit]);

  // 当Sheet关闭时重置状态
  useEffect(() => {
    if (!open) {
      setFormData(null);
      setSubmitting(false);
    }
  }, [open]);

  return (
    <Sheet open={open} onOpenChange={onOpenChange}>
      <SheetContent className="sm:min-w-full sm:min-h-full flex flex-col" side="right">
        <SheetHeader>
          <SheetTitle>{selectedRule ? '编辑' : '新建'}自动授权规则</SheetTitle>
        </SheetHeader>
        
        <div className="flex-1 overflow-hidden mt-6">
          <AuthorizationRuleForm
            resourceId={selectedRule?.id}
            onChange={handleFormDataChange}
          />
        </div>
        
        <SheetFooter className="mt-4 flex justify-end gap-2">
          <Button 
            variant="outline" 
            onClick={handleCancel}
          >
            取消
          </Button>
          <Button 
            onClick={handleSubmit}
            disabled={submitting || !formData}
          >
            {submitting ? '保存中...' : '保存规则'}
          </Button>
        </SheetFooter>
      </SheetContent>
    </Sheet>
  );
}
