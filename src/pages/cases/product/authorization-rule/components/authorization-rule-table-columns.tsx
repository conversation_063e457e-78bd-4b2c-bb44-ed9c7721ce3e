import React from 'react';
import {ColumnDef} from "@tanstack/react-table";
import {DataTableColumnHeader} from "@/components/data-table/data-table-column-header";
import {Button} from "@/components/ui/button";
import {Edit, Trash2} from "lucide-react";
import {AuthorizationRule} from "@/service/cases/authorization-rule";
import {DataTableFilterField, DataTableRowAction} from "@/components/data-table/types";
import { Badge } from '@/components/ui/badge';

// 过滤字段定义
export const filterFields: DataTableFilterField<AuthorizationRule>[] = [
    {
        id: 'descriptions',
        label: '规则描述',
        placeholder:'规则描述'
    }
];

// 表格列定义
export const getColumns = (
    setRowAction: (action: DataTableRowAction<AuthorizationRule>) => void
): ColumnDef<AuthorizationRule>[] => [
    {
        accessorKey: "descriptions",
        header: ({column}) => (
            <DataTableColumnHeader column={column} title="规则描述"/>
        ),
        meta:{
            title:'描述'
        }
    },
    {
        accessorKey: "groups",
        header: ({column}) => (
            <DataTableColumnHeader column={column} title="用户组"/>
        ),
        cell:({row})=>{
           return  row.original.groups.map((group, index) => (
                <Badge key={index} className="px-3 py-1 font-normal">
                    {group.name}
                </Badge>
            ))
        },
        meta:{
            title:'用户组'
        }
    },
    {
        id: "actions",
        cell: ({row}) => {
            return (
                <div className="flex items-center gap-2">
                    <Button
                        variant="ghost"
                        className="h-8 w-8 p-0"
                        onClick={() => setRowAction({
                            type: 'update',
                            row
                        })}
                    >
                        <Edit className="h-4 w-4"/>
                    </Button>
                    <Button
                        variant="ghost"
                        className="h-8 w-8 p-0"
                        onClick={() => setRowAction({
                            type: 'delete',
                            row
                        })}
                    >
                        <Trash2 className="h-4 w-4"/>
                    </Button>
                </div>
            );
        },
    },
];
