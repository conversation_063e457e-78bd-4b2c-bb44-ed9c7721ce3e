import React, { useState, useEffect, useRef, useCallback, memo } from 'react';
import { AuthorizationRuleTerms, AutoConditionData } from './authorization-rule-terms';
import { AuthorizationRuleGroup, AutoGroupAuthData } from './authorization-rule-group';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { fetchRule, AuthorizationRule } from '@/service/cases/authorization-rule';
import { toast } from '@/components/ui/use-toast';
import { ScrollArea } from '@/components/ui/scroll-area';

export interface AutoAuthorizationData {
  description: string;
  conditionData: AutoConditionData;
  groupAuthData: AutoGroupAuthData;
}

interface AutoAuthorizationFormProps {
  resourceId?: string;
  onChange?: (data: AutoAuthorizationData) => void;
}

const AuthorizationRuleFormComponent = ({ resourceId, onChange }: AutoAuthorizationFormProps) => {
  // 状态管理
  const [description, setDescription] = useState('');
  const [conditionData, setConditionData] = useState<AutoConditionData>({ 
    conditionGroup: { conditions: [], groups: [], logic: 'AND' } 
  });
  const [groupAuthData, setGroupAuthData] = useState<AutoGroupAuthData>({ selectedIds: [] });
  const [loading, setLoading] = useState(false);
  const [ruleData, setRuleData] = useState<AuthorizationRule | null>(null);
  
  // 使用ref跟踪组件生命周期
  const isMounted = useRef(true);
  
  // 组件卸载时清理状态
  useEffect(() => {
    return () => {
      isMounted.current = false;
    };
  }, []);

  // 加载规则数据
  useEffect(() => {
    if (!resourceId) return;
    
    setLoading(true);
    
    fetchRule(resourceId)
      .then(response => {
        if (isMounted.current && response?.data) {
          console.log('加载到的规则数据:', response.data);
          setDescription(response.data.descriptions || '');
          
          // 检查terms结构
          console.log('条件数据(terms):', response.data.terms);
          setConditionData({ conditionGroup: response.data.terms });
          
          // 保存整个规则数据，用于传递给子组件
          setRuleData(response.data);
          
          // 如果有用户组数据，也设置它
          if (response.data.groups) {
            setGroupAuthData({ 
              selectedIds: response.data.groups.map(group => group.id) 
            });
          }
        }
      })
      .catch(() => {
        if (isMounted.current) {
          toast({
            title: '加载失败',
            description: '无法加载规则数据',
            variant: 'destructive'
          });
        }
      })
      .finally(() => {
        if (isMounted.current) {
          setLoading(false);
        }
      });
  }, [resourceId]);
  
  // 当数据变化时通知父组件
  useEffect(() => {
    if (onChange) {
      onChange({
        description,
        conditionData,
        groupAuthData
      });
    }
  }, [description, conditionData, groupAuthData, onChange]);

  // 处理条件变化 - 使用useCallback缓存回调函数
  const handleConditionChange = useCallback((data: AutoConditionData) => {
    setConditionData(data);
  }, []);

  // 处理用户组授权变化 - 使用useCallback缓存回调函数
  const handleGroupAuthChange = useCallback((data: AutoGroupAuthData) => {
    setGroupAuthData(data);
  }, []);
  
  // 处理描述变化
  const handleDescriptionChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    setDescription(e.target.value);
  }, []);

  return (
    <ScrollArea className="h-[calc(100vh-200px)]">
      <div className="flex flex-col gap-4 p-4">
      <Card className="mb-4">
        <CardHeader>
          <CardTitle>规则信息</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid w-full items-center gap-4">
            <div className="flex flex-col space-y-1.5">
              <Label htmlFor="description">规则描述</Label>
              <Input 
                id="description" 
                placeholder="请输入规则描述" 
                value={description}
                onChange={handleDescriptionChange}
              />
            </div>
          </div>
        </CardContent>
      </Card>
      
      <Card className="mb-4">
        <CardHeader>
          <CardTitle>条件设置</CardTitle>
        </CardHeader>
        <CardContent>
          <AuthorizationRuleTerms 
            value={conditionData.conditionGroup} 
            onChange={handleConditionChange} 
          />
        </CardContent>
      </Card>
      
      <Card className="mb-4">
        <CardHeader>
          <CardTitle>用户组授权</CardTitle>
        </CardHeader>
        <CardContent>
          <AuthorizationRuleGroup
            resourceId={resourceId}
            initialGroups={ruleData?.groups || []}
            onChange={handleGroupAuthChange} 
          />
        </CardContent>
      </Card>
      </div>
    </ScrollArea>
  );
};

// 使用memo包装组件防止不必要的重渲染
export const AuthorizationRuleForm = memo(AuthorizationRuleFormComponent);
