import React, {useEffect, useState} from 'react';
import {But<PERSON>} from "@/components/ui/button";
import {Plus} from "lucide-react";
import {useRequest} from "umi";
import {AuthorizationRule, fetchRules, deleteRule} from "@/service/cases/authorization-rule";
import {DataTableRowAction} from "@/components/data-table/types";
import {Page} from '@/types';
import {AuthorizationRuleSheet} from './components/authorization-rule-sheet';
import {AuthorizationRuleTable} from './components/authorization-rule-table';
import {toast} from "@/components/ui/use-toast";
import {AutoAuthorizationData} from "./components/authorization-rule-form";

export default function AuthorizationRulePage() {
    const [rowAction, setRowAction] = useState<DataTableRowAction<AuthorizationRule> | null>(null);
    const [isFormSheetOpen, setIsFormSheetOpen] = useState(false);
    const [pageData, setPageData] = useState<Page<AuthorizationRule>>();
    const [selectedRule, setSelectedRule] = useState<AuthorizationRule | null>(null);

    const {run: fetchRulesRun, refresh: refreshRules} = useRequest(fetchRules, {
        manual: true,
        onSuccess(res) {
            setPageData(res);
        }
    });

    const {run: deleteRuleRun} = useRequest(deleteRule, {
        manual: true,
        onSuccess() {
            toast({
                title: "删除成功",
                description: "自动授权规则已成功删除"
            });
            fetchRulesRun({
                page: 0,
                pageSize: 10,
                specification: {}
            });
        }
    });

    // 处理新增操作
    const handleAdd = () => {
        setSelectedRule(null);
        setIsFormSheetOpen(true);
    };

    // 处理表格操作
    useEffect(() => {
        if (rowAction?.type === 'update') {
            setSelectedRule(rowAction.row.original);
            setIsFormSheetOpen(true);
        } else if (rowAction?.type === 'delete' && rowAction.row.original.id) {
            deleteRuleRun(rowAction.row.original.id);
        }
    }, [rowAction]);

    return (
        <div className="p-4">
            <div className="flex justify-between items-center mb-4">
                <h1 className="text-2xl font-bold">自动授权规则管理</h1>
                <Button onClick={handleAdd}>
                    <Plus className="h-4 w-4 mr-2"/>
                    新建规则
                </Button>
            </div>
            <AuthorizationRuleTable
                onQuery={fetchRulesRun}
                setRowAction={setRowAction}
                data={pageData}
            />
            <AuthorizationRuleSheet
                open={isFormSheetOpen}
                onOpenChange={(open) => setIsFormSheetOpen(open)}
                selectedRule={selectedRule}
                onSubmit={(data: AutoAuthorizationData) => {
                    // 关闭Sheet并刷新数据
                    setIsFormSheetOpen(false);
                    refreshRules();
                }}
                onCancel={() => {
                    // 关闭Sheet
                    setIsFormSheetOpen(false);
                }}
            />
        </div>
    );
}
