import React, {useState} from 'react';
import {Button} from "@/components/ui/button";
import {Input} from "@/components/ui/input";
import {Label} from "@/components/ui/label";
import {useRequest} from "umi";
import {fetchClassifiesTree, ProductClassify, ProductClassifySave, saveClassify} from "@/service/cases/product";
import {ChevronDown, ChevronRight, Folder, Search} from "lucide-react";
import {Popover, PopoverContent, PopoverTrigger,} from "@/components/ui/popover";
import {cn} from "@/lib/utils";

interface ClassifyFormProps {
  parentItem?: ProductClassify | null;
  onSuccess: () => void;
  isEditing?: boolean;
}

const ClassifyTreeItem: React.FC<{
  item: ProductClassify;
  level: number;
  selectedId: string;
  onSelect: (id: string) => void;
  searchValue: string;
}> = ({ item, level, selectedId, onSelect, searchValue }) => {
  const [isOpen, setIsOpen] = useState(false);
  const hasChildren = item.children && item.children.length > 0;
  const isMatch = item.name.toLowerCase().includes(searchValue.toLowerCase());
  const hasMatchingChildren = hasChildren && item.children?.some(child => 
    child.name.toLowerCase().includes(searchValue.toLowerCase())
  );

  // 如果有匹配的子项，自动展开
  React.useEffect(() => {
    if (hasMatchingChildren && !isOpen) {
      setIsOpen(true);
    }
  }, [hasMatchingChildren, isOpen]);

  // 如果没有匹配项且没有匹配的子项，不显示
  if (!isMatch && !hasMatchingChildren) {
    return null;
  }

  return (
    <>
      <div
        onClick={() => {
          onSelect(item.id);
          if (hasChildren) {
            setIsOpen(!isOpen);
          }
        }}
        className={cn(
          "cursor-pointer flex items-center px-2 py-1.5 hover:bg-accent rounded-sm",
          level > 0 && "ml-4",
          selectedId === item.id && "bg-accent"
        )}
      >
        {hasChildren ? (
          <Button
            variant="ghost"
            size="sm"
            className="h-6 w-6 p-0 mr-2"
            onClick={(e) => {
              e.stopPropagation();
              setIsOpen(!isOpen);
            }}
          >
            {isOpen ? (
              <ChevronDown className="h-4 w-4" />
            ) : (
              <ChevronRight className="h-4 w-4" />
            )}
          </Button>
        ) : (
          <div className="w-6" />
        )}
        <span className={cn(
          "flex-1",
          selectedId === item.id && "font-medium"
        )}>
          {item.name}
        </span>
      </div>
      {hasChildren && isOpen && (
        <div>
          {item.children?.map((child) => (
            <ClassifyTreeItem
              key={child.id}
              item={child}
              level={level + 1}
              selectedId={selectedId}
              onSelect={onSelect}
              searchValue={searchValue}
            />
          ))}
        </div>
      )}
    </>
  );
};

export function ClassifyForm({ parentItem, onSuccess, isEditing }: ClassifyFormProps) {
  const [name, setName] = useState('');
  const [code, setCode] = useState('');
  const [selectedParentId, setSelectedParentId] = useState<string>('');
  const [classifies, setClassifies] = useState<ProductClassify[]>([]);
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [searchValue, setSearchValue] = useState('');

  // 在组件挂载或 isEditing/parentItem 变化时更新表单数据
  React.useEffect(() => {
    if (isEditing && parentItem) {
      // 编辑模式：设置当前项的信息
      setName(parentItem.name || '');
      setCode(parentItem.code || '');
      setSelectedParentId(parentItem.parent?.id || '');
    } else if (!isEditing && parentItem) {
      // 添加子分类模式：清空表单，设置父分类
      setName('');
      setCode('');
      setSelectedParentId(parentItem.id || '');
    } else {
      // 新增模式：清空所有
      setName('');
      setCode('');
      setSelectedParentId('');
    }
  }, [isEditing, parentItem]);

  const { run: runSaveClassify, loading } = useRequest(saveClassify, {
    manual: true,
    onSuccess: () => {
      onSuccess();
    }
  });

  const { data: treeData } = useRequest(fetchClassifiesTree, {
    onSuccess: (res) => {
      if (res) {
        setClassifies(res);
      } else {
        setClassifies([]);
      }
    },
    onError: () => {
      setClassifies([]);
    }
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    const data: ProductClassifySave = {
      id: isEditing ? parentItem?.id || undefined : undefined,
      code: code || '',
      name: name || '',
      parentId: selectedParentId || undefined
    };
    runSaveClassify(data);
  };

  // 递归查找分类项
  const findClassifyItem = (items: ProductClassify[], targetId: string): ProductClassify | null => {
    if (!targetId || !items?.length) return null;
    
    for (const item of items) {
      if (item.id === targetId) {
        return item;
      }
      if (item.children?.length) {
        const found = findClassifyItem(item.children, targetId);
        if (found) {
          return found;
        }
      }
    }
    return null;
  };

  // 获取选中分类的名称
  const getSelectedClassifyName = () => {
    if (!selectedParentId) {
      return "选择父分类";
    }
    const selectedItem = findClassifyItem(classifies, selectedParentId);
    return selectedItem?.name || "选择父分类";
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4 mt-4">
      <div className="space-y-2">
        <Label htmlFor="code">分类编码</Label>
        <Input
          id="code"
          value={code || ''}
          onChange={(e) => setCode(e.target.value)}
          placeholder="请输入分类编码"
          required
          readOnly={isEditing && !parentItem?.parent}
          disabled={isEditing && !parentItem?.parent}
        />
      </div>
      <div className="space-y-2">
        <Label htmlFor="name">分类名称</Label>
        <Input
          id="name"
          value={name || ''}
          onChange={(e) => setName(e.target.value)}
          placeholder="请输入分类名称"
          required
        />
      </div>
      <div className="space-y-2">
        <Label>父分类</Label>
        <Popover open={isMenuOpen && !isEditing} onOpenChange={(open) => !isEditing && setIsMenuOpen(open)}>
          <PopoverTrigger asChild>
            <Button
              variant="outline"
              role="combobox"
              className="w-full justify-between"
              disabled={isEditing}
            >
              {getSelectedClassifyName()}
              <ChevronDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-[300px] p-0">
            <div className="p-2 border-b">
              <div className="relative">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="搜索分类..."
                  value={searchValue || ''}
                  onChange={(e) => setSearchValue(e.target.value)}
                  className="pl-8"
                />
              </div>
            </div>
            <div className="max-h-[300px] overflow-auto p-1">
              <div
                className={cn(
                  "p-1 cursor-pointer hover:bg-accent rounded-sm",
                  selectedParentId === '' && "bg-accent"
                )}
                onClick={() => {
                  setSelectedParentId('');
                  setIsMenuOpen(false);
                }}
              >
                <div className="flex items-center">
                  <Folder className="h-4 w-4 mr-2" />
                  <span>根分类</span>
                </div>
              </div>
              {classifies.map((item) => (
                <ClassifyTreeItem
                  key={item.id}
                  item={item}
                  level={0}
                  selectedId={selectedParentId}
                  onSelect={(id) => {
                    setSelectedParentId(id);
                    setIsMenuOpen(false);
                  }}
                  searchValue={searchValue}
                />
              ))}
            </div>
          </PopoverContent>
        </Popover>
      </div>
      <div className="flex justify-end gap-4">
        <Button variant="outline" type="button" onClick={onSuccess}>
          取消
        </Button>
        <Button type="submit" disabled={loading}>
          {loading ? '保存中...' : '保存'}
        </Button>
      </div>
    </form>
  );
} 