import React, {useEffect, useState} from 'react';
import {But<PERSON>} from "@/components/ui/button";
import {Plus} from "lucide-react";
import {Access, useAccess, useRequest} from "umi";
import {DownloadControl, fetchDownloadControlPage} from "@/service/cases/download";
import {DownloadControlTable} from './components/download-control-table';
import {DataTableRowAction} from "@/components/data-table/types";
import {DownloadControlFormSheet} from '@/pages/cases/product/download/components/download-control-form-sheet';
import {Page, PageQuery, Specification} from '@/types';

export default function DownloadControlPage() {
    const [rowAction, setRowAction] = useState<DataTableRowAction<DownloadControl> | null>(null);
    const [isFormSheetOpen, setIsFormSheetOpen] = useState(false);
    const [pageData, setPageData] = useState<Page<DownloadControl>>();
    const [selectedControl, setSelectedControl] = useState<DownloadControl | null>(null);
    const access = useAccess();

    const {run: fetchDownloadControlRun} = useRequest(fetchDownloadControlPage, {
        manual: true,
        onSuccess(res){
            setPageData(res);
        }
    });

    // 处理新增操作
    const handleAdd = () => {
        setSelectedControl(null);
        setIsFormSheetOpen(true);
    };

    // 处理表格操作
    useEffect(() => {
        if (rowAction?.type === 'update') {
            setSelectedControl(rowAction.row.original);
            setIsFormSheetOpen(true);
        }
    }, [rowAction]);
    return (
        <div className="p-4">
            <div className="flex justify-between items-center mb-4">
                <h1 className="text-2xl font-bold">下载控制管理</h1>
                    <Button onClick={handleAdd}>
                        <Plus className="h-4 w-4 mr-2"/>
                        新建控制
                    </Button>
            </div>
            <DownloadControlTable
                onQuery={fetchDownloadControlRun}
                setRowAction={setRowAction}
                data={pageData}
            />
            <DownloadControlFormSheet
                open={isFormSheetOpen}
                onOpenChange={setIsFormSheetOpen}
                downloadControl={selectedControl}
                onSuccess={() => {
                    setIsFormSheetOpen(false);
                    setRowAction(null);
                }}
            />
        </div>
    );
}
