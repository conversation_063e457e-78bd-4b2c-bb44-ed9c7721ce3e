import React from 'react';
import { Sheet, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, SheetTitle } from "@/components/ui/sheet";
import { DownloadControl } from "@/service/cases/download";
import { NewDownloadControlForm } from "./new-download-control-form";

interface DownloadControlFormSheetProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  downloadControl: DownloadControl | null;
  onSuccess?: () => void;
}

export function DownloadControlFormSheet({
  open,
  onOpenChange,
  downloadControl,
  onSuccess
}: DownloadControlFormSheetProps) {

  return (
    <Sheet open={open} onOpenChange={onOpenChange}>
      <SheetContent className="sm:min-w-full sm:min-h-full">
        <SheetHeader>
          <SheetTitle>{downloadControl ? "编辑下载控制" : "新建下载控制"}</SheetTitle>
        </SheetHeader>
        <div className="py-4 p-4">
          <NewDownloadControlForm 
            downloadControl={downloadControl}
            onSuccess={onSuccess}
            onCancel={() => onOpenChange(false)}
          />
        </div>
      </SheetContent>
    </Sheet>
  );
}
