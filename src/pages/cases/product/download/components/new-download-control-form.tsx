import React, {useEffect, useState} from 'react';
import {Form, FormControl, FormField, FormItem, FormLabel, FormMessage} from "@/components/ui/form";
import {Input} from "@/components/ui/input";
import {Button} from "@/components/ui/button";
// import {Select, SelectContent, SelectItem, SelectTrigger, SelectValue} from "@/components/ui/select";
import {zodResolver} from "@hookform/resolvers/zod";
import {useForm} from "react-hook-form";
import * as z from "zod";
import {useRequest} from "umi";
import {DownloadControl, DownloadControlSave, saveDownloadControl} from "@/service/cases/download";
import {Group} from "@/service/group";
import {toast} from "sonner";
import {GroupSelector} from "./group-selector";

interface NewDownloadControlFormProps {
    downloadControl: DownloadControl | null;
    onSuccess?: () => void;
    onCancel?: () => void;
}

const formSchema = z.object({
    id: z.string().optional(),
    tag: z.string().min(1, "请选择标签"),
    limit: z.coerce.number().min(0, "下载数量限制必须大于等于0,0代表无限制"),
});

export function NewDownloadControlForm({
                                           downloadControl,
                                           onSuccess,
                                           onCancel
                                       }: NewDownloadControlFormProps) {
    const [selectedGroups, setSelectedGroups] = useState<Group[]>([]);
    const {run: saveRun, loading} = useRequest(saveDownloadControl, {
        manual: true,
        onSuccess: () => {
            toast.success(downloadControl ? "更新成功" : "创建成功");
            onSuccess?.();
        }
    });

    const form = useForm<z.infer<typeof formSchema>>({
        resolver: zodResolver(formSchema),
        defaultValues: {
            id: "",
            tag: "",
            limit: 1,
        },
    });

    useEffect(() => {
        if (downloadControl) {
            form.reset({
                id: downloadControl.id,
                tag: downloadControl.tag,
                limit: downloadControl.limits,
            });
            setSelectedGroups(downloadControl.groups || []);
        } else {
            form.reset({
                id: undefined,
                tag: "",
                limit: 1,
            });
            setSelectedGroups([]);
        }
    }, [downloadControl]);

    const onSubmit = (values: z.infer<typeof formSchema>) => {
        if (selectedGroups.length === 0) {
            toast.error("请至少选择一个用户组");
            return;
        }

        const data: DownloadControlSave = {
            id: values.id,
            tag: values.tag,
            limits: values.limit,
            groups: selectedGroups,
        };
        saveRun(data);
    };

    const handleGroupChange = (data: { selectedIds: string[], groups: Group[] }) => {
        setSelectedGroups(data.groups);
    };

    return (
        <div className="space-y-6">
            <div className="w-full">
                <div className="mb-6">
                    <h3 className="text-lg font-medium mb-4">基本信息</h3>
                    <div className="p-4 border rounded-md">
                        <Form {...form}>
                            <form className="space-y-4">
                                <div className="grid grid-cols-2 gap-4">
                                    <FormField
                                        control={form.control}
                                        name="tag"
                                        render={({field}) => (
                                            <FormItem>
                                                <FormLabel>标签</FormLabel>
                                                <FormControl>
                                                    <Input placeholder="请输入标签" {...field} />
                                                </FormControl>
                                                <FormMessage/>
                                            </FormItem>
                                        )}
                                    />

                                    <FormField
                                        control={form.control}
                                        name="limit"
                                        render={({field}) => (
                                            <FormItem>
                                                <FormLabel>下载数量限制</FormLabel>
                                                <FormControl>
                                                    <Input type="number" min={1} {...field} />
                                                </FormControl>
                                                <FormMessage/>
                                            </FormItem>
                                        )}
                                    />
                                </div>
                            </form>
                        </Form>
                    </div>
                </div>

                <div>
                    <h3 className="text-lg font-medium mb-4">用户组设置</h3>
                    <div className="border rounded-md p-4">
                        <GroupSelector
                            onChange={handleGroupChange}
                            initialSelectedGroups={selectedGroups}
                        />
                    </div>
                </div>
            </div>

            <div className="flex justify-end space-x-2 pt-4">
                <Button variant="outline" onClick={onCancel} type="button">
                    取消
                </Button>
                <Button type="button" disabled={loading} onClick={form.handleSubmit(onSubmit)}>
                    {downloadControl ? "更新" : "创建"}
                </Button>
            </div>
        </div>
    );
}
