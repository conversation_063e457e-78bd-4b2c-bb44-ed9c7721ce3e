import React from 'react';
import {DataTableFilterField, DataTableRowAction} from "@/components/data-table/types";
import {useDataTable} from "@/components/data-table/hooks/use-data-table";
import {DataTable} from "@/components/data-table/data-table";
import {DataTableToolbar} from "@/components/data-table/data-table-toolbar";
import {getColumns} from '@/pages/cases/product/download/components/download-control-table-columns';
import {DownloadControl} from '@/service/cases/download';
import {Page, PageQuery, Specification} from '@/types';
import {getFilterFields} from '@/pages/cases/product/download/components/download-control-table-filters';
import {getActions} from "@/pages/cases/product/download/components/download-control-table-columns-action";
import {useAccess} from "umi";

interface DownloadControlTableProps {
  onQuery: (query: PageQuery<Specification<DownloadControl>>) => Promise<Page<DownloadControl>>;
  setRowAction: React.Dispatch<React.SetStateAction<DataTableRowAction<DownloadControl> | null>>;
  data?: Page<DownloadControl>;
}

export function DownloadControlTable({ onQuery, setRowAction, data }: DownloadControlTableProps) {
  const access = useAccess();
  
  const actions = React.useMemo(() => getActions({ setRowAction, access }), [setRowAction, access]);
  const columns = React.useMemo(() => getColumns({ actions }), [actions]);
  
  const [filterFields, setFilterFields] = React.useState<DataTableFilterField<DownloadControl>[]>(getFilterFields());

  const { table } = useDataTable({
    data: data?.data || [],
    columns,
    pageCount: Math.ceil((data?.total || 0) / (data?.pageSize || 10)),
    filterFields,
    initialState: {
      pagination: {
        pageIndex: 0,
        pageSize: 30
      },
      sorting: [{ id: 'createdTime', desc: true }],
      columnPinning: { right: ["actions"] },
    },
    getRowId: (originalRow) => originalRow.id,
    clearOnDefault: false,
    shallow: true
  });

  React.useEffect(() => {
    setFilterFields(getFilterFields());
  }, [data]);

  const handleFilterChange = (pageQuery: PageQuery<Specification<DownloadControl>>) => {
    onQuery(pageQuery);
  };

  return (
    <>
      <DataTable table={table} className="h-fit">
        <DataTableToolbar 
          type={'sql'}
          onFilterChange={handleFilterChange} 
          table={table} 
          filterFields={filterFields} 
        />
      </DataTable>
    </>
  );
}
