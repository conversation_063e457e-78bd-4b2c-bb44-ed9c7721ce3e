import React, {useEffect, useState} from 'react';
import {DataTable} from "@/components/data-table/data-table";
import {DataTableToolbar} from "@/components/data-table/data-table-toolbar";
import {useLocalDataTable} from "@/components/data-table/hooks/use-local-data-table";
import {DataTableFilterField} from "@/components/data-table/types";
import {ColumnDef} from "@tanstack/react-table";
import {Button} from "@/components/ui/button";
import {DataTableColumnHeader} from "@/components/data-table/data-table-column-header";
import {useRequest} from 'umi';
import {Plus, Trash2} from "lucide-react";
import {Page} from "@/types";
import {fetchGroups, Group} from "@/service/group";

interface GroupSelectorData {
    selectedIds: string[];
    groups: Group[];
}

interface GroupSelectorProps {
    onChange?: (data: GroupSelectorData) => void;
    initialSelectedGroups?: Group[];
}

export const GroupSelector = ({onChange, initialSelectedGroups = []}: GroupSelectorProps) => {
    const [page, setPage] = useState(0);
    const [perPage, setPerPage] = useState(20);
    const [groups, setGroups] = useState<Page<Group>>({
        page: 0,
        pageSize: 20,
        total: 0,
        data: [],
    });
    const [selectedGroups, setSelectedGroups] = useState<Group[]>(initialSelectedGroups);

    const filterFields: DataTableFilterField<Group>[] = [
        {
            id: 'name',
            label: '用户组名称',
        },
        {
            id: 'code',
            label: '用户组编码',
        }
    ];

    const columns: ColumnDef<Group>[] = [
        {
            accessorKey: "name",
            header: ({column}) => (
                <DataTableColumnHeader column={column} title="用户组名称"/>
            ),
        },
        {
            accessorKey: "code",
            header: ({column}) => (
                <DataTableColumnHeader column={column} title="用户组编码"/>
            ),
        },
        {
            id: "actions",
            cell: ({row}) => {
                const group = row.original;
                const isSelected = selectedGroups.some(g => g.id === group.id);

                return (
                    <div className="flex items-center gap-2">
                        <Button
                            variant="ghost"
                            className="h-8 w-8 p-0"
                            onClick={() => handleAddGroup(group)}
                            disabled={isSelected}
                        >
                            <Plus className="h-4 w-4"/>
                        </Button>
                    </div>
                );
            },
        },
    ];

    const selectedColumns: ColumnDef<Group>[] = [
        {
            accessorKey: "name",
            header: ({column}) => (
                <DataTableColumnHeader column={column} title="用户组名称"/>
            ),
        },
        {
            accessorKey: "code",
            header: ({column}) => (
                <DataTableColumnHeader column={column} title="用户组编码"/>
            ),
        },
        {
            id: "actions",
            cell: ({row}) => {
                const group = row.original;

                return (
                    <div className="flex items-center gap-2">
                        <Button
                            variant="ghost"
                            className="h-8 w-8 p-0"
                            onClick={() => handleRemoveGroup(group.id)}
                        >
                            <Trash2 className="h-4 w-4"/>
                        </Button>
                    </div>
                );
            },
        },
    ];

    const {table: allGroupsTable} = useLocalDataTable({
            pageCount: groups.total / perPage,
            data: groups.data,
            columns,
            filterFields,
            initialState: {
                pagination: {
                    pageIndex: page,
                    pageSize: perPage,
                }
            },
            getRowId: (originalRow: Group) => originalRow.code,
            onPaginationChange: (updaterOrValue) => {
                const newState = typeof updaterOrValue === 'function'
                    ? updaterOrValue({pageIndex: page, pageSize: perPage})
                    : updaterOrValue;
                setPage(newState.pageIndex);
                setPerPage(newState.pageSize);
            }
        }
    );

    const {table: selectedTable} = useLocalDataTable({
        pageCount: -1,
        data: selectedGroups,
        columns: selectedColumns,
    });

    // 获取所有用户组列表
    useRequest(
        () => {
            const code = allGroupsTable.getColumn("code")?.getFilterValue() as string;
            const name = allGroupsTable.getColumn("name")?.getFilterValue() as string;
            return fetchGroups({
                page,
                pageSize: perPage,
                specification: {
                    code,
                    name
                }
            })
        },
        {
            refreshDeps: [page, perPage, allGroupsTable.getState().columnFilters],
            debounceInterval: 500,
            onSuccess: (res) => {
                setGroups(res);
            }
        }
    );

    // 初始化已选择的用户组
    useEffect(() => {
        if (initialSelectedGroups && initialSelectedGroups.length > 0) {
            setSelectedGroups(initialSelectedGroups);
        }
    }, [initialSelectedGroups]);

    const handleAddGroup = (group: Group) => {
        setSelectedGroups(prev => {
            if (prev.some(g => g.id === group.id)) {
                return prev;
            }
            return [...prev, group];
        });
    };

    const handleRemoveGroup = (groupId: string) => {
        setSelectedGroups(prev => prev.filter(g => g.id !== groupId));
    };

    // 当选择的用户组列表变化时，通知父组件
    useEffect(() => {
        if (onChange) {
            onChange({
                selectedIds: selectedGroups.map(group => group.id),
                groups: selectedGroups
            });
        }
    }, [selectedGroups, onChange]);

    return (
        <div className="flex gap-4 h-full">
            <div className="flex-1">
                <h3 className="mb-4 text-lg font-medium">待选用户组</h3>
                <DataTable offsetBottom={65} table={allGroupsTable}>
                    <DataTableToolbar table={allGroupsTable} filterFields={filterFields}/>
                </DataTable>
            </div>
            <div className="flex-1">
                <h3 className="mb-4 text-lg font-medium">已选用户组 ({selectedGroups.length})</h3>
                <DataTable offsetBottom={65} table={selectedTable} showPagination={false}>
                    <DataTableToolbar table={selectedTable}/>
                </DataTable>
            </div>
        </div>
    );
};
