import {ColumnDef} from "@tanstack/react-table";
import {DownloadControl} from "@/service/cases/download";
import {Badge} from "@/components/ui/badge";
import {format} from "date-fns";

interface GetColumnsProps {
  actions?: ColumnDef<DownloadControl>;
}

export function getColumns({ actions }: GetColumnsProps): ColumnDef<DownloadControl>[] {
  const columns: ColumnDef<DownloadControl>[] = [
    {
      accessorKey: "tag",
      header: "标签",
      cell: ({ row }) => {
        return (
          <Badge variant="outline" className="px-3 py-1 font-normal">
            {row.original.tag}
          </Badge>
        );
      },
    },
    {
      accessorKey: "groups",
      header: "用户组",
      cell: ({ row }) => {
        return (
            <div className="flex flex-wrap gap-1">
              {row.original.groups.map((group, index) => (
                  <Badge key={index} variant="secondary" className="px-3 py-1 font-normal">
                    {group.name}
                  </Badge>
              ))}
            </div>
        );
      },
    },
    {
      accessorKey: "limits",
      header: "下载数量限制",
      cell: ({ row }) => {
        return <span>{row.original.limits}</span>;
      },
    },

    {
      accessorKey: "createdTime",
      header: "创建时间",
      cell: ({ row }) => {
        return (
          <span>
            {row.original.createdTime
              ? format(new Date(row.original.createdTime), "yyyy-MM-dd HH:mm:ss")
              : "-"}
          </span>
        );
      },
    },
    {
      accessorKey: "modifiedTime",
      header: "更新时间",
      cell: ({ row }) => {
        return (
          <span>
            {row.original.modifiedTime
              ? format(new Date(row.original.modifiedTime), "yyyy-MM-dd HH:mm:ss")
              : "-"}
          </span>
        );
      },
    },
  ];

  if (actions) {
    columns.push(actions);
  }

  return columns;
}
