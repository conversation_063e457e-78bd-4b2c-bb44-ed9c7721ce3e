import {DataTableFilterField} from "@/components/data-table/types";
import {DownloadControl} from "@/service/cases/download";

export function getFilterFields(): DataTableFilterField<DownloadControl>[] {
    return [
        {
            id: "tag",
            label: "标签名称",
            placeholder: '输入标签搜索'
        },
        {
            id: "groupName",
            label: "用户组",
            virtual: true,
            placeholder: '输入用户组名称搜索'
        }
    ];
}
