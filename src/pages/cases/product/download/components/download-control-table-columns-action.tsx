import {Access, AccessInstance} from "@@/exports";
import {DownloadControl} from "@/service/cases/download";
import {ColumnDef} from "@tanstack/react-table";
import {Button} from "@/components/ui/button";
import {Pencil, Trash} from "lucide-react";
import React from "react";
import type {DataTableRowAction} from "@/components/data-table/types";

export interface DownloadControlTableColumnProps {
    setRowAction: React.Dispatch<React.SetStateAction<DataTableRowAction<DownloadControl> | null>>;
    access: AccessInstance
}

export function getActions({setRowAction, access}: DownloadControlTableColumnProps): ColumnDef<DownloadControl> | undefined {
    return {
        id: "actions",
        header:'操作',
        cell: ({row}) => {
            return (
                <div className="flex items-center gap-2">
                    <Button
                        variant="ghost"
                        size="icon"
                        className="h-8 w-8"
                        onClick={() =>
                            setRowAction({
                                type: "update",
                                row: row,
                            })
                        }
                    >
                        <Pencil className="h-4 w-4"/>
                        <span className="sr-only">编辑</span>
                    </Button>
                    <Button
                        variant="ghost"
                        size="icon"
                        className="h-8 w-8"
                        onClick={() =>
                            setRowAction({
                                type: "delete",
                                row: row,
                            })
                        }
                    >
                        <Trash className="h-4 w-4"/>
                        <span className="sr-only">删除</span>
                    </Button>
                </div>
            );
        },
    }
}
