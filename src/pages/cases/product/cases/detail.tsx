import React from 'react';
import {useParams, useRequest} from "umi";
import {fetchCaseDetail, ProductCase} from "@/service/cases/product";
import FilePreview from '@/components/file-preview';
import {Badge} from '@/components/ui/badge';
import {Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger} from "@/components/ui/tabs";
import {Button} from "@/components/ui/button";
import {Download} from "lucide-react";
import {cn} from "@/lib/utils";

export default function CaseDetailPage() {
    const {id} = useParams<{ id: string }>();
    const [caseDetail, setCaseDetail] = React.useState<ProductCase>();

    // 加载案例详情
    useRequest(() => {
        if (id) {
            return fetchCaseDetail(id);
        }
        return Promise.reject('案例ID不存在');
    }, {
        onSuccess: (res) => {
            setCaseDetail(res);
        },
        ready: !!id
    });

    if (!caseDetail) {
        return <div className="p-4">加载中...</div>;
    }

    return (
        <div className="h-full flex flex-col">
            {/* 标题和基本信息 */}
            <div className="flex-none py-4 px-4 space-y-2 bg-background/95 sticky top-0 z-10 shadow-sm">
                <h1 className="text-2xl font-bold text-center">{caseDetail.title}</h1>

                <div className="flex items-center justify-center gap-4 flex-wrap">
                    {/* 标签 */}
                    <div className="flex flex-wrap gap-1">
                        {caseDetail.tags.map(tag => (
                            <Badge key={tag}>
                                {tag}
                            </Badge>
                        ))}
                    </div>
                </div>
            </div>

            {/* 文件预览 - 使用Tabs展示多个附件 */}
            <div className="flex-1 overflow-hidden">
                <Tabs defaultValue={caseDetail.attachments[0]?.file.id} className="h-full">
                    <div className="relative">
                        <div
                            className="overflow-x-auto pb-1 scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-transparent">
                            <TabsList className="px-4 w-max min-w-full">
                                {caseDetail.attachments.map((attachment) => (
                                    <TabsTrigger
                                        key={attachment.file.id}
                                        value={attachment.file.id}
                                        className="flex items-center gap-2 pr-2"
                                    >
                                        <span className="truncate max-w-[150px]">{attachment.file.fileName}</span>
                                        <Button
                                            size="icon"
                                            variant="ghost"
                                            className="h-6 w-6 rounded-full hover:bg-gray-200 ml-1"
                                            onClick={(e) => {
                                                e.stopPropagation(); // 阻止触发标签切换
                                                // 使用file.url作为下载链接
                                                if (attachment.id) {
                                                    window.open(`/api/product/cases/download?attachmentId=${attachment.id}`, '_blank');
                                                }
                                            }}
                                            title="下载文件"
                                        >
                                            <Download className="h-3.5 w-3.5"/>
                                        </Button>
                                    </TabsTrigger>
                                ))}
                            </TabsList>
                        </div>
                    </div>
                    {caseDetail.attachments.map((attachment) => (
                        <TabsContent
                            key={attachment.file.id}
                            value={attachment.file.id}
                            className="h-[calc(100%-48px)]"
                        >
                            <FilePreview
                                url={attachment.file.metadata?.previewUrl || ""}
                                height="100%"
                                className="w-full h-full"
                            />
                        </TabsContent>
                    ))}
                </Tabs>
            </div>
        </div>
    );
} 