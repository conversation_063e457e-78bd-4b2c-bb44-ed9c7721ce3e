import React from 'react';
import {AuthDialog} from '@/components/auth/auth-dialog';
import {ProductCase} from '@/service/cases/product';

interface CasesAuthSheetProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  productCase?: ProductCase;
  onSuccess?: () => void;
}

export function CasesAuthSheet({
  open,
  onOpenChange,
  productCase,
  onSuccess
}: CasesAuthSheetProps) {
  const handleSuccess = () => {
    onSuccess?.();
  };

  return (
    <AuthDialog
      open={open}
      onOpenChange={onOpenChange}
      resourceId={productCase?.id || ''}
      resourceType="CASE"
      onSuccess={handleSuccess}
    />
  );
} 