import {DataTableFilterField} from "@/components/data-table/types";
import {ProductCase} from "@/service/cases/product";
import {Aggs} from "@/types";

export function getFilterFields(aggs: Aggs<ProductCase>): DataTableFilterField<ProductCase>[] {
    return [
        {
            id: "title,content",
            label: "搜索",
            placeholder: "搜索标题或内容",
            virtual: true,
            highlight:true,
        },
        {
            id:'path',
            label:'',
            placeholder:'',
            virtual: true,
            hidden:true,
            options:[]
        },
    ]
} 