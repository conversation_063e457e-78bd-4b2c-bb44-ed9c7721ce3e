import React, {useEffect, useState} from 'react';
import {Button} from "@/components/ui/button";
import {ChevronDown, ChevronRight, Search} from "lucide-react";
import {Input} from "@/components/ui/input";
import {ProductClassify} from "@/service/cases/product";
import { ScrollArea } from "@/components/ui/scroll-area";

interface ClassifyTreeProps {
  data: ProductClassify[];
  onClassifySelect: (classify:ProductClassify | undefined ) => void;
  selectedClassifyId: string;
}

export function ClassifyTree({ data, onClassifySelect, selectedClassifyId }: ClassifyTreeProps) {
  const [expandedRows, setExpandedRows] = useState<Set<string>>(new Set());
  const [searchKeyword, setSearchKeyword] = useState('');

  // 高亮显示匹配的文本
  const highlightText = (text: string) => {
    if (!searchKeyword) return text;
    
    const parts = text.split(new RegExp(`(${searchKeyword})`, 'gi'));
    return (
      <>
        {parts.map((part, i) => 
          part.toLowerCase() === searchKeyword.toLowerCase() ? (
            <span key={i} className="bg-yellow-200 text-black rounded px-0.5">{part}</span>
          ) : (
            part
          )
        )}
      </>
    );
  };

  // 查找分类的所有父级ID
  const findParentIds = (
    classifies: ProductClassify[],
    targetId: string,
    parentIds: Set<string> = new Set()
  ): Set<string> => {
    for (const classify of classifies) {
      if (classify.id === targetId) {
        return parentIds;
      }
      if (classify.children?.length) {
        parentIds.add(classify.id);
        const found = findParentIds(classify.children, targetId, parentIds);
        if (found.size > 0) {
          return found;
        }
        parentIds.delete(classify.id);
      }
    }
    return new Set();
  };

  // 检查分类及其子分类是否匹配搜索关键词，并返回匹配结果和需要展开的父级ID
  const isClassifyMatch = (
    classify: ProductClassify,
    parentIds: Set<string> = new Set()
  ): { isMatch: boolean; matchedParentIds: Set<string> } => {
    const isCurrentMatch = classify.name.toLowerCase().includes(searchKeyword.toLowerCase()) ||
                          classify.code.toLowerCase().includes(searchKeyword.toLowerCase());
    
    if (classify.children) {
      let hasMatchingChild = false;
      const childParentIds = new Set(parentIds);
      childParentIds.add(classify.id);
      
      for (const child of classify.children) {
        const { isMatch, matchedParentIds } = isClassifyMatch(child, childParentIds);
        if (isMatch) {
          hasMatchingChild = true;
          matchedParentIds.forEach(id => parentIds.add(id));
        }
      }
      
      if (isCurrentMatch || hasMatchingChild) {
        return { isMatch: true, matchedParentIds: parentIds };
      }
    }
    
    return { 
      isMatch: isCurrentMatch, 
      matchedParentIds: isCurrentMatch ? parentIds : new Set() 
    };
  };

  // 当选中的分类ID变化时，自动展开父级分类
  useEffect(() => {
    if (selectedClassifyId) {
      const parentIds = findParentIds(data, selectedClassifyId);
      setExpandedRows(prev => new Set([...prev, ...parentIds]));
    }
  }, [selectedClassifyId, data]);

  // 当搜索关键词变化时，更新展开的行
  useEffect(() => {
    if (searchKeyword) {
      const newExpandedRows = new Set<string>();
      data.forEach(classify => {
        const { matchedParentIds } = isClassifyMatch(classify);
        matchedParentIds.forEach(id => newExpandedRows.add(id));
      });
      setExpandedRows(newExpandedRows);
    }
  }, [searchKeyword, data]);

  const toggleRow = (id: string) => {
    const newExpandedRows = new Set(expandedRows);
    if (newExpandedRows.has(id)) {
      newExpandedRows.delete(id);
    } else {
      newExpandedRows.add(id);
    }
    setExpandedRows(newExpandedRows);
  };

  const renderClassify = (classify: ProductClassify, level: number = 0) => {
    const hasChildren = classify.children && classify.children.length > 0;
    const isExpanded = expandedRows.has(classify.id);
    const isSelected = selectedClassifyId === classify.id;

    // 如果有搜索关键词，检查当前分类及其子分类是否匹配
    if (searchKeyword) {
      const { isMatch } = isClassifyMatch(classify);
      if (!isMatch) {
        return null;
      }
    }

    return (
      <div key={classify.id}>
        <div 
          className={`flex items-center py-2 px-4 hover:bg-accent cursor-pointer ${isSelected ? 'bg-accent' : ''}`}
          style={{ paddingLeft: `${level * 20 + 16}px` }}
          onClick={() => onClassifySelect(classify.id === selectedClassifyId ? undefined : classify)}
        >
          {hasChildren && (
            <Button
              variant="ghost"
              size="icon"
              className="h-6 w-6 mr-2"
              onClick={(e) => {
                e.stopPropagation();
                toggleRow(classify.id);
              }}
            >
              {isExpanded ? (
                <ChevronDown className="h-4 w-4" />
              ) : (
                <ChevronRight className="h-4 w-4" />
              )}
            </Button>
          )}
          {!hasChildren && <div className="w-6 mr-2" />}
          <div className="flex-1">
            {highlightText(classify.name)}
          </div>
        </div>
        {hasChildren && isExpanded && (
          <div>
            {classify.children?.map(child => renderClassify(child, level + 1))}
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="w-[300px] border-r flex flex-col h-full">
      <div className="p-4">
        <div className="relative mb-4">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="搜索分类..."
            value={searchKeyword}
            onChange={(e) => setSearchKeyword(e.target.value)}
            className="pl-8"
          />
        </div>
        <ScrollArea className="h-[calc(100vh-180px)]">
          <div className="border rounded-lg">
            {data.map(classify => renderClassify(classify))}
          </div>
        </ScrollArea>
      </div>
    </div>
  );
} 