import React from "react";
import type {DataTableRowAction} from "@/components/data-table/types";
import {ColumnDef} from "@tanstack/react-table";
import {DataTableColumnHeader} from "@/components/data-table/data-table-column-header";
import {ProductCase} from "@/service/cases/product";
import {Badge} from "@/components/ui/badge";
import {Link, useAccess, Access} from "umi";

export interface CasesTableColumnProps {
   actions: ColumnDef<ProductCase> | undefined;
}

export function getColumns({actions}: CasesTableColumnProps): ColumnDef<ProductCase>[] {
    const  columns: ColumnDef<ProductCase>[] = [
        {
            accessorKey: "title",
            header: ({column}) => <DataTableColumnHeader column={column}/>,
            cell: ({row}) => (
                <Link to={`/product/cases/${row.original.id}`}>
                    <div className={'pl-1'} dangerouslySetInnerHTML={{__html: row.original.title}}/>
                </Link>
            ),
            meta: {
                title: "案例标题",
            },
            enableSorting: true,
            enableHiding: true,
        },
        {
            accessorKey: "tags",
            header: ({column}) => <DataTableColumnHeader column={column}/>,
            cell: ({row}) => {
                const tags = row.getValue("tags") as string[];
                return (
                    <div className="flex gap-1 flex-wrap">
                        {tags.map((tag) => (
                            <Badge key={tag}>
                                {tag}
                            </Badge>
                        ))}
                    </div>
                );
            },
            meta: {
                title: "标签",
            },
            enableSorting: false,
            enableHiding: true,
        },
    ];
    if (actions){
        columns.push(actions);
    }
    return columns;
} 