import React from 'react';
import {DataTableFilterField, DataTableRowAction} from "@/components/data-table/types";
import {useDataTable} from "@/components/data-table/hooks/use-data-table";
import {DataTable} from "@/components/data-table/data-table";
import {DataTableToolbar} from "@/components/data-table/data-table-toolbar";
import {getColumns} from '@/pages/cases/product/cases/components/cases-table-columns';
import {ProductCase} from '@/service/cases/product';
import {Page, PageQuery, Specification} from '@/types';
import {getFilterFields} from './cases-table-filters';
import {CasesAuthSheet} from './cases-auth-sheet';
import {getActions} from "@/pages/cases/product/cases/components/cases-table-columns-action";
import {useAccess} from "umi";

interface CasesTableProps {
  path: string;
  onQuery: (query: PageQuery<Specification<ProductCase>>) => Promise<Page<ProductCase>>;
  setRowAction: React.Dispatch<React.SetStateAction<DataTableRowAction<ProductCase> | null>>;
  data?: Page<ProductCase>;
}

export function CasesTable({ path, onQuery, setRowAction, data }: CasesTableProps) {
  const access = useAccess();
  
  const actions = React.useMemo(() => getActions({ setRowAction, access }), [setRowAction, access]);
  const columns = React.useMemo(() => getColumns({ actions }), [actions]);
  
  const [filterFields, setFilterFields] = React.useState<DataTableFilterField<ProductCase>[]>(getFilterFields(data?.aggs || {}));



  const { table } = useDataTable({
    data: data?.data || [],
    columns,
    pageCount: Math.ceil((data?.total || 0) / (data?.pageSize || 10)),
    filterFields,
    initialState: {
      pagination: {
        pageIndex: 0,
        pageSize: 30
      },
      columnFilters:[{
        id:'path',
        value:path
      }],
      sorting: [{ id: 'status', desc: true }],
      columnPinning: { right: ["actions"] },
    },
    getRowId: (originalRow) => originalRow.id,
    clearOnDefault: false,
    shallow:true
  });

  React.useEffect(() => {
    setFilterFields(getFilterFields(data?.aggs || {}));
  }, [data]);

  const handleFilterChange = (pageQuery: PageQuery<Specification<ProductCase>>) => {
    onQuery(pageQuery);
  };

  return (
    <>
      <DataTable table={table} className="h-fit">
        <DataTableToolbar 
          type={'es'} 
          onFilterChange={handleFilterChange} 
          table={table} 
          filterFields={filterFields} 
        />
      </DataTable>
    </>
  );
} 