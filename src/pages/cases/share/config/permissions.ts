import {AccessInstance} from 'umi';

/**
 * 共享库权限配置接口
 * 定义了共享库中各种操作的权限控制
 */
export interface ShareLibraryPermissions {
    // 文件操作权限
    file: {
        rename: boolean;     // 重命名文件
        preview: boolean;    // 预览文件
        download: boolean;   // 下载文件
        delete: boolean;     // 删除文件
    };

    // 文件夹操作权限
    folder: {
        create: boolean;     // 创建文件夹
        rename: boolean;     // 重命名文件夹
        delete: boolean;     // 删除文件夹
        navigate: boolean;   // 导航进入文件夹
    };

    // 上传操作权限
    upload: {
        files: boolean;      // 上传文件
        folder: boolean;     // 文件夹拖放上传
    };
}

/**
 * 默认权限配置
 * 所有操作都允许
 */
export const defaultPermissions: ShareLibraryPermissions = {
    file: {
        rename: true,
        preview: true,
        download: true,
        delete: true,
    },
    folder: {
        create: true,
        rename: true,
        delete: true,
        navigate: true,
    },
    upload: {
        files: true,
        folder: true,
    }
};

/**
 * 只读权限配置
 * 只允许查看和导航，不允许修改操作
 */
export const readOnlyPermissions: ShareLibraryPermissions = {
    file: {
        rename: false,
        preview: true,
        download: true,
        delete: false,
    },
    folder: {
        create: false,
        rename: false,
        delete: false,
        navigate: true,
    },
    upload: {
        files: false,
        folder: false,
    }
};

/**
 * 根据访问控制实例生成权限配置
 * @param access 访问控制实例
 * @returns 权限配置对象
 */
export function getPermissionsByAccess(access: AccessInstance): ShareLibraryPermissions {
    return {
        file: {
            rename: access.hasAction("SHARE:FILE:RENAME"),
            preview: access.hasAction("SHARE:FILE:PREVIEW") || true, // 默认允许预览
            download: access.hasAction("SHARE:FILE:DOWNLOAD"),
            delete: access.hasAction("SHARE:FILE:DELETE"),
        },
        folder: {
            create: access.hasAction("SHARE:FOLDER:CREATE"),
            rename: access.hasAction("SHARE:FOLDER:RENAME"),
            delete: access.hasAction("SHARE:FOLDER:DELETE"),
            navigate: access.hasAction("SHARE:FOLDER:NAVIGATE") || true, // 默认允许导航
        },
        upload: {
            files: access.hasAction("SHARE:UPLOAD:FILES"),
            folder: access.hasAction("SHARE:UPLOAD:FOLDER"),
        }
    };
}
