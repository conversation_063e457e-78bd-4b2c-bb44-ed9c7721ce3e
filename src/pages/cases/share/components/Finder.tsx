import React, {useEffect, useState} from 'react';
import {Resource} from './Resource';
import {Toolbar} from './Toolbar';
import {FinderPagination} from './FinderPagination';
import {FinderBreadcrumb} from '@/pages/cases/share/components/FinderBreadcrumb';
import {Dialog, DialogContent, DialogHeader, DialogTitle,} from "@/components/ui/dialog";
import {Input} from "@/components/ui/input";
import {FolderPlus, Upload, UploadCloud} from 'lucide-react';
import {useDropzone} from 'react-dropzone';
import {ContextMenu, ContextMenuContent, ContextMenuItem, ContextMenuTrigger,} from '@/components/ui/context-menu';
import {ShareLibraryPermissions} from '../config/permissions';
import {ShareResource} from "@/service/cases/share";

interface PathItem {
    id: string;
    name: string;
}

interface FinderProps {
    resources: any[];
    loading: boolean;
    onPathChange: (newPath: string) => void;
    currentPath: string;
    onOpenResource: (resource: any) => void;
    onRenameResource: (resource: any, newName: string) => void;
    onDeleteResource: (resource: ShareResource) => void;
    onUploadFiles: (files: File[]) => void;
    onFolderDrop?: (files: File[], resourceId: string) => void;
    onCreateFolder: (name: string) => void;
    pathHistory: PathItem[];
    onNavigate: (index: number) => void;
    // 分页相关属性
    currentPage: number;
    totalPages: number;
    onPageChange: (page: number) => void;
    // 每页数量相关属性
    pageSize: number;
    onPageSizeChange: (pageSize: number) => void;
    // 搜索相关属性
    onSearch?: (query: string) => void;
    searchQuery?: string;
    // 权限配置
    permissions?: ShareLibraryPermissions;
}

export const Finder: React.FC<FinderProps> = ({
                                                  resources,
                                                  loading,
                                                  onOpenResource,
                                                  onRenameResource,
                                                  onDeleteResource,
                                                  onUploadFiles,
                                                  onFolderDrop,
                                                  onCreateFolder,
                                                  pathHistory,
                                                  onNavigate,
                                                  currentPage,
                                                  totalPages,
                                                  onPageChange,
                                                  pageSize,
                                                  onPageSizeChange,
                                                  onSearch,
                                                  searchQuery,
                                                  permissions
                                              }) => {
    const [isDragging, setIsDragging] = useState(false);
    const [isCreateFolderOpen, setIsCreateFolderOpen] = useState(false);
    const [newFolderName, setNewFolderName] = useState('');


    const handleDrop = (acceptedFiles: File[]) => {
        setIsDragging(false);
        if (acceptedFiles.length > 0) {
            onUploadFiles(acceptedFiles);
        }
    };

    const {getRootProps, getInputProps, isDragActive, open} = useDropzone({
        onDrop: handleDrop,
        onDropRejected: () => setIsDragging(false),
        onDragEnter: () => setIsDragging(true),
        onDragLeave: () => setIsDragging(false),
        noClick: true,
        // 根据权限禁用拖放
        disabled: permissions && !permissions.upload.files,
    });

    useEffect(() => {
        const handleKeyDown = (e: KeyboardEvent) => {
            if (e.key === 'Escape' && isDragging) {
                setIsDragging(false);
            }
        };

        window.addEventListener('keydown', handleKeyDown);
        return () => {
            window.removeEventListener('keydown', handleKeyDown);
        };
    }, [isDragging]);

    useEffect(() => {
        if (!isDragActive && isDragging) {
            setIsDragging(false);
        }
    }, [isDragActive, isDragging]);

    const handleCreateFolderSubmit = () => {
        if (newFolderName) {
            onCreateFolder(newFolderName);
            setIsCreateFolderOpen(false);
            setNewFolderName('');
        }
    };

    // 如果资源列表为空，自动加载第一页 - 现在由父组件控制
    useEffect(() => {
        if (resources.length === 0 && !loading) {
            console.log('初始化加载第一页');
            // 不再需要手动触发加载，由父组件控制
        }
    }, [resources.length, loading]);

    return (
        <div className="flex flex-col h-full">
            <FinderBreadcrumb paths={pathHistory} onNavigate={onNavigate}/>
            <Toolbar
                onCreateFolder={() => setIsCreateFolderOpen(true)}
                onUploadFiles={onUploadFiles}
                onSearch={onSearch}
                searchQuery={searchQuery}
                permissions={permissions}
            />
            <div className="flex-1 w-full">
                <div className='h-[calc(100%-40px)]'>
                    {loading && (
                        <div className="absolute inset-0 bg-background/50 flex items-center justify-center z-50">
                            <div className="text-center py-4 text-muted-foreground font-medium">
                                <div className="animate-pulse">加载中...</div>
                            </div>
                        </div>
                    )}
                    <ContextMenu>
                        <ContextMenuTrigger>
                            <div
                                {...getRootProps()}
                                className={`flex-1 h-full rounded-lg border-2 border-dashed p-6 relative transition-colors ${isDragging ? 'border-primary bg-primary/10' : 'border-gray-200'}`}
                            >
                                <input {...getInputProps()} />

                                {isDragging && (
                                    <div
                                        className="absolute inset-0 flex flex-col items-center justify-center bg-primary/5 backdrop-blur-sm z-10 pointer-events-none rounded-lg">
                                        <UploadCloud size={100} className="text-primary mb-6"/>
                                        <p className="text-2xl font-medium text-primary">释放文件以上传</p>
                                        <p className="text-lg text-muted-foreground mt-3">文件将被上传到当前文件夹</p>
                                    </div>
                                )}


                                <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 xl:grid-cols-8 gap-6">
                                    {resources.map(resource => (
                                        <Resource
                                            key={resource.id}
                                            resource={resource}
                                            onOpen={onOpenResource}
                                            onRename={onRenameResource}
                                            onDrop={onFolderDrop}
                                            className="z-20"
                                            permissions={permissions}
                                            onDelete={onDeleteResource}
                                        />
                                    ))}
                                </div>

                                {resources.length === 0 && !isDragging && (
                                    <div
                                        className="absolute inset-0 flex flex-col items-center justify-center text-muted-foreground">
                                        <UploadCloud size={80} className="mb-6 text-muted-foreground/70"/>
                                        <p className="text-xl font-medium">当前文件夹为空</p>
                                        <p className="text-base mt-2">拖拽文件或点击"上传文件"按钮添加文件</p>
                                    </div>
                                )}
                            </div>
                        </ContextMenuTrigger>
                        <ContextMenuContent className="w-64">
                            {/* 根据权限显示新建文件夹选项 */}
                            {(!permissions || permissions.folder.create) && (
                                <ContextMenuItem onClick={() => setIsCreateFolderOpen(true)}>
                                    <FolderPlus className="mr-2 h-4 w-4"/>
                                    新建文件夹
                                </ContextMenuItem>
                            )}
                            {/* 根据权限显示上传文件选项 */}
                            {(!permissions || permissions.upload.files) && (
                                <ContextMenuItem onClick={open}>
                                    <Upload className="mr-2 h-4 w-4"/>
                                    上传文件
                                </ContextMenuItem>
                            )}
                        </ContextMenuContent>
                    </ContextMenu>
                </div>

                {/* 分页控制区 */}
                <div className="flex flex-col items-center justify-center mt-1">
                    {/* 加载状态提示 */}
                    {loading && (
                        <div className="text-muted-foreground mb-4 animate-pulse">加载中...</div>
                    )}

                    {/* 分页组件 */}
                    {resources.length > 0 && !loading && (
                        <FinderPagination
                            currentPage={currentPage}
                            totalPages={totalPages}
                            onPageChange={onPageChange}
                            pageSize={pageSize}
                            onPageSizeChange={onPageSizeChange}
                        />
                    )}
                </div>
            </div>

            {/* 创建文件夹对话框 */}
            <Dialog open={isCreateFolderOpen} onOpenChange={(open) => {
                if (!open) setIsCreateFolderOpen(false);
            }}>
                <DialogContent>
                    <DialogHeader>
                        <DialogTitle>新建文件夹</DialogTitle>
                    </DialogHeader>
                    <div className="py-4">
                        <Input
                            value={newFolderName}
                            onChange={(e) => setNewFolderName(e.target.value)}
                            placeholder="请输入文件夹名称"
                            onKeyDown={(e) => {
                                if (e.key === 'Enter' && newFolderName) {
                                    handleCreateFolderSubmit();
                                }
                            }}
                            autoFocus
                        />
                    </div>
                </DialogContent>
            </Dialog>
        </div>
    );
}; 