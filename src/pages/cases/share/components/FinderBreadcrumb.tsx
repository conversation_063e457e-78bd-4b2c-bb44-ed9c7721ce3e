import {Button} from '@/components/ui/button';
import {ChevronRight, Home} from 'lucide-react';

interface BreadcrumbProps {
  paths: Array<{ id: string; name: string }>;
  onNavigate: (index: number) => void;
}

export function FinderBreadcrumb({ paths, onNavigate }: BreadcrumbProps) {
  return (
    <div className="mb-4 flex items-center text-sm">
      <Button 
        variant="ghost" 
        size="sm" 
        className="h-8 px-2 hover:bg-primary/10"
        onClick={() => onNavigate(-1)}
      >
        <Home className="h-4 w-4 mr-1" />
        根目录
      </Button>
      {paths.map((path, index) => (
        <div key={path.id} className="flex items-center">
          <ChevronRight className="h-4 w-4 mx-1 text-muted-foreground" />
          <Button
            variant="ghost"
            size="sm"
            className="h-8 px-2 hover:bg-primary/10"
            onClick={() => onNavigate(index)}
          >
            {path.name}
          </Button>
        </div>
      ))}
    </div>
  );
} 