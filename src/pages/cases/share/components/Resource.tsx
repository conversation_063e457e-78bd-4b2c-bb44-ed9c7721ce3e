import React from 'react';
import {ShareResource} from '@/service/cases/share';
import Folder from './Folder';
import File from './File';
import {ShareLibraryPermissions} from '../config/permissions';

interface ShareResourceItemProps {
  resource: ShareResource;
  onOpen: (resource: ShareResource) => void;
  onRename: (resource: ShareResource, newName: string) => void;
  onDrop?: (files: File[], resourceId: string) => void;
  onDelete:(resource:ShareResource) =>void;
  className?: string;
  permissions?: ShareLibraryPermissions; // 添加权限配置项
}

export const Resource: React.FC<ShareResourceItemProps> = ({
  resource,
  onOpen,
  onRename,
  onDrop,
  onDelete,
  className,
  permissions,

}) => {
  return resource.type === 'DIRECTORY' ? (
    <Folder
      resource={resource}
      onOpen={onOpen}
      onRename={onRename}
      onDelete={onDelete}
      onDrop={onDrop}
      className={className}
      permissions={permissions}
    />
  ) : (
    <File
      resource={resource}
      onOpen={onOpen}
      onRename={onRename}
      onDelete={onDelete}
      className={className}
      permissions={permissions}
    />
  );
};