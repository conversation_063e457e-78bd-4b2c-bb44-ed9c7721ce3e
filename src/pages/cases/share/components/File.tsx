import React, {useState} from 'react';
import {ShareResource} from '@/service/cases/share';
import {cn} from '@/lib/utils';
import FileIcon from '@/pages/cases/share/components/FileIcon';
import {ContextMenu, ContextMenuContent, ContextMenuItem, ContextMenuTrigger,} from '@/components/ui/context-menu';
import {Dialog, DialogContent, DialogHeader, DialogTitle,} from '@/components/ui/dialog';
import {Input} from '@/components/ui/input';
import FilePreview from '@/components/file-preview';
import {ShareLibraryPermissions} from '../config/permissions';
import {Sheet, SheetContent, SheetHeader, SheetTitle} from "@/components/ui/sheet";
import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,

} from "@/components/ui/alert-dialog"

interface FileProps {
    resource: ShareResource;
    onOpen: (resource: ShareResource) => void;
    onRename: (resource: ShareResource, newName: string) => void;
    className?: string;
    onDelete: (resource: ShareResource) => void;
    permissions?: ShareLibraryPermissions; // 添加权限配置项
}

const File: React.FC<FileProps> = ({resource, onOpen, onRename, onDelete, className, permissions}) => {
    const [renameOpen, setRenameOpen] = useState(false);
    const [deleteOpen, setDeleteOpen] = useState(false);
    const [previewOpen, setPreviewOpen] = useState(false);
    const [newName, setNewName] = useState(resource.name);

    // 打开预览弹窗并回调 onOpen
    const handlePreview = () => {
        setPreviewOpen(true);
        onOpen(resource);
    };

    // 打开重命名弹窗时重置输入框
    const handleRenameOpenChange = (open: boolean) => {
        setRenameOpen(open);
        if (open) {
            setNewName(resource.name);
        }
    };

    const handleDownload = async () => {
        window.open(`/api/storage/download/${resource.file?.id}`, '_blank');
    };


    // 关闭预览弹窗
    const handlePreviewOpenChange = (open: boolean) => {
        setPreviewOpen(open);
    };

    // 提交重命名
    const handleRenameSubmit = () => {
        if (newName && newName !== resource.name) {
            onRename(resource, newName);
        }
        setRenameOpen(false);
    };

    const handleDelete = async () => {
        setDeleteOpen(true)
    };


    const handleDeleteSubmit = () => {

    };

    const renderIcon = () => {
        // 否则使用通用文件图标组件
        return (
            <FileIcon
                filename={resource.name}
                type={resource.type}
                ext={resource.file?.ext}
                size="lg"
            />
        );
    };

    return (
        <>
            <ContextMenu>
                <ContextMenuTrigger>
                    <div
                        className={cn(
                            'group relative flex flex-col items-center justify-center p-4 rounded-lg border border-transparent hover:bg-accent hover:border-accent-foreground/20 transition-colors',
                            (!permissions || permissions.file.preview) ? 'cursor-pointer' : 'cursor-default',
                            className
                        )}
                        onClick={(!permissions || permissions.file.preview) ? handlePreview : undefined}
                    >
                        {renderIcon()}
                        <span
                            className="mt-2 text-sm font-medium text-center break-all line-clamp-2">{resource.name}
                        </span>
                    </div>
                </ContextMenuTrigger>
                <ContextMenuContent>
                    {/* 根据权限显示预览选项 */}
                    {(!permissions || permissions.file.preview) && (
                        <ContextMenuItem onClick={() => setPreviewOpen(true)}>
                            打开
                        </ContextMenuItem>
                    )}
                    {/* 根据权限显示重命名选项 */}
                    {(!permissions || permissions.file.rename) && (
                        <ContextMenuItem onClick={() => handleRenameOpenChange(true)}>
                            重命名
                        </ContextMenuItem>
                    )}
                    {/* 下载 */}
                    {(!permissions || permissions.file.download) && (
                        <ContextMenuItem onClick={() => handleDownload()}>
                            下载
                        </ContextMenuItem>
                    )}
                    {/* 删除 */}
                    {(!permissions || permissions.file.delete) && (
                        <ContextMenuItem onClick={() => handleDelete()}>
                            删除
                        </ContextMenuItem>
                    )}
                </ContextMenuContent>
            </ContextMenu>

            {/* 文件预览弹窗 */}
            {resource.file ? (
                <Sheet open={previewOpen} onOpenChange={(open) => {
                    if (open !== previewOpen) {
                        handlePreviewOpenChange(open);
                    }
                }}>
                    <SheetContent className="sm:min-w-full sm:min-h-full">
                        <SheetHeader>
                            <SheetTitle>{resource.name}</SheetTitle>
                        </SheetHeader>
                        <FilePreview showFullButton={false} url={resource.file.metadata?.previewUrl || ''}/>
                    </SheetContent>
                </Sheet>
            ) : null}

            {/* 重命名弹窗 */}
            <Dialog open={renameOpen} onOpenChange={(open) => {
                if (open !== renameOpen) {
                    handleRenameOpenChange(open);
                }
            }}>
                <DialogContent>
                    <DialogHeader>
                        <DialogTitle>重命名</DialogTitle>
                    </DialogHeader>
                    <div className="py-4">
                        <Input
                            value={newName}
                            onChange={(e) => setNewName(e.target.value)}
                            placeholder="请输入新名称"
                            onKeyDown={(e) => {
                                if (e.key === 'Enter' && newName) {
                                    handleRenameSubmit();
                                }
                            }}
                            autoFocus
                        />
                    </div>
                </DialogContent>
            </Dialog>


            <AlertDialog open={deleteOpen}>
                <AlertDialogContent>
                    <AlertDialogHeader>
                        <AlertDialogTitle>你确定要删除吗</AlertDialogTitle>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                        <AlertDialogCancel onClick={() => setDeleteOpen(false)}>取消</AlertDialogCancel>
                        <AlertDialogAction onClick={() => {
                            onDelete(resource)
                            setDeleteOpen(false)
                        }}>确认</AlertDialogAction>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>
        </>
    );
};

export default File;
