/**
 * 文件浏览器的分页控制组件
 */

import React from 'react';
import {ChevronLeft, ChevronRight, ChevronsLeft, ChevronsRight,} from "lucide-react";

import {Button} from "@/components/ui/button";
import {Select, SelectContent, SelectItem, SelectTrigger, SelectValue,} from "@/components/ui/select";

/**
 * 分页组件的属性接口
 */
interface SharePaginationProps {
  /** 当前页码（从0开始） */
  currentPage: number;
  /** 总页数 */
  totalPages: number;
  /** 页码变化回调函数 */
  onPageChange: (page: number) => void;
  /** 当前每页数量 */
  pageSize: number;
  /** 每页数量变化回调函数 */
  onPageSizeChange: (pageSize: number) => void;
  /** 每页数量选项 */
  pageSizeOptions?: number[];
}

/**
 * 文件浏览器分页组件
 */
export const FinderPagination: React.FC<SharePaginationProps> = ({
  currentPage,
  totalPages,
  onPageChange,
  pageSize,
  onPageSizeChange,
  pageSizeOptions = [10, 20, 50, 100],
}) => {
  // 确保至少有 1 页
  const actualTotalPages = Math.max(1, totalPages);
  
  return (
    <div className="flex items-center justify-between gap-4 py-2">
      <div className="flex items-center gap-4">
        <div className="flex items-center space-x-2">
          <span className="text-sm font-medium">每页数量</span>
          <Select
            value={String(pageSize)}
            onValueChange={(value) => onPageSizeChange(Number(value))}
          >
            <SelectTrigger className="h-8 w-[70px]">
              <SelectValue placeholder={pageSize} />
            </SelectTrigger>
            <SelectContent side="top">
              {pageSizeOptions.map((size) => (
                <SelectItem key={size} value={String(size)}>
                  {size}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        
        <div className="text-sm text-muted-foreground">
          第 {currentPage + 1} 页 / 共 {actualTotalPages} 页
        </div>
      </div>
      
      <div className="flex items-center space-x-2">
        <Button
          variant="outline"
          size="icon"
          className="h-8 w-8 p-0"
          onClick={() => onPageChange(0)}
          disabled={currentPage <= 0}
          title="第一页"
        >
          <ChevronsLeft className="h-4 w-4" />
        </Button>
        
        <Button
          variant="outline"
          size="icon"
          className="h-8 w-8 p-0"
          onClick={() => onPageChange(currentPage - 1)}
          disabled={currentPage <= 0}
          title="上一页"
        >
          <ChevronLeft className="h-4 w-4" />
        </Button>
        
        <Button
          variant="outline"
          size="icon"
          className="h-8 w-8 p-0"
          onClick={() => onPageChange(currentPage + 1)}
          disabled={currentPage >= actualTotalPages - 1}
          title="下一页"
        >
          <ChevronRight className="h-4 w-4" />
        </Button>
        
        <Button
          variant="outline"
          size="icon"
          className="h-8 w-8 p-0"
          onClick={() => onPageChange(actualTotalPages - 1)}
          disabled={currentPage >= actualTotalPages - 1}
          title="最后一页"
        >
          <ChevronsRight className="h-4 w-4" />
        </Button>
      </div>
    </div>
  );
};