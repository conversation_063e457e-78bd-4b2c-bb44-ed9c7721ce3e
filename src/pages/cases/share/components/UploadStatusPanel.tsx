import React from 'react';
import {<PERSON><PERSON><PERSON><PERSON>cle, CheckCircle, FileUp, Loader2, X} from 'lucide-react';
import {Button} from '@/components/ui/button';
import FileIcon from '@/pages/cases/share/components/FileIcon';

export interface UploadItem {
  id: string;
  filename: string;
  status: 'uploading' | 'success' | 'error';
  progress?: number;
  errorMessage?: string;
}

interface UploadStatusPanelProps {
  uploads: UploadItem[];
  onClose: () => void;
  onRetry?: (id: string) => void;
  onClear?: () => void;
}

export const UploadStatusPanel: React.FC<UploadStatusPanelProps> = ({
  uploads,
  onClose,
  onRetry,
  onClear,
}) => {
  if (uploads.length === 0) return null;

  const hasErrors = uploads.some(item => item.status === 'error');
  const allCompleted = uploads.every(item => item.status === 'success' || item.status === 'error');
  
  return (
    <div className="fixed bottom-4 right-4 z-50 w-80 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
      <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 border-b border-gray-200 dark:border-gray-600">
        <div className="flex items-center gap-2">
          <FileUp className="h-4 w-4" />
          <h3 className="text-sm font-medium">文件上传状态</h3>
        </div>
        <div className="flex items-center gap-2">
          {allCompleted && onClear && (
            <Button 
              variant="ghost" 
              size="sm" 
              className="h-7 px-2 text-xs"
              onClick={onClear}
            >
              清除
            </Button>
          )}
          <Button 
            variant="ghost" 
            size="icon" 
            className="h-6 w-6" 
            onClick={onClose}
          >
            <X className="h-3.5 w-3.5" />
          </Button>
        </div>
      </div>
      
      <div className="max-h-60 overflow-y-auto">
        {uploads.map((item) => (
          <div 
            key={item.id} 
            className="p-3 border-b border-gray-100 dark:border-gray-700 last:border-0"
          >
            <div className="flex justify-between items-start mb-1">
              <div className="flex items-center gap-2">
                <div className="flex-shrink-0">
                  <FileIcon 
                    filename={item.filename}
                    size="sm"
                  />
                </div>
                <div className="text-sm font-medium truncate max-w-[160px]" title={item.filename}>
                  {item.filename}
                </div>
              </div>
              <div className="flex items-center">
                {item.status === 'uploading' && (
                  <Loader2 className="h-4 w-4 text-blue-500 animate-spin" />
                )}
                {item.status === 'success' && (
                  <CheckCircle className="h-4 w-4 text-green-500" />
                )}
                {item.status === 'error' && (
                  <AlertCircle className="h-4 w-4 text-red-500" />
                )}
              </div>
            </div>
            
            {item.status === 'uploading' && item.progress !== undefined && (
              <div className="w-full h-1.5 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden">
                <div 
                  className="h-full bg-blue-500 rounded-full transition-all duration-300 ease-in-out"
                  style={{ width: `${item.progress}%` }}
                />
              </div>
            )}
            
            {item.status === 'error' && item.errorMessage && (
              <div className="mt-1 text-xs text-red-500">
                {item.errorMessage}
                {onRetry && (
                  <Button
                    variant="link"
                    size="sm"
                    className="h-auto p-0 ml-2 text-xs text-blue-500"
                    onClick={() => onRetry(item.id)}
                  >
                    重试
                  </Button>
                )}
              </div>
            )}
          </div>
        ))}
      </div>
      
      {hasErrors && (
        <div className="p-2 bg-gray-50 dark:bg-gray-700 border-t border-gray-200 dark:border-gray-600 text-center">
          <span className="text-xs text-gray-500 dark:text-gray-400">
            部分文件上传失败
          </span>
          {onRetry && (
            <Button
              variant="link"
              size="sm"
              className="h-auto p-0 ml-2 text-xs"
              onClick={() => uploads.filter(u => u.status === 'error').forEach(u => onRetry(u.id))}
            >
              全部重试
            </Button>
          )}
        </div>
      )}
    </div>
  );
};

export default UploadStatusPanel;
