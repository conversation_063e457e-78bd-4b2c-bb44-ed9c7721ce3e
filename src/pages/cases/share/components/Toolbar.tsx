import React, {useEffect, useRef, useState} from 'react';
import {Button} from '@/components/ui/button';
import {FolderPlus, Search, Upload, X} from 'lucide-react';
import {Input} from '@/components/ui/input';
import {ShareLibraryPermissions} from '../config/permissions';

interface ShareToolbarProps {
  onCreateFolder: () => void;
  onUploadFiles: (files: File[]) => void;
  onSearch?: (query: string) => void;
  searchQuery?: string;
  permissions?: ShareLibraryPermissions; // 添加权限配置项
}

export const Toolbar: React.FC<ShareToolbarProps> = ({
  onCreateFolder,
  onUploadFiles,
  onSearch,
  searchQuery = '',
  permissions,
}) => {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [searchValue, setSearchValue] = useState(searchQuery);

  // Update local state when prop changes, but only if they're different
  useEffect(() => {
    if (searchValue !== searchQuery) {
      setSearchValue(searchQuery);
    }
  }, [searchQuery]);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      onUploadFiles(Array.from(e.target.files));
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };

  // 执行搜索
  const executeSearch = () => {
    if (onSearch) {
      onSearch(searchValue);
    }
  };

  // 键盘按回车搜索
  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      executeSearch();
    }
  };

  // 清除搜索
  const clearSearch = () => {
    // 只有当当前有搜索内容时才清除并通知父组件
    if (searchValue !== '') {
      setSearchValue('');
      if (onSearch) {
        onSearch('');
      }
    }
  };

  return (
    <div className="flex items-center gap-2 p-2 border-b">
      {/* 根据权限显示新建文件夹按钮 */}
      {(!permissions || permissions.folder.create) && (
        <Button 
          variant="outline" 
          size="sm" 
          className="flex items-center gap-1"
          onClick={onCreateFolder}
        >
          <FolderPlus size={16} />
          <span>新建文件夹</span>
        </Button>
      )}
      
      {/* 根据权限显示上传文件按钮 */}
      {(!permissions || permissions.upload.files) && (
        <Button 
          variant="outline" 
          size="sm" 
          className="flex items-center gap-1"
          onClick={() => fileInputRef.current?.click()}
        >
          <Upload size={16} />
          <span>上传文件</span>
        </Button>
      )}
      
      <div className="flex-1 ml-4 relative">
        <div className="relative flex items-center">
          <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            value={searchValue}
            onChange={(e) => setSearchValue(e.target.value)}
            onKeyDown={handleKeyDown}
            placeholder="搜索文件名和内容..."
            className="pl-8 h-9 pr-16"
          />
          {searchValue && (
            <button 
              onClick={clearSearch}
              className="absolute right-[70px] top-1/2 transform -translate-y-1/2 text-muted-foreground hover:text-foreground"
            >
              <X size={16} />
            </button>
          )}
          <Button 
            size="sm" 
            className="absolute right-1 top-1/2 transform -translate-y-1/2 h-7"
            onClick={executeSearch}
          >
            搜索
          </Button>
        </div>
      </div>
      
      <input
        type="file"
        ref={fileInputRef}
        className="hidden"
        multiple
        onChange={handleFileChange}
      />
    </div>
  );
}; 