import React, {useState} from 'react';
import {useDropzone} from 'react-dropzone';
import {cn} from '@/lib/utils';
import {ShareResource} from '@/service/cases/share';
import FileIcon from '@/pages/cases/share/components/FileIcon';
import {ContextMenu, ContextMenuContent, ContextMenuItem, ContextMenuTrigger,} from '@/components/ui/context-menu';
import {Dialog, DialogContent, DialogHeader, DialogTitle,} from '@/components/ui/dialog';
import {Input} from '@/components/ui/input';
import {ShareLibraryPermissions} from '../config/permissions';
import {
    AlertDialog, AlertDialogAction, AlertDialogCancel,
    AlertDialogContent,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle
} from "@/components/ui/alert-dialog";

interface FolderProps {
    resource: ShareResource;
    onOpen: (resource: ShareResource) => void;
    onRename: (resource: ShareResource, newName: string) => void;
    onDelete: (resource: ShareResource) => void;
    onDrop?: (files: File[], resourceId: string) => void;
    className?: string;
    permissions?: ShareLibraryPermissions; // 添加权限配置项
}

const Folder: React.FC<FolderProps> = ({resource, onOpen, onRename, onDelete, onDrop, className, permissions}) => {
    const {getRootProps, getInputProps, isDragActive} = useDropzone({
        onDrop: (files) => {
            // 只有在有权限且提供了onDrop回调时才执行
            if (onDrop && (!permissions || permissions.upload.folder)) {
                onDrop(files, resource.id);
            }
        },
        noClick: true,
        // 根据权限禁用拖放
        disabled: permissions && !permissions.upload.folder,
    });

    const [deleteOpen, setDeleteOpen] = useState(false);
    const [renameOpen, setRenameOpen] = React.useState(false);
    const [newName, setNewName] = React.useState(resource.name);

    const handleClick = () => {
        onOpen(resource);
    };

    const handleRenameOpenChange = (open: boolean) => {
        setRenameOpen(open);
        if (open) {
            setNewName(resource.name);
        }
    };

    const handleRenameSubmit = () => {
        if (newName && newName !== resource.name) {
            onRename(resource, newName);
        }
        setRenameOpen(false);
    };


    return (
        <>
            <ContextMenu>
                <ContextMenuTrigger>
                    <div
                        {...getRootProps()}
                        className={cn(
                            'group relative flex flex-col items-center justify-center p-4 rounded-lg border border-transparent hover:bg-accent hover:border-accent-foreground/20 transition-colors',
                            // 根据导航权限设置光标样式
                            (!permissions || permissions.folder.navigate) ? 'cursor-pointer' : 'cursor-default',
                            isDragActive && 'border-primary bg-primary/10',
                            className
                        )}
                        onClick={(!permissions || permissions.folder.navigate) ? handleClick : undefined}
                    >
                        <input {...getInputProps()} />
                        <FileIcon
                            filename={resource.name}
                            type={resource.type}
                            size="lg"
                        />
                        <span className="mt-2 text-sm font-medium text-center break-all line-clamp-2">
              {resource.name}
            </span>
                        {isDragActive && (
                            <div
                                className="absolute inset-0 flex items-center justify-center bg-primary/5 backdrop-blur-sm rounded-lg">
                                <span className="text-primary font-medium">释放以上传到此文件夹</span>
                            </div>
                        )}
                    </div>
                </ContextMenuTrigger>
                <ContextMenuContent>
                    {/* 根据权限显示打开选项 */}
                    {(!permissions || permissions.folder.navigate) && (
                        <ContextMenuItem onClick={() => onOpen(resource)}>
                            打开
                        </ContextMenuItem>
                    )}
                    {/* 根据权限显示重命名选项 */}
                    {(!permissions || permissions.folder.rename) && (
                        <ContextMenuItem onClick={() => handleRenameOpenChange(true)}>
                            重命名
                        </ContextMenuItem>
                    )}

                    {/* 删除 */}
                    {(!permissions || permissions.folder.delete) && (
                        <ContextMenuItem onClick={() => setDeleteOpen(true)}>
                            删除
                        </ContextMenuItem>
                    )}
                </ContextMenuContent>
            </ContextMenu>
            <Dialog open={renameOpen} onOpenChange={(open) => {
                if (open !== renameOpen) {
                    handleRenameOpenChange(open);
                }
            }}>
                <DialogContent>
                    <DialogHeader>
                        <DialogTitle>重命名</DialogTitle>
                    </DialogHeader>
                    <div className="py-4">
                        <Input
                            value={newName}
                            onChange={(e: React.ChangeEvent<HTMLInputElement>) => setNewName(e.target.value)}
                            placeholder="请输入新名称"
                            onKeyDown={(e: React.KeyboardEvent<HTMLInputElement>) => {
                                if (e.key === 'Enter' && newName) {
                                    handleRenameSubmit();
                                }
                            }}
                            autoFocus
                        />
                    </div>
                </DialogContent>
            </Dialog>

            <AlertDialog open={deleteOpen}>
                <AlertDialogContent>
                    <AlertDialogHeader>
                        <AlertDialogTitle>你确定要删除吗</AlertDialogTitle>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                        <AlertDialogCancel onClick={() => setDeleteOpen(false)}>取消</AlertDialogCancel>
                        <AlertDialogAction onClick={() => {
                            onDelete(resource)
                            setDeleteOpen(false)
                        }}>确认</AlertDialogAction>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>
        </>
    );
};

export default Folder;
