import React, {useCallback, useState} from 'react';
import {useQueryState} from 'nuqs';
import {toast} from 'sonner';
import {
    createFile,
    createFolder, deleteResource,
    fetchPath,
    fetchResources,
    FileSave,
    FolderSave,
    PathInfo,
    renameResource,
    ShareResource,
} from '@/service/cases/share';
import {uploadFile} from '@/service/storage';
import {Finder} from './components/Finder';
import {useAccess, useRequest} from 'umi';
import UploadStatusPanel, {UploadItem} from './components/UploadStatusPanel';
import {v4 as uuidv4} from 'uuid';
import {defaultPermissions, getPermissionsByAccess, ShareLibraryPermissions} from './config/permissions';


// 使用服务中定义的 PathInfo 接口
type PathItem = PathInfo;

export default function ShareLibrary() {
    const [pathHistory, setPathHistory] = useState<PathItem[]>([]);
    const [currentPath, setCurrentPath] = useQueryState('path', {defaultValue: 'root'});
    const [pageSize, setPageSize] = useState(50); // 每页显示数量
    const [page, setPage] = useState(0); // 当前页码
    const [uploadItems, setUploadItems] = useState<UploadItem[]>([]); // 上传状态列表
    const [showUploadPanel, setShowUploadPanel] = useState(false); // 是否显示上传面板
    const [searchQuery, setSearchQuery] = useState(''); // 搜索关键词

    // 获取权限控制实例
    const access = useAccess();

    // 根据访问控制实例生成权限配置
    const permissions: ShareLibraryPermissions = getPermissionsByAccess(access);

    // 使用 useRequest 加载资源列表
    const {data: resourceResponse, loading, refresh} = useRequest(
        () => fetchResources({
            page,
            pageSize,
            specification: {
                aggs: [],
                highlight: [],
                source: ['name', 'type', 'path', "file", "content", "tags", "createdTime"],
                conditions: {
                    parentId: {
                        term: {
                            parentId: currentPath
                        }
                    },
                    ...(searchQuery ? {
                        "name,content": {
                            multi_match: {
                                query: searchQuery,
                                fields: ['name', 'content']
                            }
                        }
                    } : {})
                }
            },
            sort: [
                {field: 'type', order: 'asc'},
                {field: 'createdTime', order: 'asc'}
            ]
        }),
        {
            refreshDeps: [currentPath, page, pageSize, searchQuery],
            onSuccess: (result) => {
                // 当切换目录时，更新路径历史
                if (currentPath !== 'root' && !pathHistory.some(item => item.id === currentPath)) {
                    // 当前路径不在历史中，需要获取完整路径
                    fetchResourcePath(currentPath);
                }
            }
        }
    );

    // 处理页码变化
    const handlePageChange = (newPage: number) => {
        if (newPage === page) return; // 已经是当前页
        setPage(newPage);
    };

    // 处理每页数量变化
    const handlePageSizeChange = (newPageSize: number) => {
        if (newPageSize === pageSize) return; // 数量没变
        setPageSize(newPageSize);
        // 改变每页数量后重置到第一页
        setPage(0);
    };

    // 处理搜索
    const handleSearch = (query: string) => {
        setSearchQuery(query);
        // 搜索时重置到第一页
        setPage(0);
    };

    // 导航到特定路径
    const handleNavigate = useCallback((index: number) => {
        if (index === -1) {
            // 点击根目录
            setCurrentPath('root');
            setPathHistory([]);
        } else {
            // 点击面包屑中的某个路径
            const newPath = pathHistory[index];
            setCurrentPath(newPath.id);

            // 去掉点击后面的路径，只保留到当前点击的路径
            setPathHistory(pathHistory.slice(0, index + 1));
        }
    }, [pathHistory, setCurrentPath]);

    // 打开资源
    const handleOpenResource = (resource: ShareResource) => {
        if (resource.type === 'DIRECTORY') {
            // 切换到新目录时重置页码
            setPage(0);
            setCurrentPath(resource.id);
            // 如果资源有路径信息，直接使用，但要过滤掉根目录
            if (resource.path && resource.path.length > 0) {
                const filteredPath = resource.path.filter(item => item.id !== 'root');
                setPathHistory(filteredPath);
            } else {
                // 否则获取路径信息
                fetchResourcePath(resource.id);
            }
        }
    };

    // 获取资源路径
    const {run: fetchResourcePath} = useRequest(
        (resourceId: string) => {
            return fetchPath(resourceId)
        },
        {
            manual: true,
            onSuccess: (result) => {
                if (result && Array.isArray(result)) {
                    // 过滤掉根目录，根目录的 id 通常是 'root'
                    const filteredPath = result.filter(item => item.id !== 'root');
                    setPathHistory(filteredPath);
                }
            },
            onError: (error) => {
                console.error('获取路径失败:', error);
                toast.error('获取路径信息失败');
            }
        }
    );

    // 使用 useRequest 处理创建文件夹
    const {run: runCreateFolder} = useRequest(
        (folderData: FolderSave) => createFolder(folderData),
        {
            manual: true,
            onSuccess: async () => {
                toast.success('文件夹创建成功');
                refresh(); // 刷新资源列表
            },
            onError: (error) => {
                console.error('Failed to create folder:', error);
                toast.error('文件夹创建失败');
            }
        }
    );

    // 创建文件夹
    const handleCreateFolder = (name: string) => {
        const newResource: FolderSave = {
            name,
            parentId: currentPath,
            type: 'DIRECTORY',
            tags: []
        };
        runCreateFolder(newResource);
    };

    // 使用 useRequest 处理重命名
    const {run: runRename} = useRequest(
        (params: { id: string, name: string }) => renameResource(params),
        {
            manual: true,
            onSuccess: async () => {
                toast.success('重命名成功');
                refresh(); // 刷新资源列表
            },
            onError: (error) => {
                console.error('Failed to rename resource:', error);
                toast.error('重命名失败');
            }
        }
    );


    // 使用 useRequest 处理重命名
    const {run: runDeleteResource} = useRequest(
        (id: string) => deleteResource(id),
        {
            manual: true,
            onSuccess: async () => {
                toast.success('删除成功');
                refresh(); // 刷新资源列表
            },
            onError: (error) => {
                if ('data' in error) {
                    toast.error((error.data as any).msg);
                } else {
                    toast.error("删除失败");
                }
                console.error('Failed to rename resource:', error);

            }
        }
    );

    // 处理重命名
    const handleRenameResource = (resource: ShareResource, newName: string) => {
        runRename({id: resource.id, name: newName});
    };
    const handleDeleteSource = (resource: ShareResource) => {
        runDeleteResource(resource.id)
    };

    // 使用 useRequest 处理文件上传
    const {run: runUploadFile} = useRequest(
        async (files: File[]) => {
            const results = [];
            const uploadItemsMap = new Map<string, UploadItem>();

            // 创建上传项并显示面板
            const newUploadItems = files.map(file => {
                const id = uuidv4();
                const item: UploadItem = {
                    id,
                    filename: file.name,
                    status: 'uploading',
                    progress: 0
                };
                uploadItemsMap.set(id, item);
                return item;
            });

            setUploadItems(prev => [...prev, ...newUploadItems]);
            setShowUploadPanel(true);

            // 处理每个文件上传
            for (const file of files) {
                const uploadId = newUploadItems.find(item => item.filename === file.name)?.id;
                if (!uploadId) continue;

                try {
                    // 模拟上传进度
                    const progressInterval = setInterval(() => {
                        setUploadItems(prev => {
                            const updatedItems = [...prev];
                            const itemIndex = updatedItems.findIndex(item => item.id === uploadId);
                            if (itemIndex >= 0 && updatedItems[itemIndex].status === 'uploading') {
                                const currentProgress = updatedItems[itemIndex].progress || 0;
                                if (currentProgress < 90) {
                                    updatedItems[itemIndex] = {
                                        ...updatedItems[itemIndex],
                                        progress: currentProgress + Math.floor(Math.random() * 10) + 5
                                    };
                                }
                            }
                            return updatedItems;
                        });
                    }, 300);

                    // 实际上传文件
                    const formData = new FormData();
                    formData.append('file', file);
                    const uploadRes = await uploadFile(formData);

                    if (!uploadRes?.data) {
                        throw new Error('文件上传失败');
                    }

                    // 创建资源记录
                    const newResource: FileSave = {
                        name: file.name,
                        parentId: currentPath,
                        type: 'FILE',
                        fileId: uploadRes.data,
                        tags: []
                    };

                    const result = await createFile(newResource);
                    results.push(result);

                    // 更新上传状态为成功
                    clearInterval(progressInterval);
                    setUploadItems(prev => {
                        const updatedItems = [...prev];
                        const itemIndex = updatedItems.findIndex(item => item.id === uploadId);
                        if (itemIndex >= 0) {
                            updatedItems[itemIndex] = {
                                ...updatedItems[itemIndex],
                                status: 'success',
                                progress: 100
                            };
                        }
                        return updatedItems;
                    });
                } catch (error) {
                    // 更新上传状态为错误
                    setUploadItems(prev => {
                        const updatedItems = [...prev];
                        const itemIndex = updatedItems.findIndex(item => item.id === uploadId);
                        if (itemIndex >= 0) {
                            updatedItems[itemIndex] = {
                                ...updatedItems[itemIndex],
                                status: 'error',
                                errorMessage: error instanceof Error ? error.message : '未知错误'
                            };
                        }
                        return updatedItems;
                    });
                    throw error;
                }
            }

            return results;
        },
        {
            manual: true,
            onSuccess: () => {
                refresh(); // 刷新资源列表
            },
            onError: (error) => {
                console.error('Failed to upload files:', error);
            }
        }
    );

    // 上传文件
    const handleFileUpload = (files: File[]) => {
        runUploadFile(files);
    };

    // 重试上传
    const handleRetryUpload = (id: string) => {
        const item = uploadItems.find(item => item.id === id);
        if (!item) return;

        // 创建一个新的 File 对象（实际场景中可能需要保存原始文件或重新获取）
        // 这里仅作为示例，实际实现可能需要调整
        const dummyFile = new File([
            new Blob([''], {type: 'application/octet-stream'})
        ], item.filename);

        // 更新状态为上传中
        setUploadItems(prev => {
            const updatedItems = [...prev];
            const itemIndex = updatedItems.findIndex(i => i.id === id);
            if (itemIndex >= 0) {
                updatedItems[itemIndex] = {
                    ...updatedItems[itemIndex],
                    status: 'uploading',
                    progress: 0,
                    errorMessage: undefined
                };
            }
            return updatedItems;
        });

        // 重新上传
        runUploadFile([dummyFile]);
    };

    // 清除上传记录
    const handleClearUploads = () => {
        setUploadItems(prev => prev.filter(item => item.status === 'uploading'));
        if (uploadItems.every(item => item.status !== 'uploading')) {
            setShowUploadPanel(false);
        }
    };

    // 使用 useRequest 处理文件夹拖放上传
    const {run: runFolderDrop} = useRequest(
        async (params: { files: File[], targetId: string }) => {
            const {files, targetId} = params;
            const results = [];
            const uploadItemsMap = new Map<string, UploadItem>();

            // 创建上传项并显示面板
            const newUploadItems = files.map(file => {
                const id = uuidv4();
                const item: UploadItem = {
                    id,
                    filename: file.name,
                    status: 'uploading',
                    progress: 0
                };
                uploadItemsMap.set(id, item);
                return item;
            });

            setUploadItems(prev => [...prev, ...newUploadItems]);
            setShowUploadPanel(true);

            // 处理每个文件上传
            for (const file of files) {
                const uploadId = newUploadItems.find(item => item.filename === file.name)?.id;
                if (!uploadId) continue;

                try {
                    // 模拟上传进度
                    const progressInterval = setInterval(() => {
                        setUploadItems(prev => {
                            const updatedItems = [...prev];
                            const itemIndex = updatedItems.findIndex(item => item.id === uploadId);
                            if (itemIndex >= 0 && updatedItems[itemIndex].status === 'uploading') {
                                const currentProgress = updatedItems[itemIndex].progress || 0;
                                if (currentProgress < 90) {
                                    updatedItems[itemIndex] = {
                                        ...updatedItems[itemIndex],
                                        progress: currentProgress + Math.floor(Math.random() * 10) + 5
                                    };
                                }
                            }
                            return updatedItems;
                        });
                    }, 300);

                    // 实际上传文件
                    const formData = new FormData();
                    formData.append('file', file);
                    const uploadRes = await uploadFile(formData);

                    if (!uploadRes?.data) {
                        throw new Error('文件上传失败');
                    }

                    // 创建资源记录
                    const newResource: FileSave = {
                        name: file.name,
                        parentId: targetId,
                        type: 'FILE',
                        fileId: uploadRes.data,
                        tags: []
                    };

                    const result = await createFile(newResource);
                    results.push(result);

                    // 更新上传状态为成功
                    clearInterval(progressInterval);
                    setUploadItems(prev => {
                        const updatedItems = [...prev];
                        const itemIndex = updatedItems.findIndex(item => item.id === uploadId);
                        if (itemIndex >= 0) {
                            updatedItems[itemIndex] = {
                                ...updatedItems[itemIndex],
                                status: 'success',
                                progress: 100
                            };
                        }
                        return updatedItems;
                    });
                } catch (error) {
                    // 更新上传状态为错误
                    setUploadItems(prev => {
                        const updatedItems = [...prev];
                        const itemIndex = updatedItems.findIndex(item => item.id === uploadId);
                        if (itemIndex >= 0) {
                            updatedItems[itemIndex] = {
                                ...updatedItems[itemIndex],
                                status: 'error',
                                errorMessage: error instanceof Error ? error.message : '未知错误'
                            };
                        }
                        return updatedItems;
                    });
                    throw error;
                }
            }

            return results;
        },
        {
            manual: true,
            onSuccess: () => {
                refresh(); // 刷新资源列表
            },
            onError: (error) => {
                console.error('Failed to upload files to folder:', error);
            }
        }
    );

    // 文件夹拖放上传
    const handleFolderDrop = (files: File[], targetId: string) => {
        runFolderDrop({files, targetId});
    };


    // 计算总页数
    const totalItems = resourceResponse?.total || 0;
    const totalPages = Math.ceil(totalItems / pageSize) || 1;
    const resources = resourceResponse?.data || [];

    return (
        <>
            <Finder
                resources={resources}
                pathHistory={pathHistory}
                onNavigate={handleNavigate}
                onCreateFolder={handleCreateFolder}
                onRenameResource={handleRenameResource}
                onDeleteResource={handleDeleteSource}
                onUploadFiles={handleFileUpload}
                onOpenResource={handleOpenResource}
                onFolderDrop={handleFolderDrop}
                loading={loading}
                onPathChange={(newPath: string) => {
                    setPage(0); // 切换路径时重置页码
                    setCurrentPath(newPath);
                }}
                currentPath={currentPath}
                currentPage={page}
                totalPages={totalPages}
                onPageChange={handlePageChange}
                pageSize={pageSize}
                onPageSizeChange={handlePageSizeChange}
                onSearch={handleSearch}
                searchQuery={searchQuery}
                permissions={permissions}
            />

            {/* 上传状态面板 */}
            {showUploadPanel && (
                <UploadStatusPanel
                    uploads={uploadItems}
                    onClose={() => setShowUploadPanel(false)}
                    onRetry={handleRetryUpload}
                    onClear={handleClearUploads}
                />
            )}
        </>
    );
}