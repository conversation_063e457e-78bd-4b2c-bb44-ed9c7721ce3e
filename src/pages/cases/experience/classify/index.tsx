import React, {useState} from 'react';
import {But<PERSON>} from "@/components/ui/button";
import {ChevronDown, ChevronRight, FolderPlus, Lock, Pencil, Plus, Search} from "lucide-react";
import {Sheet, SheetContent, SheetDescription, SheetHeader, SheetTitle,} from "@/components/ui/sheet";
import {ClassifyForm} from '@/pages/cases/experience/classify/components/classify-form';
import {AuthDialog} from '@/components/auth/auth-dialog';
import {useRequest} from "umi";
import {ExperienceClassify, fetchClassifiesTree, filterClassifies} from "@/service/cases/experience";
import {Input} from "@/components/ui/input";

export default function ClassifyPage() {
  const [data, setData] = useState<ExperienceClassify[]>([]);
  const [isSheetOpen, setIsSheetOpen] = useState(false);
  const [isAuthSheetOpen, setIsAuthSheetOpen] = useState(false);
  const [editingItem, setEditingItem] = useState<ExperienceClassify | null>(null);
  const [expandedRows, setExpandedRows] = useState<Set<string>>(new Set());
  const [searchKeyword, setSearchKeyword] = useState('');

const {refresh} =useRequest(fetchClassifiesTree, {
    onSuccess: (res) => {
      if (res) {
        setData(res);
      }
    }
  });

  const { run: runSearch } = useRequest(filterClassifies, {
    manual: true,
    onSuccess: (res) => {
      if (res) {
        setData(res);
      }
    }
  });

  const toggleRow = (id: string) => {
    const newExpandedRows = new Set(expandedRows);
    if (newExpandedRows.has(id)) {
      newExpandedRows.delete(id);
    } else {
      newExpandedRows.add(id);
    }
    setExpandedRows(newExpandedRows);
  };

  const handleSearch = (value: string) => {
    setSearchKeyword(value);
    if (value) {
      runSearch(value);
    } else {
      // 如果搜索框为空，重新加载树形数据
      fetchClassifiesTree('').then((res) => {
        if (res?.data) {
          setData(res.data);
        }
      });
    }
  };

  const handleAuthSuccess = () => {
    // 授权成功后可以刷新数据或进行其他操作
    setIsAuthSheetOpen(false);
    setEditingItem(null);
  };

  const renderClassify = (classify: ExperienceClassify, level: number = 0) => {
    const hasChildren = classify.children && classify.children.length > 0;
    const isExpanded = expandedRows.has(classify.id);

    return (
      <div key={classify.id}>
        <div 
          className="flex items-center py-2 px-4 hover:bg-accent cursor-pointer"
          style={{ paddingLeft: `${level * 20 + 16}px` }}
        >
          {hasChildren && (
            <Button
              variant="ghost"
              size="icon"
              className="h-6 w-6 mr-2"
              onClick={() => toggleRow(classify.id)}
            >
              {isExpanded ? (
                <ChevronDown className="h-4 w-4" />
              ) : (
                <ChevronRight className="h-4 w-4" />
              )}
            </Button>
          )}
          {!hasChildren && <div className="w-6 mr-2" />}
          <div className="w-32 text-sm text-muted-foreground">{classify.code}</div>
          <div className="flex-1">{classify.name}</div>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => {
              setEditingItem(classify);
              setIsSheetOpen(true);
            }}
          >
            <Pencil className="h-4 w-4 mr-2" />
            编辑
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => {
              setEditingItem({ parent: classify } as ExperienceClassify);
              setIsSheetOpen(true);
            }}
          >
            <FolderPlus className="h-4 w-4 mr-2" />
            添加子分类
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => {
              setEditingItem(classify);
              setIsAuthSheetOpen(true);
            }}
          >
            <Lock className="h-4 w-4 mr-2" />
            授权
          </Button>
        </div>
        {hasChildren && isExpanded && (
          <div>
            {classify.children?.map(child => renderClassify(child, level + 1))}
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="p-4">
      <div className="flex justify-between items-center mb-4">
        <h1 className="text-2xl font-bold">分类维护</h1>
        <Button onClick={() => {
          setEditingItem(null);
          setIsSheetOpen(true);
        }}>
          <Plus className="h-4 w-4 mr-2" />
          添加分类
        </Button>
      </div>

      <div className="flex items-center space-x-2 mb-4">
        <div className="relative flex-1 max-w-sm">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="搜索分类编码或名称"
            value={searchKeyword}
            onChange={(e) => handleSearch(e.target.value)}
            className="pl-8"
          />
        </div>
      </div>
      
      <div className="border rounded-lg">
        <div className="flex items-center py-2 px-4 bg-muted/50 border-b">
          <div className="w-6 mr-2" />
          <div className="w-32 text-sm font-medium text-muted-foreground">编码</div>
          <div className="flex-1 text-sm font-medium text-muted-foreground">名称</div>
          <div className="w-32" />
        </div>
        {data.map(classify => renderClassify(classify))}
      </div>

      <Sheet open={isSheetOpen} onOpenChange={setIsSheetOpen}>
        <SheetContent>
          <SheetHeader>
            <SheetDescription/>
            <SheetTitle>{editingItem ? '编辑分类' : '添加分类'}</SheetTitle>
          </SheetHeader>
          <div className='p-4'>
            <ClassifyForm
              parentItem={editingItem}
              onSuccess={() => {
                setIsSheetOpen(false);
                setEditingItem(null);
                refresh()
              }}
              isEditing={!!editingItem}
            />
          </div>
        </SheetContent>
      </Sheet>

      <AuthDialog
        open={isAuthSheetOpen}
        onOpenChange={setIsAuthSheetOpen}
        resourceId={editingItem?.id || ''}
        resourceType="CLASSIFY"
        onSuccess={handleAuthSuccess}
      />
    </div>
  );
}