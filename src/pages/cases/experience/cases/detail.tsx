import React from 'react';
import {useParams, useRequest} from "umi";
import {ExperienceCase, fetchCaseDetail, getTagColor} from "@/service/cases/experience";
import FilePreview from '@/components/file-preview';
import {Badge} from '@/components/ui/badge';

export default function CaseDetailPage() {
  const { id } = useParams<{ id: string }>();
  const [caseDetail, setCaseDetail] = React.useState<ExperienceCase>();

  // 加载案例详情
  useRequest(() => {
    if (id) {
      return fetchCaseDetail(id);
    }
    return Promise.reject('案例ID不存在');
  }, {
    onSuccess: (res) => {
      setCaseDetail(res);
    },
    ready: !!id
  });

  if (!caseDetail) {
    return <div className="p-4">加载中...</div>;
  }

  return (
    <div className="h-full flex flex-col">
      {/* 标题和基本信息 - 减少上下间距 */}
      <div className="flex-none py-4 px-4 space-y-2 bg-background/95 sticky top-0 z-10 shadow-sm">
        <h1 className="text-2xl font-bold text-center">{caseDetail.title}</h1>
        
        <div className="flex items-center justify-center gap-4 flex-wrap">
          {/* 元信息 */}
          <div className="flex items-center gap-4 text-sm text-muted-foreground">
            <div className="flex items-center gap-1">
              <span className="font-medium">年份</span>
              <span>{caseDetail.caseOfYear}</span>
            </div>
            <div className="flex items-center gap-1">
              <span className="font-medium">创建时间</span>
              <span>{caseDetail.postDate}</span>
            </div>
          </div>

          {/* 标签 */}
          <div className="flex flex-wrap gap-1">
            {caseDetail.tags.map(tag => (
              <Badge key={tag} variant={getTagColor(tag)}>
                {tag}
              </Badge>
            ))}
          </div>
        </div>
      </div>

      {/* 文件预览 - 占据剩余空间 */}
      <div className="flex-1 overflow-hidden">
        <FilePreview 
          url={caseDetail.file.metadata?.previewUrl || ""}
          height="100%"
          className="w-full h-full" 
        />
      </div>
    </div>
  );
} 