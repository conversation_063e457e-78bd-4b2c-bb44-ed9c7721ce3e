import React, {useState} from 'react';
import {But<PERSON>} from "@/components/ui/button";
import {Card} from "@/components/ui/card";
import {Input} from "@/components/ui/input";
import {Label} from "@/components/ui/label";
import {history, useParams, useRequest} from "umi";
import {
    ExperienceCaseStatus,
    ExperienceClassify,
    fetchCaseDetail,
    fetchClassifiesTree,
    saveCase
} from "@/service/cases/experience";
import {ClassifyTree} from './components/classify-tree';
import {uploadFile} from '@/service/storage';
import {Badge} from '@/components/ui/badge';
import {X} from 'lucide-react';
import {z} from "zod";
import {toast} from 'sonner';

// 表单验证 schema
const experienceCaseSchema = z.object({
  title: z.string().min(1, "标题不能为空"),
  classifyId: z.string().min(1, "请选择分类"),
  caseOfYear: z.string().regex(/^\d{4}$/, "请输入正确的年份"),
  tags: z.array(z.string()),
  content: z.string().min(1, "请上传文件"),
  fileId: z.string().min(1, "请上传文件")
});

type FormValues = z.infer<typeof experienceCaseSchema>;

// 格式化日期为 yyyy-MM-dd HH:mm:ss
const formatDateTime = (date: Date): string => {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  const seconds = String(date.getSeconds()).padStart(2, '0');
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
};

interface FormState {
  title: string;
  tags: string[];
  caseOfYear: string;
  status: ExperienceCaseStatus;
  postDate: string;
  postUser: string;
  fileId: string;
  id?: string;
  classifyId?: string;
  fileName?: string;
}

export default function EditCasePage() {
  const params = useParams<{ id: string }>();
  const isEdit = !!params.id;

  // 分类树数据
  const [classifyTree, setClassifyTree] = useState<ExperienceClassify[]>([]);
  
  // 表单数据
  const [formData, setFormData] = useState<FormState>({
    title: '',
    tags: [],
    caseOfYear: new Date().getFullYear().toString(),
    status: 'OFF_SHELVES',
    postDate: formatDateTime(new Date()),
    postUser: '', // TODO: 从登录用户获取
    fileId: '',
    id: undefined,
    classifyId: undefined,
    fileName: undefined
  });

  // 标签输入
  const [currentTag, setCurrentTag] = useState('');
  
  // 文件预览
  const [previewUrl, setPreviewUrl] = useState<string>();

  // 加载分类树
  useRequest(fetchClassifiesTree, {
    onSuccess: (res) => {
      if (res) {
        setClassifyTree(res);
      }
    }
  });

  // 加载案例数据
  useRequest(() => {
    if (!isEdit) return Promise.reject('不是编辑模式');
    return fetchCaseDetail(params.id);
  }, {
    ready: isEdit,
    onSuccess: (res) => {
      if (res) {
        setFormData({
          title: res.title,
          tags: res.tags,
          caseOfYear: res.caseOfYear,
          status: res.status,
          postDate: res.postDate,
          postUser: res.postUser,
          fileId: res.file.id,
          id: res.id,
          classifyId: res.classifyId,
          fileName: res.file.fileName // 编辑时不显示文件名
        });
      }
    }
  });

  // 保存案例
  const { run: saveExperienceCase, loading } = useRequest(saveCase, {
    manual: true,
    onSuccess: () => {
      toast.success(isEdit ? '编辑成功' : '创建成功');
      history.back();
    }
  });

  // 处理文件上传
  const handleFileUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      const formData = new FormData();
      formData.append('file', file);
      const res = await uploadFile(formData);
      if (res?.data) {
        setFormData(prev => ({
          ...prev,
          fileId: res.data,
          fileName: file.name
        }));
      }
    }
  };

  // 添加标签
  const addTag = (value: string) => {
    const tag = value.trim();
    if (tag && !formData.tags.includes(tag)) {
      setFormData(prev => ({
        ...prev,
        tags: [...prev.tags, tag]
      }));
    }
    setCurrentTag(''); // 清空输入框
  };

  // 处理标签输入
  const handleTagInput = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter' && currentTag.trim()) {
      e.preventDefault();
      addTag(currentTag);
    } else if (e.key === 'Backspace' && !currentTag) {
      // 当输入框为空且按下删除键时，删除最后一个标签
      e.preventDefault();
      setFormData(prev => ({
        ...prev,
        tags: prev.tags.slice(0, -1)
      }));
    }
  };

  // 处理失去焦点
  const handleBlur = () => {
    if (currentTag.trim()) {
      addTag(currentTag);
    }
  };

  // 移除标签
  const removeTag = (tagToRemove: string) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags.filter(tag => tag !== tagToRemove)
    }));
  };

  // 处理表单字段变化
  const handleFieldChange = (field: keyof FormState, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // 提交表单
  const handleSubmit = () => {
    if (!formData.title) {
      alert('请输入标题');
      return;
    }
    if (!formData.classifyId) {
      alert('请选择分类');
      return;
    }
    if (!formData.fileId) {
      alert('请上传文件');
      return;
    }

    const submitData = {
      title: formData.title,
      tags: formData.tags,
      caseOfYear: formData.caseOfYear,
      status: formData.status,
      postDate: formData.postDate,
      postUser: formData.postUser,
      fileId: formData.fileId,
      classifyId: formData.classifyId || '',
      id: formData.id || undefined,
      code: ''
    };

    saveExperienceCase(submitData);
  };

  return (
    <div className="flex h-full">
      {/* 左侧分类树 */}
      <ClassifyTree 
        data={classifyTree} 
        onClassifySelect={(id) => handleFieldChange('classifyId', id)}
        selectedClassifyId={formData.classifyId || ''}
      />

      {/* 右侧表单 */}
      <div className="flex-1 p-4 overflow-auto">
        <Card className="p-6">
          <h1 className="text-2xl font-bold mb-6">{isEdit ? '编辑案例' : '新建案例'}</h1>
          <div className="space-y-6">
            <div className="space-y-2">
              <Label>标题</Label>
              <Input
                value={formData.title}
                onChange={(e) => handleFieldChange('title', e.target.value)}
                placeholder="请输入标题"
              />
            </div>

            <div className="space-y-2">
              <Label>年份</Label>
              <Input
                value={formData.caseOfYear}
                onChange={(e) => handleFieldChange('caseOfYear', e.target.value)}
                placeholder="请输入年份"
              />
            </div>

            <div className="space-y-2">
              <Label>标签</Label>
              <div className="flex flex-wrap gap-2 p-2 border rounded-lg">
                <div className="flex flex-wrap gap-2">
                  {formData.tags.map(tag => (
                    <Badge key={tag} variant="secondary" className="flex items-center gap-1">
                      {tag}
                      <button
                        type="button"
                        onClick={() => removeTag(tag)}
                        className="h-3 w-3 rounded-full"
                      >
                        <X className="h-3 w-3 cursor-pointer" />
                      </button>
                    </Badge>
                  ))}
                </div>
                <Input
                  value={currentTag}
                  onChange={(e) => setCurrentTag(e.target.value)}
                  onKeyDown={handleTagInput}
                  onBlur={handleBlur}
                  placeholder="输入标签后按回车添加"
                  className="border-0 flex-1 p-0 focus-visible:ring-0 placeholder:text-muted-foreground min-w-[200px]"
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label>发布日期时间</Label>
              <Input
                type="datetime-local"
                value={formData.postDate.replace(' ', 'T')}
                onChange={(e) => {
                  const date = new Date(e.target.value);
                  handleFieldChange('postDate', formatDateTime(date));
                }}
              />
            </div>

            <div className="space-y-2">
              <Label>内容</Label>
              <div className="border rounded-lg p-4">
                <Input
                  type="file"
                  onChange={handleFileUpload}
                  className="mb-2"
                />
                {formData.fileId && (
                  <div className="flex items-center gap-2 p-2 border rounded">
                    <div className="flex-1 truncate">{formData.fileName || '已上传文件'}</div>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => {
                        setFormData(prev => ({
                          ...prev,
                          fileId: '',
                          fileName: undefined
                        }));
                      }}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                )}
              </div>
            </div>

            <div className="flex justify-end gap-4">
              <Button variant="outline" onClick={() => history.back()}>
                取消
              </Button>
              <Button onClick={handleSubmit} disabled={loading}>
                {loading ? '保存中...' : '保存'}
              </Button>
            </div>
          </div>
        </Card>
      </div>
    </div>
  );
} 