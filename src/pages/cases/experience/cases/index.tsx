import React, {useEffect, useState} from 'react';
import {But<PERSON>} from "@/components/ui/button";
import {Plus} from "lucide-react";
import {Access, history, useAccess, useRequest} from "umi";
import {ExperienceCase, ExperienceClassify, fetchCasesPage, fetchClassifiesTree} from "@/service/cases/experience";
import {CasesTable} from './components/cases-table';
import {ClassifyTree} from './components/classify-tree';
import {parseAsArrayOf, parseAsString, useQueryState} from "nuqs";
import {DataTableRowAction} from "@/components/data-table/types";
import {CasesAuthSheet} from './components/cases-auth-sheet';
import {Page, PageQuery, Specification} from '@/types';

export default function CasesPage() {
  const [data, setData] = useState<ExperienceClassify[]>([]);
  const [rowAction, setRowAction] = useState<DataTableRowAction<ExperienceCase> | null>(null);
  const [isAuthSheetOpen, setIsAuthSheetOpen] = useState(false);
  const [pageData, setPageData] = useState<Page<ExperienceCase>>();
  const access = useAccess();
  const [path, setPath] = useQueryState(
    "path",
      parseAsArrayOf(parseAsString, ","),
  );

  const [classifyId, setClassifyId] = useState<string>('')
  
  // 当 URL 中的 path 参数变化时，更新 classifyId
  useEffect(() => {
    if (path && path.length > 0 && path[0]) {
      setClassifyId(path[0]);
    }
  }, [path]);

  // 当 classifyId 变化时，更新 URL 中的 path 参数
  useEffect(() => {
    if (classifyId) {
      setPath([classifyId]);
    } else {
      // 当取消选择时，清除 URL 中的 path 参数
      setPath(null);
    }
  }, [classifyId, setPath])

  useRequest(fetchClassifiesTree, {
    onSuccess: (res) => {
      if (res) {
        setData(res);
      }
    }
  });

  const { run: fetchCases } = useRequest(fetchCasesPage, {
    manual: true
  });

  // 跳转到新增页面
  const handleAdd = () => {
    history.push('/experience/cases/new');
  };

  // 处理表格操作
  useEffect(() => {
    if (rowAction?.type === 'update') {
      history.push(`/experience/cases/edit/${rowAction.row.original.id}`);
    } else if (rowAction?.type === 'auth') {
      setIsAuthSheetOpen(true);
    }
  }, [rowAction]);

  // 处理查询操作
  const handleQuery = (query: PageQuery<Specification<ExperienceCase>>) => {
    return fetchCases(query).then(res => {
      setPageData(res);
      return res;
    });
  };

  return (
    <div className="flex h-full">
      {/* 左侧分类树 */}
      <ClassifyTree
        data={data}
        onClassifySelect={setClassifyId}
        selectedClassifyId={classifyId}
      />

      {/* 右侧表格 */}
      <div className="flex-1 p-4 overflow-auto">
        <div className="flex justify-between items-center mb-4">
          <h1 className="text-2xl font-bold">案例管理</h1>
          <Access accessible ={access.hasAction("EXPERIENCE:CASES:ADD")}>
            <Button onClick={handleAdd}>
              <Plus className="h-4 w-4 mr-2" />
              新建案例
            </Button>
          </Access>
        </div>
        <CasesTable
          path={classifyId}
          onQuery={handleQuery}
          setRowAction={setRowAction}
          data={pageData}
        />
        {rowAction?.type === 'auth' && (
          <CasesAuthSheet
            open={isAuthSheetOpen}
            onOpenChange={setIsAuthSheetOpen}
            experienceCase={rowAction.row.original}
            onSuccess={() => {
              setIsAuthSheetOpen(false);
              setRowAction(null);
            }}
          />
        )}
      </div>
    </div>
  );
}
