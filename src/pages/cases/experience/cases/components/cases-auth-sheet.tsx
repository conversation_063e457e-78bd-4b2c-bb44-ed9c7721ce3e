import React from 'react';
import {AuthDialog} from '@/components/auth/auth-dialog';
import {ExperienceCase} from '@/service/cases/experience';

interface CasesAuthSheetProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  experienceCase?: ExperienceCase;
  onSuccess?: () => void;
}

export function CasesAuthSheet({
  open,
  onOpenChange,
  experienceCase,
  onSuccess
}: CasesAuthSheetProps) {
  const handleSuccess = () => {
    onSuccess?.();
  };

  return (
    <AuthDialog
      open={open}
      onOpenChange={onOpenChange}
      resourceId={experienceCase?.id || ''}
      resourceType="CASE"
      onSuccess={handleSuccess}
    />
  );
} 