import React from "react";
import {ColumnDef} from "@tanstack/react-table";
import {Checkbox} from "@/components/ui/checkbox";
import {DataTableColumnHeader} from "@/components/data-table/data-table-column-header";
import {ExperienceCase, getTagColor} from "@/service/cases/experience";
import {Badge} from "@/components/ui/badge";
import {Card, CardContent} from "@/components/ui/card";
import {Link} from "umi";

export interface CasesTableColumnProps {
    actions?: ColumnDef<ExperienceCase>;
}

export function getColumns({actions}: CasesTableColumnProps): ColumnDef<ExperienceCase>[] {
    const columns: ColumnDef<ExperienceCase>[] = [
        {
            id: "select",
            header: ({table}) => (
                <Checkbox
                    checked={
                        table.getIsAllPageRowsSelected() ||
                        (table.getIsSomePageRowsSelected() && "indeterminate")
                    }
                    onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
                    aria-label="Select all"
                    className="translate-y-0.5"
                />
            ),
            cell: ({row}) => (
                <Checkbox
                    checked={row.getIsSelected()}
                    onCheckedChange={(value) => row.toggleSelected(!!value)}
                    aria-label="Select row"
                    className="translate-y-0.5"
                />
            ),
            enableSorting: false,
            enableHiding: false,
        },
        {
            accessorKey: "title",
            header: ({column}) => <DataTableColumnHeader column={column}/>,
            cell: ({row}) => (
                <Link to={`/experience/cases/${row.original.id}`}>
                    <div dangerouslySetInnerHTML={{__html: row.original.title}}/>
                </Link>
            ),
            meta: {
                title: "案例标题",
            },
            enableSorting: false,
            enableHiding: true,
        },
        {
            accessorKey: "caseOfYear",
            header: ({column}) => <DataTableColumnHeader column={column}/>,
            cell: ({row}) => <div>{row.getValue("caseOfYear")}</div>,
            meta: {
                title: "案例年度",
            },
            enableSorting: true,
            enableHiding: true,
        },
        {
            accessorKey: "tags",
            header: ({column}) => <DataTableColumnHeader column={column}/>,
            cell: ({row}) => {
                const tags = row.getValue("tags") as string[];
                return (
                    <div className="flex gap-1 flex-wrap">
                        {tags.map((tag) => (
                            <Badge key={tag} variant={getTagColor(tag)}>
                                {tag}
                            </Badge>
                        ))}
                    </div>
                );
            },
            meta: {
                title: "标签",
            },
            enableSorting: false,
            enableHiding: true,
        },
        {
            accessorKey: "status",
            header: ({column}) => <DataTableColumnHeader column={column}/>,
            cell: ({row}) => <div>{row.getValue("status")}</div>,
            meta: {
                title: "状态",
            },
            enableSorting: false,
            enableHiding: true,
        },
        {
            accessorKey: 'content',
            header: ({column}) => null,
            cell: ({row}) => {
                if (row.original.content.includes("<span class='highlight'>")) {
                    return (
                        <Card>
                            <CardContent>
                                <div className="whitespace-pre-wrap break-words"
                                     dangerouslySetInnerHTML={{__html: row.original.content}}></div>
                            </CardContent>
                        </Card>
                    )
                }
                return null
            },
            meta: {
                title: "状态",
                expend: true
            },
            enableSorting: false,
            enableHiding: true,
        },

    ];

    if (actions) {
        columns.push(actions);
    }

    return columns;
}
