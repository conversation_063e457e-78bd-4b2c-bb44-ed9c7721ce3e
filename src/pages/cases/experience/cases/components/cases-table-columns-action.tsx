import React from "react";
import type {DataTableRowAction} from "@/components/data-table/types";
import {ColumnDef} from "@tanstack/react-table";
import {Button} from "@/components/ui/button";
import {Lock, Pencil, Trash} from "lucide-react";
import {ExperienceCase} from "@/service/cases/experience";
import {AccessInstance, Access} from "umi";

export interface CasesTableColumnActionProps {
    setRowAction: React.Dispatch<React.SetStateAction<DataTableRowAction<ExperienceCase> | null>>;
    access: AccessInstance;
}

export function getActions({setRowAction, access}: CasesTableColumnActionProps): ColumnDef<ExperienceCase> | undefined {
    // 这里可以根据权限控制按钮的显示
    if (access.hasAction("EXPERIENCE:CASES")) {
        return {
            id: "actions",
            cell: ({row}) => {
                return (
                    <div className="flex items-center gap-2">
                        <Access accessible={access.hasAction("EXPERIENCE:CASES:EDIT")}>
                            <Button
                                variant="ghost"
                                size="icon"
                                className="h-8 w-8"
                                onClick={() =>
                                    setRowAction({
                                        type: "update",
                                        row: row,
                                    })
                                }
                            >
                                <Pencil className="h-4 w-4"/>
                                <span className="sr-only">编辑</span>
                            </Button>
                        </Access>
                        <Access accessible={access.hasAction("EXPERIENCE:CASES:AUTHORIZATION")}>
                            <Button
                                variant="ghost"
                                size="icon"
                                className="h-8 w-8"
                                onClick={() =>
                                    setRowAction({
                                        type: "auth",
                                        row: row,
                                    })
                                }
                            >
                                <Lock className="h-4 w-4"/>
                                <span className="sr-only">授权</span>
                            </Button>
                        </Access>
                        <Access accessible={access.hasAction("EXPERIENCE:CASES:DELETE")}>
                            <Button
                                variant="ghost"
                                size="icon"
                                className="h-8 w-8"
                                onClick={() =>
                                    setRowAction({
                                        type: "delete",
                                        row: row,
                                    })
                                }
                            >
                                <Trash className="h-4 w-4"/>
                                <span className="sr-only">删除</span>
                            </Button>
                        </Access>
                    </div>
                );
            },
        };
    }
    return undefined;
}
