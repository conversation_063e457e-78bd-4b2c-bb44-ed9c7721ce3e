import {DataTableFilterField, Option} from "@/components/data-table/types";
import {ExperienceCase} from "@/service/cases/experience";
import {Aggs} from "@/types";

export function getFilterFields(aggs: Aggs<ExperienceCase>): DataTableFilterField<ExperienceCase>[] {
    return [
        {
            id: "title,content",
            label: "搜索",
            placeholder: "搜索标题或内容",
            virtual: true,
            highlight:true,
        },
        {
            id:'path',
            label:'',
            placeholder:'',
            virtual: true,
            hidden:true,
            options:[]
        },
        {
            id: "status",
            label: "状态",
            placeholder: "案例状态",
            agg:true,
            options: [
                {label: '上架', value: 'ON_SHELVES'},
                {label: '下架', value: 'OFF_SHELVES'}
            ]
        },
        {
            id: "caseOfYear",
            label: "年度",
            placeholder: "年度",
            agg:true,
            options: aggs.caseOfYear?.buckets.map(item => {
                return {label: item.key, value: item.key, count: item.docCount} as Option
            }) || []
        }
    ]
}
