import React, { useState, useEffect } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Plus } from "lucide-react";
import { useRequest, useAccess } from "umi";
import { NewsPaper, deleteNewspaper, fetchNewspaperPage } from "@/service/news/newspaper";
import { NewspaperTable } from '@/pages/news/newspaper/components/newspaper-table';
import { Page, PageQuery, Specification } from '@/types';
import { DataTableRowAction } from "@/components/data-table/types";
import { Sheet, SheetContent, SheetHeader, SheetTitle } from "@/components/ui/sheet";
import { NewspaperForm } from '@/pages/news/newspaper/components//newspaper-form';
import {fetchNavigationTree, SelectOptions} from "@/service/news/navigation";
import {fetchBlockSelect} from "@/service/news/block";

export default function NewspaperPage() {
  const [pageData, setPageData] = useState<Page<NewsPaper>>();
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [editingItem, setEditingItem] = useState<NewsPaper>();
  const access = useAccess();
  const [rowAction, setRowActionState] = useState<DataTableRowAction<NewsPaper> | null>(null);
  const [selectOptions, setSelectOptions] = useState<SelectOptions>();

  const { run: deleteItem } = useRequest(deleteNewspaper, {
    manual: true,
    onSuccess: () => {
      // 刷新数据
      handleQuery({
        pageSize: 10,
        page: 0,
        specification: {}
      });
    },
  });

  // 获取选择项数据
  const handleSelectOption = async () => {
    const resNavigation = await fetchNavigationTree()
    const resBlock = await fetchBlockSelect()
    setSelectOptions({
      navigationOptions: resNavigation.data,
      blockOptions: resBlock.data
    })
  }

  // 加载栏目树
  useRequest(handleSelectOption);

  const handleQuery = async (query: PageQuery<Specification<NewsPaper>>) => {
    const response = await fetchNewspaperPage(query);
    setPageData(response.data);
    return response.data;
  };

  // 监听rowAction状态变化，触发相应处理
  useEffect(() => {
    if (rowAction) {
      handleRowAction(rowAction);
    }
  }, [rowAction]);

  const handleRowAction = (action: DataTableRowAction<NewsPaper> | null) => {
    // 如果为空，直接返回
    if (!action) return;

    // 将row的原始数据转换为完整的CasesNavigation对象
    const rowData = action.row.original;
    switch (action.type) {
      case "update":
        setEditingItem(rowData);
        setIsFormOpen(true);
        break
      case "delete":
        deleteItem(rowData.id!);
        break
      case "auth":
        // 查看详情
        setEditingItem(rowData);
        setIsFormOpen(true);
    }
  };

  const handleSuccess = () => {
    setIsFormOpen(false);
    setEditingItem(undefined);
    // 刷新数据
    handleQuery({
      pageSize: 10,
      page: 0,
      specification: {}
    });
  };

  const handleAdd = () => {
    setEditingItem(undefined);
    setIsFormOpen(true);
  };

  return (
    <div className="container mx-auto py-6">
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-2xl font-bold">报刊管理</h1>
        {access.hasAction('NEWS:NEWSPAPER:SAVE') && (
          <Button onClick={handleAdd}>
            <Plus className="mr-2 h-4 w-4" />
            新增报刊
          </Button>
        )}
      </div>

      <div className="w-full">
        <NewspaperTable
          onQuery={handleQuery}
          setRowAction={setRowActionState}
          data={pageData}
        />
      </div>

      <Sheet open={isFormOpen} onOpenChange={setIsFormOpen}>
        <SheetContent className="w-[1200px] sm:max-w-[1200px]">
          <SheetHeader>
            <SheetTitle>{editingItem ? '编辑报刊' : '新增报刊'}</SheetTitle>
          </SheetHeader>
          <div className="mt-6 p-4">
            <NewspaperForm
              initialData={editingItem}
              onSuccess={handleSuccess}
              onCancel={() => setIsFormOpen(false)}
              selectOption={selectOptions}
            />
          </div>
        </SheetContent>
      </Sheet>
    </div>
  );
}
