import React, {useEffect, useState} from 'react';
import {useF<PERSON>, SubmitHandler} from 'react-hook-form';
import {zodResolver} from '@hookform/resolvers/zod';
import * as z from 'zod';
import {Button} from '@/components/ui/button';
import {Form, FormControl, FormField, FormItem, FormLabel, FormMessage} from '@/components/ui/form';
import {Input} from '@/components/ui/input';
import {NewsPaper, saveNewspaper} from '@/service/news/newspaper';
import {useRequest} from 'umi';
import {Label} from "@/components/ui/label";
import {Badge} from "@/components/ui/badge";
import {X} from "lucide-react";
import {Select, SelectContent, SelectItem, SelectTrigger, SelectValue} from "@/components/ui/select";
import {NewsBlock} from "@/service/news/block";
import {uploadFile} from "@/service/storage";
import {SelectOptions} from "@/service/news/navigation";
import {getUserInfo} from "@/service/account";
import {toast} from "sonner";

const formSchema = z.object({
    id: z.string().optional(),
    title: z.string().min(1, '请输入报刊标题'),
    tags: z.array(z.string()),
    periods: z.number().min(1, '请输入期数'),
    pageNum: z.number().min(1, '请输入页数'),
    sponsor: z.string().min(1, '请输入主办'),
    publishDate: z.string().min(1, '请选择出版日期'),
    issueTime: z.string().min(1, '请选择发布日期'),
    issuer: z.string().min(1, '请输入发布人'),
    issueUnit: z.string().min(1, '请输入发布单位'),
    newsBlockId: z.string().min(1, '请选择模块'),
    navigationId: z.string().min(1, '请选择栏目'),
    status: z.enum(['ENABLED', 'DISABLED']).default('ENABLED'),
    file: z.any().optional(),
});

type ProjectFormValues = z.infer<typeof formSchema>;

interface ProjectFormProps {
    initialData?: NewsPaper;
    onSuccess?: () => void;
    onCancel?: () => void;
    selectOption?: SelectOptions;
}

interface FileState {
    file: {
        id: string,
        fileName: string;
    }
}

export function NewspaperForm({initialData, onSuccess, onCancel, selectOption}: ProjectFormProps) {
    const [newsBlockSelect, setNewsBlockSelect] = useState<NewsBlock[]>();
    const [fileData, setFileData] = useState<FileState>({
        file: {
            id: initialData?.file?.id || '',
            fileName: initialData?.file?.originalFilename || '',
        }
    })
    const isEdit = !!initialData?.id;
    const defaultValues: ProjectFormValues = {
        title: initialData?.title || '',
        tags: initialData?.tags || [],
        periods: initialData?.periods || 0,
        pageNum: initialData?.pageNum || 0,
        sponsor: initialData?.sponsor || '',
        publishDate: initialData?.publishDate || '',
        issueTime: initialData?.issueTime || '',
        issuer: initialData?.issuer || '',
        issueUnit: initialData?.issueUnit || '',
        newsBlockId: initialData?.newsBlock?.id || '',
        navigationId: initialData?.newsBlock?.navigation?.id || '',
        status: (initialData?.status as 'ENABLED' | 'DISABLED') || 'ENABLED',
    };
    const form = useForm<ProjectFormValues>({
        resolver: zodResolver(formSchema),
        defaultValues,
    });
    // 加载用户基本信息
    useRequest(getUserInfo, {
        onSuccess: (res) => {
            if (res) {
                if (!isEdit) {
                    const beijingTime = new Date(Date.now() + 8 * 3600 * 1000);
                    form.setValue("publishDate", beijingTime.toISOString().slice(0, 10))
                    form.setValue("issueTime", beijingTime.toISOString().slice(0, 10))
                    if (Object.keys(res).length > 0) {
                        form.setValue("issuer", res?.name || "",)
                        form.setValue("issueUnit", res?.orgs[0].parent.name || "")
                    } else {
                        toast.error('登录信息获取失败，请手动填写！');
                    }
                }
            }
        }
    });

    useEffect(() => {
        if (initialData?.newsBlock?.navigation?.id) {
            const blocks = selectOption?.blockOptions?.filter(
                data => data.navigation.id === initialData.newsBlock.navigation.id
            );
            setNewsBlockSelect(blocks);
        }
    }, [initialData?.newsBlock?.navigation?.id, selectOption?.blockOptions]);

    // 标签输入
    const [currentTag, setCurrentTag] = useState('');

    // 添加标签
    const addTag = (value: string) => {
        const tag = value.trim();
        if (tag && !form.getValues("tags").includes(tag)) {
            const newTags = [...form.getValues("tags"), tag];
            form.setValue("tags", newTags);  // 使用 setValue 更新标签
        }
        setCurrentTag("");  // 清空输入框
    };

    // 处理标签输入
    const handleTagInput = (e: React.KeyboardEvent<HTMLInputElement>) => {
        if (e.key === "Enter" && currentTag.trim()) {
            e.preventDefault();
            addTag(currentTag);  // 添加标签
        } else if (e.key === "Backspace" && !currentTag) {
            e.preventDefault();
            // 移除最后一个标签
            const tags = form.getValues("tags");
            tags.pop();
            form.setValue("tags", tags);
        }
    };

    // 处理失去焦点
    const handleBlur = () => () => {
        if (currentTag.trim()) {
            addTag(currentTag);  // 添加标签
        }
    };

    // 移除标签
    const removeTag = (tagToRemove: string) => {
        const tags = form.getValues("tags");
        form.setValue(
            "tags",
            tags.filter(att => att !== tagToRemove),
            { shouldDirty: true, shouldValidate: true } // 关键参数
        );
    };
    const obChangeBlockSelectOption = (id: string) => {
        setNewsBlockSelect(selectOption?.blockOptions?.filter(data => data.navigation.id === id));
    }

    // 处理文件上传
    const handleFileUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
        const files = Array.from(e.target.files || []);
        for (const file of files) {
            if (!file.type.includes("pdf")) {
                toast.error('请上传PDF类型的文件！');
                return;
            }
            const formData = new FormData();
            formData.append('file', file);
            const res = await uploadFile(formData);
            if (res?.data) {
                setFileData({
                    file: {
                        id: res.data,
                        fileName: file.name
                    }
                })
            }
        }
    };

    // 移除附件
    const removeAttachment = () => {
        setFileData(() => ({
            file: {
                id: '',
                fileName: '',
            },
        }));
    };


    const {loading: saveLoading, run: runSave} = useRequest(saveNewspaper, {
        manual: true,
        onSuccess: () => {
            onSuccess?.();
        },
    });

    const {loading: updateLoading, run: runUpdate} = useRequest(saveNewspaper, {
        manual: true,
        onSuccess: () => {
            onSuccess?.();
        },
    });

    const onSubmit: SubmitHandler<ProjectFormValues> = (data) => {
        if (fileData.file.id === '') {
            toast.error('请上传报刊的PDF附件');
            return;
        }
        if (isEdit && initialData?.id) {
            runUpdate({
                ...data,
                id: initialData.id,
                fileId: fileData.file.id
            });
        } else {
            // 确保parentId是字符串或undefined，不是null
            const formDataSave = {
                ...data,
                fileId: fileData.file.id
            };
            runSave(formDataSave);
        }
    };

    return (
        <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                <div className="flex space-x-4">

                    <FormField
                        control={form.control}
                        name="navigationId"
                        render={({field}) => (
                            <FormItem className="flex-1">
                                <FormLabel>栏目</FormLabel>
                                <Select
                                    defaultValue={field.value}
                                    onValueChange={(val) => {
                                        field.onChange(val);
                                        obChangeBlockSelectOption(val);
                                    }}>
                                    <FormControl>
                                        <SelectTrigger className="w-[100%]">
                                            <SelectValue placeholder="请选择栏目"/>
                                        </SelectTrigger>
                                    </FormControl>
                                    <SelectContent>
                                        {selectOption?.navigationOptions.map((option) => (
                                            <SelectItem key={option.id} value={option.id}>
                                                {option.title}
                                            </SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                                <FormMessage/>
                            </FormItem>
                        )}
                    />

                    <FormField
                        control={form.control}
                        name="newsBlockId"
                        render={({field}) => (
                            <FormItem className="flex-1">
                                <FormLabel>模块</FormLabel>
                                <Select
                                    defaultValue={field.value}
                                    onValueChange={field.onChange}>
                                    <FormControl>
                                        <SelectTrigger className="w-[100%]">
                                            <SelectValue placeholder="请选择模块"/>
                                        </SelectTrigger>
                                    </FormControl>
                                    <SelectContent>
                                        {newsBlockSelect?.map((option) => (
                                            <SelectItem key={option.id} value={option.id}>
                                                {option.title}
                                            </SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                                <FormMessage/>
                            </FormItem>
                        )}
                    />

                    <FormField
                        control={form.control}
                        name="title"
                        render={({field}) => (
                            <FormItem className="flex-1">
                                <FormLabel>报刊标题</FormLabel>
                                <FormControl>
                                    <Input placeholder="请输入" {...field} />
                                </FormControl>
                                <FormMessage/>
                            </FormItem>
                        )}
                    />
                </div>
                <div className="flex space-x-4">

                    <FormField
                        control={form.control}
                        name="periods"
                        render={({field}) => (
                            <FormItem className="flex-1">
                                <FormLabel>期数</FormLabel>
                                <FormControl>
                                    <Input
                                        type="number"
                                        placeholder="请输入"
                                        {...field} onChange={(e) => field.onChange(Number(e.target.value))} />
                                </FormControl>
                                <FormMessage/>
                            </FormItem>
                        )}
                    />

                    <FormField
                        control={form.control}
                        name="pageNum"
                        render={({field}) => (
                            <FormItem className="flex-1">
                                <FormLabel>页数</FormLabel>
                                <FormControl>
                                    <Input
                                        type="number"
                                        placeholder="请输入"
                                        {...field} onChange={(e) => field.onChange(Number(e.target.value))} />
                                </FormControl>
                                <FormMessage/>
                            </FormItem>
                        )}
                    />
                    <FormField
                        control={form.control}
                        name="sponsor"
                        render={({field}) => (
                            <FormItem className="flex-1">
                                <FormLabel>主办</FormLabel>
                                <FormControl>
                                    <Input placeholder="请输入" {...field} />
                                </FormControl>
                                <FormMessage/>
                            </FormItem>
                        )}
                    />
                </div>
                <div className="flex space-x-4">
                    <FormField
                        control={form.control}
                        name="publishDate"
                        render={({field}) => (
                            <FormItem className="flex-1">
                                <FormLabel>出版日期</FormLabel>
                                <FormControl>
                                    <Input
                                        type="date"
                                        placeholder="请选择"
                                        {...field}
                                    />
                                </FormControl>
                                <FormMessage/>
                            </FormItem>
                        )}
                    />
                    <FormField
                        control={form.control}
                        name="issueTime"
                        render={({field}) => (
                            <FormItem className="flex-1">
                                <FormLabel>发布日期</FormLabel>
                                <FormControl>
                                    <Input
                                        type="date"
                                        placeholder="请选择"
                                        {...field}
                                    />
                                </FormControl>
                                <FormMessage/>
                            </FormItem>
                        )}
                    />
                    <FormField
                        control={form.control}
                        name="issuer"
                        render={({field}) => (
                            <FormItem className="flex-1">
                                <FormLabel>发布人</FormLabel>
                                <FormControl>
                                    <Input placeholder="请输入" {...field} />
                                </FormControl>
                                <FormMessage/>
                            </FormItem>
                        )}
                    />
                </div>


                <div className="flex space-x-4">
                    <div className="space-y-2 flex-1">
                        <Label>标签</Label>
                        <div className="flex flex-wrap gap-2 p-2 border rounded-lg">
                            <div className="flex flex-wrap gap-2">
                                {form.watch("tags").map(tag => (
                                    <Badge key={tag} variant="secondary" className="flex items-center gap-1">
                                        {tag}
                                        <button
                                            type="button"
                                            onClick={() => removeTag(tag)}
                                            className="h-3 w-3 rounded-full"
                                        >
                                            <X className="h-3 w-3 cursor-pointer"/>
                                        </button>
                                    </Badge>
                                ))}
                            </div>
                            <Input
                                value={currentTag}
                                onChange={(e) => setCurrentTag(e.target.value)}
                                onKeyDown={(e) => handleTagInput(e)}
                                onBlur={handleBlur()}
                                placeholder="输入标签后按回车添加"
                                className="border-0 flex-1 p-0 focus-visible:ring-0 placeholder:text-muted-foreground h-5 min-w-[200px]"
                            />
                        </div>
                    </div>
                    <FormField
                        control={form.control}
                        name="issueUnit"
                        render={({field}) => (
                            <FormItem className="flex-1">
                                <FormLabel>发布单位</FormLabel>
                                <FormControl>
                                    <Input placeholder="请输入" {...field} />
                                </FormControl>
                                <FormMessage/>
                            </FormItem>
                        )}
                    />
                    {/*<FormField*/}
                    {/*    control={form.control}*/}
                    {/*    name="status"*/}
                    {/*    render={({field}) => (*/}
                    {/*        <FormItem className="flex-1">*/}
                    {/*            <FormLabel>状态</FormLabel>*/}
                    {/*            <Select onValueChange={field.onChange} defaultValue={field.value}>*/}
                    {/*                <FormControl>*/}
                    {/*                    <SelectTrigger className="w-[100%]">*/}
                    {/*                        <SelectValue placeholder="请选择状态"/>*/}
                    {/*                    </SelectTrigger>*/}
                    {/*                </FormControl>*/}
                    {/*                <SelectContent>*/}
                    {/*                    <SelectItem value="ENABLED">上架</SelectItem>*/}
                    {/*                    <SelectItem value="DISABLED">下架</SelectItem>*/}
                    {/*                </SelectContent>*/}
                    {/*            </Select>*/}
                    {/*            <FormMessage/>*/}
                    {/*        </FormItem>*/}
                    {/*    )}*/}
                    {/*/>*/}
                </div>


                <div className="space-y-2">
                    <Label>附件</Label>
                    <div className="border rounded-lg p-4">
                        <Input
                            type="file"
                            onChange={handleFileUpload}
                            className="mb-2"
                            multiple
                        />
                        <div className="space-y-2">
                            {fileData.file.id !== '' && (
                                <div key={fileData.file.id} className="flex items-center gap-2 p-2 border rounded">
                                    <div className="flex-1 truncate">{fileData.file.fileName}</div>
                                    <Button
                                        variant="ghost"
                                        size="sm"
                                        onClick={() => removeAttachment()}
                                    >
                                        <X className="h-4 w-4"/>
                                    </Button>
                                </div>
                            )}
                        </div>
                    </div>
                </div>

                <div className="flex justify-end space-x-4">
                    <Button type="button" variant="outline" onClick={onCancel}>
                        取消
                    </Button>
                    <Button type="submit" disabled={saveLoading || updateLoading}>
                        {isEdit ? '保存' : '创建'}
                    </Button>
                </div>
            </form>
        </Form>
    );
}
