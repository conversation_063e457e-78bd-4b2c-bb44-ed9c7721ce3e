import {ColumnDef} from "@tanstack/react-table";
import {NewsPaper} from "@/service/news/newspaper";
import {Badge} from "@/components/ui/badge";
import {DataTableColumnHeader} from "@/components/data-table/data-table-column-header";
import {Link} from "@@/exports";
import React from "react";
import {getTagColor} from "@/service/news/newspaper";

export const getColumns = ({
   actions,
}: {
    actions: any;
}): ColumnDef<NewsPaper>[] => {
    const columns: ColumnDef<NewsPaper>[] = [
        {
            accessorKey: "title",
            header: ({column}) => <DataTableColumnHeader column={column}/>,
            cell: ({row}) => (
                <Link to={`/news/newspaper-board?newspaperId=${row.original.id}`}>
                    <div dangerouslySetInnerHTML={{__html: row.original.title}}/>
                </Link>
            ),
            meta: {
                title: "标题",
            },
            enableSorting: false,
            enableHiding: true,
        },
        {
            accessorKey: "pageNum",
            header: ({column}) => <DataTableColumnHeader column={column}/>,
            cell: ({row}) => (
                    <div dangerouslySetInnerHTML={{__html: row.original.pageNum + "页"}}/>
            ),
            meta: {
                title: "页数",
            },
            enableSorting: false,
            enableHiding: true,
        },
        {
            accessorKey: "periods",
            header: ({column}) => <DataTableColumnHeader column={column}/>,
            cell: ({row}) => (
                    <div dangerouslySetInnerHTML={{__html: row.original.periods + "期"}}/>
            ),
            meta: {
                title: "期数",
            },
            enableSorting: false,
            enableHiding: true,
        },
        {
            accessorKey: "status",
            header: ({column}) => <DataTableColumnHeader column={column}/>,
            cell: ({row}) => (
                    <div dangerouslySetInnerHTML={{__html: row.original.status}}/>
            ),
            meta: {
                title: "状态",
            },
            enableSorting: false,
            enableHiding: true,
        },
        {
            accessorKey: "sponsor",
            header: ({column}) => <DataTableColumnHeader column={column}/>,
            cell: ({row}) => (
                    <div dangerouslySetInnerHTML={{__html: row.original.sponsor}}/>
            ),
            meta: {
                title: "主办",
            },
            enableSorting: false,
            enableHiding: true,
        },
        {
            accessorKey: "publishDate",
            header: ({column}) => <DataTableColumnHeader column={column}/>,
            cell: ({row}) => (
                    <div dangerouslySetInnerHTML={{__html: row.original.publishDate}}/>
            ),
            meta: {
                title: "出版日期",
            },
            enableSorting: false,
            enableHiding: true,
        },
        {
            accessorKey: "issueTime",
            header: ({column}) => <DataTableColumnHeader column={column}/>,
            cell: ({row}) => (
                    <div dangerouslySetInnerHTML={{__html: row.original.issueTime}}/>
            ),
            meta: {
                title: "发布时间",
            },
            enableSorting: false,
            enableHiding: true,
        },
        {
            accessorKey: "issuer",
            header: ({column}) => <DataTableColumnHeader column={column}/>,
            cell: ({row}) => (
                    <div dangerouslySetInnerHTML={{__html: row.original.issuer}}/>
            ),
            meta: {
                title: "发布人",
            },
            enableSorting: false,
            enableHiding: true,
        },
        {
            accessorKey: "tags",
            header: ({column}) => <DataTableColumnHeader column={column}/>,
            cell: ({row}) => {
                const tags = row.getValue("tags") as string[] || [];
                return (
                    <div className="flex gap-1 flex-wrap">
                        {tags.map((tag) => (
                            <Badge key={tag} variant={getTagColor(tag)}>
                                {tag}
                            </Badge>
                        ))}
                    </div>
                );
            },
            meta: {
                title: "标签",
            },
            enableSorting: false,
            enableHiding: true,
        },
        {
            accessorKey: "newsBlockId",
            header: ({column}) => (
                <DataTableColumnHeader column={column} title="所属模块"/>
            ),
            cell: ({row}) => {
                const newsType = (row.original.newsBlock.navigation.title + '-' + row.original.newsBlock.title) as string;
                return (
                    <Badge variant='default'>
                        {newsType}
                    </Badge>
                )
            },
            meta: {
                title: "所属模块",
            }
        }
    ]

    if (actions) {
        columns.push(actions);
    }
    return columns;
};
