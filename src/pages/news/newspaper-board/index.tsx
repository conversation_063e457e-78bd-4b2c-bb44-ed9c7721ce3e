import React, { useState, useEffect } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Plus } from "lucide-react";
import { useRequest, useAccess } from "umi";
import { NewspaperBoard, deleteNewspaperBoard, fetchNewspaperBoardPage } from "@/service/news/newspaper-board";
import { NewspaperBoardTable } from '@/pages/news/newspaper-board/components/newspaper-board-table';
import { Page, PageQuery, Specification } from '@/types';
import { DataTableRowAction } from "@/components/data-table/types";
import { Sheet, SheetContent, SheetHeader, SheetTitle } from "@/components/ui/sheet";
import { NewspaperBoardForm } from '@/pages/news/newspaper-board/components/newspaper-board-form';
import {parseAsString, useQueryState} from "nuqs";

export default function NewspaperPage() {
  const [pageData, setPageData] = useState<Page<NewspaperBoard>>();
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [editingItem, setEditingItem] = useState<NewspaperBoard>();
  const access = useAccess();
  const [rowAction, setRowActionState] = useState<DataTableRowAction<NewspaperBoard> | null>(null);

  const [newspaperId] = useQueryState(
      "newspaperId",
      parseAsString.withDefault(''),
  );

  const { run: deleteItem } = useRequest(deleteNewspaperBoard, {
    manual: true,
    onSuccess: () => {
      // 刷新数据
      handleQuery({
        pageSize: 10,
        page: 0,
        specification: {
          newspaperId: newspaperId
        }
      });
    },
  });

  const handleQuery = async (query: PageQuery<Specification<NewspaperBoard>>) => {
    const response = await fetchNewspaperBoardPage(query);
    setPageData(response.data);
    return response.data;
  };

  // 监听rowAction状态变化，触发相应处理
  useEffect(() => {
    if (rowAction) {
      handleRowAction(rowAction);
    }
  }, [rowAction]);

  const handleRowAction = (action: DataTableRowAction<NewspaperBoard> | null) => {
    // 如果为空，直接返回
    if (!action) return;

    // 将row的原始数据转换为完整的CasesNavigation对象
    const rowData = action.row.original;
    switch (action.type) {
      case "update":
        setEditingItem(rowData);
        setIsFormOpen(true);
        break
      case "delete":
        deleteItem(rowData.id!);
        break
      case "auth":
        // 查看详情
        setEditingItem(rowData);
        setIsFormOpen(true);
    }
  };

  const handleSuccess = () => {
    setIsFormOpen(false);
    setEditingItem(undefined);
    // 刷新数据
    handleQuery({
      pageSize: 10,
      page: 0,
      specification: {
        newspaperId: newspaperId
      }
    });
  };

  const handleAdd = () => {
    setEditingItem(undefined);
    setIsFormOpen(true);
  };

  return (
    <div className="container mx-auto py-6">
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-2xl font-bold">报刊版面管理</h1>
        {access.hasAction('NEWS:NEWSPAPERBOARD:SAVE') && (
          <Button onClick={handleAdd}>
            <Plus className="mr-2 h-4 w-4" />
            新增版面
          </Button>
        )}
      </div>

      <div className="w-full">
        <NewspaperBoardTable
          onQuery={handleQuery}
          setRowAction={setRowActionState}
          data={pageData}
        />
      </div>

      <Sheet open={isFormOpen} onOpenChange={setIsFormOpen}>
        <SheetContent className="w-[600px] sm:max-w-[600px]">
          <SheetHeader>
            <SheetTitle>{editingItem ? '编辑版面' : '新增版面'}</SheetTitle>
          </SheetHeader>
          <div className="mt-6 p-4">
            <NewspaperBoardForm
              initialData={editingItem}
              onSuccess={handleSuccess}
              onCancel={() => setIsFormOpen(false)}
            />
          </div>
        </SheetContent>
      </Sheet>
    </div>
  );
}
