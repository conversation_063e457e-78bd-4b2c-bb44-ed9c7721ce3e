import React, {useEffect, useState} from 'react';
import {useForm, SubmitHandler} from 'react-hook-form';
import {zodResolver} from '@hookform/resolvers/zod';
import * as z from 'zod';
import {Button} from '@/components/ui/button';
import {Form, FormControl, FormField, FormItem, FormLabel, FormMessage} from '@/components/ui/form';
import {Input} from '@/components/ui/input';
import {NewspaperBoard, saveNewspaperBoard} from '@/service/news/newspaper-board';
import {useRequest} from 'umi';
import {Label} from "@/components/ui/label";
import {X} from "lucide-react";
import {Select, SelectContent, SelectItem, SelectTrigger, SelectValue} from "@/components/ui/select";
import {uploadFileInfo} from "@/service/storage";
import {parseAsString, useQueryState} from "nuqs";
import {toast} from "sonner";

const formSchema = z.object({
    id: z.string().optional(),
    title: z.string().min(1, '请输入版面标题'),
    unfold: z.enum(['false', 'true']).default('false'),
    sequence: z.number().default(0),
    newspaperId: z.string()
});

type NewspaperBoardFormValues = z.infer<typeof formSchema>;

interface NewspaperBoardFormProps {
    initialData?: NewspaperBoard;
    onSuccess?: () => void;
    onCancel?: () => void;
}
interface ImgState {
    attachment: {
        id: string,
        fileName: string;
        url: string;
    }
}

export function NewspaperBoardForm({initialData, onSuccess, onCancel}: NewspaperBoardFormProps) {
    const [newspaperId] = useQueryState(
        "newspaperId",
        parseAsString.withDefault(''),
    );
    const [imgData, setImgData] = useState<ImgState>({
        attachment: {
            id: initialData?.image?.id || '',
            fileName: initialData?.image?.originalFilename || '',
            url: initialData?.image?.url || ''
        }
    })
    const isEdit = !!initialData?.id;
    const defaultValues: NewspaperBoardFormValues = {
        title: initialData?.title || '',
        sequence: initialData?.sequence || 0,
        unfold: initialData?.unfold || "false",
        newspaperId: initialData?.newspaper?.id || '',
    };
    const form = useForm<NewspaperBoardFormValues>({
        resolver: zodResolver(formSchema),
        defaultValues,
    });



    // 处理文件上传
    const handleFileUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
        const files = Array.from(e.target.files || []);
        for (const file of files) {
            if (!file.type.includes("image")) {
                toast.error('请上传图片类型的文件！');
                return;
            }
            const formData = new FormData();
            formData.append('file', file);
            const res = await uploadFileInfo(formData);
            if (res?.data) {
                setImgData({
                    attachment: {
                        id: res.data.id,
                        fileName: file.name,
                        url: res.data.url,
                    }
                });
            }
        }
    };

    // 移除附件
    const removeAttachment = () => {
        setImgData(() => ({
            attachment: {
                id: '',
                fileName: '',
                url: ''
            },
        }));
    };


    const {loading: saveLoading, run: runSave} = useRequest(saveNewspaperBoard, {
        manual: true,
        onSuccess: () => {
            onSuccess?.();
        },
    });

    const {loading: updateLoading, run: runUpdate} = useRequest(saveNewspaperBoard, {
        manual: true,
        onSuccess: () => {
            onSuccess?.();
        },
    });

    const onSubmit: SubmitHandler<NewspaperBoardFormValues> = (data) => {
        if (imgData.attachment.id === "") {
            toast.error('请上传版面图！');
            return;
        }
        if (isEdit && initialData?.id) {
            runUpdate({
                ...data,
                id: initialData.id,
                imageId: imgData.attachment?.id,
                newspaperId: newspaperId
            });
        } else {
            // 确保parentId是字符串或undefined，不是null
            const formDataSave = {
                ...data,
                imageId: imgData.attachment?.id,
                newspaperId: newspaperId
            };
            runSave(formDataSave);
        }
    };

    return (
        <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                <FormField
                    control={form.control}
                    name="title"
                    render={({field}) => (
                        <FormItem className="flex-1">
                            <FormLabel>版面标题</FormLabel>
                            <Select onValueChange={field.onChange} defaultValue={field.value}>
                                <FormControl>
                                    <SelectTrigger className="w-[100%]">
                                        <SelectValue placeholder="请选择版面"/>
                                    </SelectTrigger>
                                </FormControl>
                                <SelectContent>
                                    <SelectItem value="建华新闻">建华新闻</SelectItem>
                                    <SelectItem value="文化之光">文化之光</SelectItem>
                                    <SelectItem value="严精细选">严精细选</SelectItem>
                                    <SelectItem value="芳草地">芳草地</SelectItem>
                                </SelectContent>
                            </Select>
                            <FormMessage/>
                        </FormItem>
                    )}
                />
                <FormField
                    control={form.control}
                    name="sequence"
                    render={({field}) => (
                        <FormItem className="flex-1">
                            <FormLabel>序列</FormLabel>
                            <FormControl>
                                <Input
                                    type="number"
                                    placeholder="请输入排序号"
                                    {...field} onChange={(e) => field.onChange(Number(e.target.value))}
                                />
                            </FormControl>
                            <FormMessage/>
                        </FormItem>
                    )}
                />
                <FormField
                    control={form.control}
                    name="unfold"
                    render={({field}) => (
                        <FormItem className="flex-1">
                            <FormLabel>首图展开排版</FormLabel>
                            <Select onValueChange={field.onChange} defaultValue={field.value}>
                                <FormControl>
                                    <SelectTrigger className="w-[100%]">
                                        <SelectValue placeholder="请选择状态"/>
                                    </SelectTrigger>
                                </FormControl>
                                <SelectContent>
                                    <SelectItem value="true">启用</SelectItem>
                                    <SelectItem value="false">禁用</SelectItem>
                                </SelectContent>
                            </Select>
                            <FormMessage/>
                        </FormItem>
                    )}
                />


                <div className="space-y-2">
                    <Label>版面图</Label>
                    <div className="border rounded-lg p-4">
                        <Input
                            type="file"
                            onChange={handleFileUpload}
                            className="mb-2"
                            multiple
                        />
                        {imgData.attachment.id !== '' && (
                            <div className="space-y-2">
                                <div key={imgData.attachment.id} className="flex items-center gap-2 p-2 border rounded">
                                    <img
                                        className="w-15 h-15"
                                        src={imgData.attachment.url} alt="" />
                                    <Button
                                        variant="ghost"
                                        size="sm"
                                        onClick={() => removeAttachment()}
                                    >
                                        <X className="h-4 w-4" />
                                    </Button>
                                </div>
                            </div>
                        )}
                    </div>
                </div>

                <div className="flex justify-end space-x-4">
                    <Button type="button" variant="outline" onClick={onCancel}>
                        取消
                    </Button>
                    <Button type="submit" disabled={saveLoading || updateLoading}>
                        {isEdit ? '保存' : '创建'}
                    </Button>
                </div>
            </form>
        </Form>
    );
}
