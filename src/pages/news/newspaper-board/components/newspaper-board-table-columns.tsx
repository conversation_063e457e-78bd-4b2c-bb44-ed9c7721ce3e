import {ColumnDef} from "@tanstack/react-table";
import {NewspaperBoard} from "@/service/news/newspaper-board";
import {Badge} from "@/components/ui/badge";
import {DataTableColumnHeader} from "@/components/data-table/data-table-column-header";
import {Link} from "@@/exports";
import React from "react";

export const getColumns = ({
   actions,
}: {
    actions: any;
}): ColumnDef<NewspaperBoard>[] => {
    const columns: ColumnDef<NewspaperBoard>[] = [
        {
            accessorKey: "title",
            header: ({column}) => <DataTableColumnHeader column={column}/>,
            cell: ({row}) => (
                <Link to={`/news/resource-manager?newspaperBoardId=${row.original.id}`}>
                    <div dangerouslySetInnerHTML={{__html: row.original.title}}/>
                </Link>
            ),
            meta: {
                title: "标题",
            },
            enableSorting: false,
            enableHiding: true,
        },
        {
            accessorKey: "sequence",
            header: ({column}) => <DataTableColumnHeader column={column}/>,
            cell: ({row}) => (
                    <div dangerouslySetInnerHTML={{__html: row.original.sequence}}/>
            ),
            meta: {
                title: "序列",
            }
        },
        {
            accessorKey: "unfold",
            header: ({column}) => (
                <DataTableColumnHeader column={column} title="首图展开版面"/>
            ),
            cell: ({row}) => {
                const status = row.getValue("unfold");
                return (
                    <Badge variant={status === 'true' ? 'default' : 'secondary'}>
                        {status === 'true' ? '启用' : '禁用'}
                    </Badge>
                );
            },
            filterFn: (row, id, value) => {
                return value.includes(row.getValue(id));
            },
            meta: {
                title: "首图展开版面",
            },
            enableSorting: false,
            enableHiding: true
        },
        {
            accessorKey: "newspaperName",
            header: ({column}) => (
                <DataTableColumnHeader column={column} title="所属报刊"/>
            ),
            cell: ({row}) => {
                const newsType = (row.original.newspaper.title) as string;
                return (
                    <Badge variant='default'>
                        {newsType}
                    </Badge>
                )
            },
            meta: {
                title: "所属报刊",
            }
        }
    ]

    if (actions) {
        columns.push(actions);
    }
    return columns;
};
