import React, { useEffect } from 'react';
import {DataTableFilterField, DataTableRowAction} from "@/components/data-table/types";
import {useDataTable} from "@/components/data-table/hooks/use-data-table";
import {DataTable} from "@/components/data-table/data-table";
import {DataTableToolbar} from "@/components/data-table/data-table-toolbar";
import {getColumns} from './newspaper-board-table-columns';
import {NewspaperBoard} from '@/service/news/newspaper-board';
import {Page, PageQuery, Specification} from '@/types';
import {getFilterFields} from './newspaper-board-table-filters';
import {useAccess} from "umi";
import {getActions} from './newspaper-board-table-columns-action';
import {parseAsString, useQueryState} from "nuqs";

interface NewsPaperTableProps {
  onQuery: (query: PageQuery<Specification<NewspaperBoard>>) => Promise<Page<NewspaperBoard>>;
  setRowAction: React.Dispatch<React.SetStateAction<DataTableRowAction<NewspaperBoard> | null>>;
  data?: Page<NewspaperBoard>;
}

export function NewspaperBoardTable({ onQuery, setRowAction, data }: NewsPaperTableProps) {
  const access = useAccess();
  const [newspaperId] = useQueryState(
      "newspaperId",
      parseAsString.withDefault(''),
  );

  const actions = React.useMemo(
    () => getActions({ setRowAction, access }),
    [setRowAction, access]
  );

  const columns = React.useMemo(() => getColumns({ actions }), [actions]);
  const [filterFields, setFilterFields] = React.useState<DataTableFilterField<NewspaperBoard>[]>(getFilterFields());



  const { table } = useDataTable({
    data: data?.data || [],
    columns,
    pageCount: Math.ceil((data?.total || 0) / (data?.pageSize || 10)),
    filterFields,
    initialState: {
      pagination: {
        pageIndex: 0,
        pageSize: 10,
      },
      columnPinning: { right: ["actions"] },
      // columnVisibility:{
      //   'member':false,
      //   'projectCost':false,
      //   'planningCycle':false
      // }
    },
    getRowId: (originalRow) => originalRow.id!,
  });

  // 监听表格状态变化，触发查询
  useEffect(() => {
    setFilterFields(getFilterFields());
  }, [data]);

  return (
    <div className="space-y-4">
      <DataTableToolbar table={table} filterFields={filterFields} onFilterChange={onQuery} />
      <DataTable table={table} />
    </div>
  );
}
