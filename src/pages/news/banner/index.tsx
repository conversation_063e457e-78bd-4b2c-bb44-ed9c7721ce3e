import React, { useState, useEffect } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Plus } from "lucide-react";
import { useRequest, useAccess } from "umi";
import { NewsBanner, deleteBanner, fetchBannerPage } from "@/service/news/banner";
import { BannerTable } from '@/pages/news/banner/components/banner-table';
import { Page, PageQuery, Specification } from '@/types';
import { DataTableRowAction } from "@/components/data-table/types";
import { Sheet, SheetContent, SheetHeader, SheetTitle } from "@/components/ui/sheet";
import { BannerForm } from '@/pages/news/banner/components/banner-form';
import {NavigationTreeOptions, fetchNavigationTree} from "@/service/news/navigation";

export default function NewspaperPage() {
  const [pageData, setPageData] = useState<Page<NewsBanner>>();
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [editingItem, setEditingItem] = useState<NewsBanner>();
  const [selectOption, setSelectOption] = useState<NavigationTreeOptions[]>([]);
  const access = useAccess();
  const [rowAction, setRowActionState] = useState<DataTableRowAction<NewsBanner> | null>(null);

  const { run: deleteItem } = useRequest(deleteBanner, {
    manual: true,
    onSuccess: () => {
      // 刷新数据
      handleQuery({
        pageSize: 10,
        page: 0,
        specification: {}
      });
    },
  });

  const handleQuery = async (query: PageQuery<Specification<NewsBanner>>) => {
    const response = await fetchBannerPage(query);
    setPageData(response.data);
    return response.data;
  };

  // 监听rowAction状态变化，触发相应处理
  useEffect(() => {
    if (rowAction) {
      handleRowAction(rowAction);
    }
  }, [rowAction]);

  useRequest(fetchNavigationTree, {
    onSuccess(res){
      setSelectOption(res);
    }
  });

  const handleRowAction = (action: DataTableRowAction<NewsBanner> | null) => {
    // 如果为空，直接返回
    if (!action) return;

    // 将row的原始数据转换为完整的CasesNavigation对象
    const rowData = action.row.original;
    switch (action.type) {
      case "update":
        setEditingItem(rowData);
        setIsFormOpen(true);
        break
      case "delete":
        deleteItem(rowData.id!);
        break
      case "auth":
        // 查看详情
        setEditingItem(rowData);
        setIsFormOpen(true);
    }
  };

  const handleSuccess = () => {
    setIsFormOpen(false);
    setEditingItem(undefined);
    // 刷新数据
    handleQuery({
      pageSize: 10,
      page: 0,
      specification: {}
    });
  };

  const handleAdd = () => {
    setEditingItem(undefined);
    setIsFormOpen(true);
  };

  return (
    <div className="container mx-auto py-6">
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-2xl font-bold">banner管理</h1>
        {access.hasAction('NEWS:BANNER:SAVE') && (
          <Button onClick={handleAdd}>
            <Plus className="mr-2 h-4 w-4" />
            新增banner
          </Button>
        )}
      </div>

      <div className="w-full">
        <BannerTable
          onQuery={handleQuery}
          setRowAction={setRowActionState}
          data={pageData}
          selectOption={selectOption}
        />
      </div>

      <Sheet open={isFormOpen} onOpenChange={setIsFormOpen}>
        <SheetContent className="w-[900px] sm:max-w-[900px]">
          <SheetHeader>
            <SheetTitle>{editingItem ? '编辑banner' : '新增banner'}</SheetTitle>
          </SheetHeader>
          <div className="mt-6 p-4">
            <BannerForm
              initialData={editingItem}
              onSuccess={handleSuccess}
              onCancel={() => setIsFormOpen(false)}
              selectOption={selectOption}
            />
          </div>
        </SheetContent>
      </Sheet>
    </div>
  );
}
