import React, {useEffect, useState} from 'react';
import {useForm, SubmitHandler} from 'react-hook-form';
import {zodResolver} from '@hookform/resolvers/zod';
import * as z from 'zod';
import {Button} from '@/components/ui/button';
import {Form, FormControl, FormField, FormItem, FormLabel, FormMessage} from '@/components/ui/form';
import {Input} from '@/components/ui/input';
import {NewsBanner, saveBanner} from '@/service/news/banner';
import {NavigationTreeOptions} from '@/service/news/navigation';
import {useRequest} from 'umi';
import {Label} from "@/components/ui/label";
import {X} from "lucide-react";
import {Select, SelectContent, SelectItem, SelectTrigger, SelectValue} from "@/components/ui/select";
import {uploadFileInfo} from "@/service/storage";
import {toast} from "sonner";
import {getUserInfo} from "@/service/account";

const formSchema = z.object({
    id: z.string().optional(),
    title: z.string().min(1, '请输入标题'),
    navigationId: z.string().min(1, '请选择栏目'),
    articleUrl: z.string().optional(),
    issuer: z.string().min(1, '请输入发布人'),
    issueDate: z.string().min(1, '请输入发布时间'),
    sequence: z.number().default(0),
    newspaper: z.any().optional(),
});

type BannerFormValues = z.infer<typeof formSchema>;

interface BannerFormProps {
    initialData?: NewsBanner;
    onSuccess?: () => void;
    onCancel?: () => void;
    selectOption?: NavigationTreeOptions[];
}

interface ImgState {
    attachment: {
        id: string,
        fileName: string;
        url: string;
    }
}

export function BannerForm({initialData, onSuccess, onCancel, selectOption}: BannerFormProps) {
    const [imgData, setImgData] = useState<ImgState>({
        attachment: {
            id: initialData?.image?.id || '',
            fileName: initialData?.image?.originalFilename || '',
            url: initialData?.image?.url || ''
        }
    })
    const isEdit = !!initialData?.id;
    const defaultValues: BannerFormValues = {
        title: initialData?.title || '',
        sequence: initialData?.sequence || 0,
        navigationId: initialData?.navigation?.id || '',
        articleUrl: initialData?.articleUrl || '',
        issuer: initialData?.issuer || '',
        issueDate: initialData?.issueDate || '',
    };
    const form = useForm<BannerFormValues>({
        resolver: zodResolver(formSchema),
        defaultValues,
    });

    // 处理文件上传
    const handleFileUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
        const files = Array.from(e.target.files || []);
        for (const file of files) {
            if (!file.type.includes("image")) {
                toast.error('请上传图片类型的文件！');
                return;
            }
            const formData = new FormData();
            formData.append('file', file);
            const res = await uploadFileInfo(formData);
            if (res?.data) {
                setImgData({
                    attachment: {
                        id: res.data.id,
                        fileName: file.name,
                        url: res.data.url,
                    }
                });
            }
        }
    };
    // 加载用户基本信息
    useRequest(getUserInfo, {
        onSuccess: (res) => {
            if (res) {
                if (!isEdit) {
                    const beijingTime = new Date(Date.now() + 8 * 3600 * 1000);
                    form.setValue("issueDate", beijingTime.toISOString().slice(0, 10))
                    if (Object.keys(res).length > 0) {
                        form.setValue("issuer", res?.name || "",)
                    } else {
                        toast.error('登录信息获取失败，请手动填写！');
                    }
                }
            }
        }
    });

    // 移除附件
    const removeAttachment = () => {
        setImgData(() => ({
            attachment: {
                id: '',
                fileName: '',
                url: ''
            },
        }));
    };


    const {loading: saveLoading, run: runSave} = useRequest(saveBanner, {
        manual: true,
        onSuccess: () => {
            onSuccess?.();
        },
    });

    const {loading: updateLoading, run: runUpdate} = useRequest(saveBanner, {
        manual: true,
        onSuccess: () => {
            onSuccess?.();
        },
    });

    const onSubmit: SubmitHandler<BannerFormValues> = (data) => {
        if (isEdit && initialData?.id) {
            runUpdate({
                ...data,
                id: initialData.id,
                imageId: imgData.attachment?.id
            });
        } else {
            // 确保parentId是字符串或undefined，不是null
            const formDataSave = {
                ...data,
                imageId: imgData.attachment?.id
            };
            runSave(formDataSave);
        }
    };

    return (
        <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                <div className="flex space-x-4">
                    <FormField
                        control={form.control}
                        name="navigationId"
                        render={({field}) => (
                            <FormItem className="flex-1">
                                <FormLabel>栏目</FormLabel>
                                <Select
                                    defaultValue={field.value}
                                    onValueChange={field.onChange}>
                                    <FormControl>
                                        <SelectTrigger className="w-[100%]">
                                            <SelectValue placeholder="请选择栏目"/>
                                        </SelectTrigger>
                                    </FormControl>
                                    <SelectContent>
                                        {selectOption?.map((option) => (
                                            <SelectItem key={option.id} value={option.id}>
                                                {option.title}
                                            </SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                                <FormMessage/>
                            </FormItem>
                        )}
                    />

                    <FormField
                        control={form.control}
                        name="title"
                        render={({field}) => (
                            <FormItem className="flex-1">
                                <FormLabel>标题</FormLabel>
                                <FormControl>
                                    <Input placeholder="请输入" {...field} />
                                </FormControl>
                                <FormMessage/>
                            </FormItem>
                        )}
                    />
                </div>
                <div className="flex space-x-4">
                    <FormField
                        control={form.control}
                        name="articleUrl"
                        render={({field}) => (
                            <FormItem className="flex-1">
                                <FormLabel>新闻关联code</FormLabel>
                                <FormControl>
                                    <Input placeholder="请输入（由新闻列表生成）" {...field} />
                                </FormControl>
                                <FormMessage/>
                            </FormItem>
                        )}
                    />
                    <FormField
                        control={form.control}
                        name="issuer"
                        render={({field}) => (
                            <FormItem className="flex-1">
                                <FormLabel>发布人</FormLabel>
                                <FormControl>
                                    <Input placeholder="请输入" {...field} />
                                </FormControl>
                                <FormMessage/>
                            </FormItem>
                        )}
                    />

                </div>
                <div className="flex space-x-4">
                    <FormField
                        control={form.control}
                        name="issueDate"
                        render={({field}) => (
                            <FormItem className="flex-1">
                                <FormLabel>发布时间</FormLabel>
                                <FormControl>
                                    <Input
                                        type="date"
                                        placeholder="请选择"
                                        {...field}
                                    />
                                </FormControl>
                                <FormMessage/>
                            </FormItem>
                        )}
                    />
                    <FormField
                        control={form.control}
                        name="sequence"
                        render={({field}) => (
                            <FormItem className="flex-1">
                                <FormLabel>序列</FormLabel>
                                <FormControl>
                                    <Input type="number" placeholder="请输入" {...field}
                                           onChange={(e) => field.onChange(Number(e.target.value))}/>
                                </FormControl>
                                <FormMessage/>
                            </FormItem>
                        )}
                    />
                </div>


                <div className="space-y-2">
                    <Label>banner</Label>
                    <div className="border rounded-lg p-4">
                        <Input
                            type="file"
                            onChange={handleFileUpload}
                            className="mb-2"
                            multiple
                        />
                        {imgData.attachment.id !== '' && (
                            <div className="space-y-2">
                                <div key={imgData.attachment.id} className="flex items-center gap-2 p-2 border rounded">
                                    <img
                                        className="w-15 h-15"
                                        src={imgData.attachment.url} alt=""/>
                                    <Button
                                        variant="ghost"
                                        size="sm"
                                        onClick={() => removeAttachment()}
                                    >
                                        <X className="h-4 w-4"/>
                                    </Button>
                                </div>
                            </div>
                        )}
                    </div>
                </div>

                <div className="flex justify-end space-x-4">
                    <Button type="button" variant="outline" onClick={onCancel}>
                        取消
                    </Button>
                    <Button type="submit" disabled={saveLoading || updateLoading}>
                        {isEdit ? '保存' : '创建'}
                    </Button>
                </div>
            </form>
        </Form>
    );
}
