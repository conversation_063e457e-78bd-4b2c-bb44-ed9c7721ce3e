import {ColumnDef} from "@tanstack/react-table";
import {NewsBanner} from "@/service/news/banner";
import {Badge} from "@/components/ui/badge";
import {DataTableColumnHeader} from "@/components/data-table/data-table-column-header";
import {Link} from "@@/exports";
import React from "react";

export const getColumns = ({
   actions,
}: {
    actions: any;
}): ColumnDef<NewsBanner>[] => {
    const columns: ColumnDef<NewsBanner>[] = [
        {
            accessorKey: "title",
            header: ({column}) => <DataTableColumnHeader column={column}/>,
            cell: ({row}) => (
                <div dangerouslySetInnerHTML={{__html: row.original.title}}/>
            ),
            meta: {
                title: "标题",
            },
            enableSorting: false,
            enableHiding: true,
        },
        {
            accessorKey: "newsBlockId",
            header: ({column}) => (
                <DataTableColumnHeader column={column} title="所属栏目"/>
            ),
            cell: ({row}) => {
                const newsType = (row.original.navigation.title) as string;
                return (
                    <Badge variant='default'>
                        {newsType}
                    </Badge>
                )
            },
            meta: {
                title: "所属栏目",
            }
        },
        {
            accessorKey: "issueDate",
            header: ({column}) => <DataTableColumnHeader column={column}/>,
            cell: ({row}) => (
                    <div dangerouslySetInnerHTML={{__html: row.original.issueDate}}/>
            ),
            meta: {
                title: "发布时间",
            },
            enableSorting: false,
            enableHiding: true,
        },
        {
            accessorKey: "issuer",
            header: ({column}) => <DataTableColumnHeader column={column}/>,
            cell: ({row}) => (
                    <div dangerouslySetInnerHTML={{__html: row.original.issuer}}/>
            ),
            meta: {
                title: "发布人",
            },
            enableSorting: false,
            enableHiding: true,
        },
        {
            accessorKey: "articleUrl",
            header: ({column}) => <DataTableColumnHeader column={column}/>,
            cell: ({row}) => (
                    <div dangerouslySetInnerHTML={{__html: row.original.articleUrl}}/>
            ),
            meta: {
                title: "新闻关联code",
            },
            enableSorting: false,
            enableHiding: true,
        },
        {
            accessorKey: "sequence",
            header: ({column}) => <DataTableColumnHeader column={column}/>,
            cell: ({row}) => (
                    <div dangerouslySetInnerHTML={{__html: row.original.sequence}}/>
            ),
            meta: {
                title: "序列",
            },
            enableSorting: false,
            enableHiding: true,
        },
    ]

    if (actions) {
        columns.push(actions);
    }
    return columns;
};
