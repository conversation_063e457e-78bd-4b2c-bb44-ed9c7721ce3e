import React, { useEffect } from 'react';
import {DataTableFilterField, DataTableRowAction} from "@/components/data-table/types";
import {useDataTable} from "@/components/data-table/hooks/use-data-table";
import {DataTable} from "@/components/data-table/data-table";
import {DataTableToolbar} from "@/components/data-table/data-table-toolbar";
import {getColumns} from './banner-table-columns';
import {NewsBanner} from '@/service/news/banner';
import {NavigationTreeOptions} from '@/service/news/navigation';
import {Page, PageQuery, Specification} from '@/types';
import {getFilterFields} from './banner-table-filters';
import {useAccess} from "umi";
import {getActions} from './banner-table-columns-action';

interface NewsPaperTableProps {
  onQuery: (query: PageQuery<Specification<NewsBanner>>) => Promise<Page<NewsBanner>>;
  setRowAction: React.Dispatch<React.SetStateAction<DataTableRowAction<NewsBanner> | null>>;
  data?: Page<NewsBanner>;
  selectOption?: NavigationTreeOptions[];
}

export function BannerTable({ onQuery, setRowAction, data, selectOption }: NewsPaperTableProps) {
  const access = useAccess();

  const actions = React.useMemo(
    () => getActions({ setRowAction, access }),
    [setRowAction, access]
  );

  const columns = React.useMemo(() => getColumns({ actions }), [actions]);
  const [filterFields, setFilterFields] = React.useState<DataTableFilterField<NewsBanner>[]>(getFilterFields(selectOption as NavigationTreeOptions[]));



  const { table } = useDataTable({
    data: data?.data || [],
    columns,
    pageCount: Math.ceil((data?.total || 0) / (data?.pageSize || 10)),
    filterFields,
    initialState: {
      pagination: {
        pageIndex: 0,
        pageSize: 10,
      },
      columnPinning: { right: ["actions"] },
      // columnVisibility:{
      //   'member':false,
      //   'projectCost':false,
      //   'planningCycle':false
      // }
    },
    getRowId: (originalRow) => originalRow.id!,
  });

  // 监听表格状态变化，触发查询
  React.useEffect(() => {
    setFilterFields(getFilterFields(selectOption as NavigationTreeOptions[]));
  }, [data]);

  return (
    <div className="space-y-4">
      <DataTable table={table}>
        <DataTableToolbar
            table={table}
            filterFields={filterFields}
            onFilterChange={onQuery}
        />
      </DataTable>
    </div>
  );
}
