import {DataTableFilterField, Option} from "@/components/data-table/types";
import { NavigationTreeOptions } from "@/service/news/navigation";
import {NewsBanner} from "@/service/news/banner";

export const getFilterFields = (selectOption: NavigationTreeOptions[]): DataTableFilterField<NewsBanner>[] => [
  {
    id: "title",
    label: "标题",
    placeholder: "搜索标题...",
  },
  {
    id: "navigationId",
    label: "栏目",
    placeholder: "栏目",
    agg: true,
    virtual: true,
    options: selectOption?.map(item => {
      return {label: item.title, value: item.id} as Option
    }) || []
  },
];
