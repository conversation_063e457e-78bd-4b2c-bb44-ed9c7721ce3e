import React, {useState, useEffect, Component} from 'react';
import {But<PERSON>} from "@/components/ui/button";
import {Plus} from "lucide-react";
import {useRequest, useAccess} from "umi";
import {NewsBlock, deleteBlock, fetchBlockPage} from "@/service/news/block";
import {BlockTable} from './components/block-table';
import {Page, PageQuery, Specification} from '@/types';
import {DataTableRowAction} from "@/components/data-table/types";
import {Sheet, SheetContent, SheetHeader, SheetTitle} from "@/components/ui/sheet";
import {BlockForm} from './components/block-form';
import {fetchNavigationTree, NavigationTreeOptions} from "@/service/news/navigation";
import {BlockNavigationList} from "@/pages/news/block/components/block-navigation-list";
import {parseAsString, useQueryState} from "nuqs";

export default function BlockPage() {
    const [pageData, setPageData] = useState<Page<NewsBlock>>();
    const [isFormOpen, setIsFormOpen] = useState(false);
    const [editingItem, setEditingItem] = useState<NewsBlock>();
    const [navigationOptions, setNavigationOptions] = useState<NavigationTreeOptions[]>([]);
    const access = useAccess();

    const [navigationId] = useQueryState(
        "navigationId",
        parseAsString,
    );

    // 检查用户是否有新增权限
    const canEdit = access.hasAction && access.hasAction('NEWS:BLOCK:SAVE');

    const {run: deleteItem} = useRequest(deleteBlock, {
        manual: true,
        onSuccess: () => {
            // 刷新数据
            handleQuery({
                pageSize: 10,
                page: 0,
                specification: {
                    navigationId: navigationId
                }
            });
        },
    });

    // 获取栏目数据选择列表
    const handleNavigationOption = async () => {
        const res = await fetchNavigationTree()
        setNavigationOptions(res.data)
        return res.data;
    }

    const handleQuery = async (query: PageQuery<Specification<NewsBlock>>) => {
        const response = await fetchBlockPage(query);
        setPageData(response.data);
        return response.data;
    };

    const [rowAction, setRowActionState] = useState<DataTableRowAction<NewsBlock> | null>(null);

    // 监听rowAction状态变化，触发相应处理
    useEffect(() => {
        handleNavigationOption();
        if (rowAction) {
            handleRowAction(rowAction);
        }
    }, [rowAction]);

    const handleRowAction = (action: DataTableRowAction<NewsBlock> | null) => {
        // 如果为空，直接返回
        if (!action) return;
        // 将row的原始数据转换为完整的CasesNavigation对象
        const rowData = action.row.original as NewsBlock;
        switch (action.type) {
            case "delete":
                deleteItem(rowData.id!);
                break;
            case "update":
                setEditingItem(rowData);
                setIsFormOpen(true);
                break;
        }
    };

    const handleSuccess = () => {
        setIsFormOpen(false);
        setEditingItem(undefined);
        // 刷新数据
        handleQuery({
            pageSize: 10,
            page: 0,
            specification: {
                navigationId: navigationId
            }
        });
    };

    const handleAdd = () => {
        setEditingItem(undefined);
        setIsFormOpen(true);
    };

    return (
        <div className="flex h-full">
            {/* 左侧分类树 */}
            <BlockNavigationList
                data={navigationOptions}
            />

            {/* 右侧表格 */}
            <div className="flex-1 p-4 overflow-auto">
                <div className="flex items-center justify-between mb-6">
                    <h1 className="text-2xl font-bold">模块管理</h1>
                    {canEdit && (
                        <Button onClick={handleAdd}>
                            <Plus className="mr-2 h-4 w-4"/>
                            新增模块
                        </Button>
                    )}
                </div>

                <div className="w-full">
                    <BlockTable
                        onQuery={handleQuery}
                        setRowAction={setRowActionState}
                        data={pageData}
                        navigationOption={navigationOptions}
                    />
                </div>

                <Sheet open={isFormOpen} onOpenChange={setIsFormOpen}>
                    <SheetContent className="w-[900px] sm:max-w-[900px]">
                        <SheetHeader>
                            <SheetTitle>{editingItem ? '编辑模块' : '新增模块'}</SheetTitle>
                        </SheetHeader>
                        <div className="mt-6 p-4">
                            <BlockForm
                                initialData={editingItem}
                                navigationOption={navigationOptions}
                                onSuccess={handleSuccess}
                                onCancel={() => setIsFormOpen(false)}
                            />
                        </div>
                    </SheetContent>
                </Sheet>
            </div>
        </div>
    );
}
