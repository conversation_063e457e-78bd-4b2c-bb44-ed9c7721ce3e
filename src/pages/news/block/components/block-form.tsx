import React, {useState} from 'react';
import {use<PERSON>orm, SubmitHandler} from 'react-hook-form';
import {zodResolver} from '@hookform/resolvers/zod';
import * as z from 'zod';
import {Button} from '@/components/ui/button';
import {Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage} from '@/components/ui/form';
import {Input} from '@/components/ui/input';
import {Select, SelectContent, SelectItem, SelectTrigger, SelectValue} from '@/components/ui/select';
import {Textarea} from '@/components/ui/textarea';
import {NewsBlock, saveBlock} from '@/service/news/block';
import {useRequest} from 'umi';
import {NavigationTreeOptions} from "@/service/news/navigation";
import {Label} from "@/components/ui/label";
import {X} from "lucide-react";
import {uploadFileInfo} from "@/service/storage";
import {toast} from "sonner";

const formSchema = z.object({
    id: z.string().optional(),
    title: z.string().min(1, '请输入模块目名称'),
    code: z.string().min(1, '请输入编码'),
    navigationId: z.string().min(1, '请选择栏目'),
    remark: z.string().optional(),
    sequence: z.number().default(0),
    status: z.enum(['ENABLED', 'DISABLED']).default('ENABLED'),
    navigation: z.any().optional(),
    image: z.any().optional(),
});

type BlockFormValues = z.infer<typeof formSchema>;

interface BlockFormProps {
    initialData?: NewsBlock;
    navigationOption?: NavigationTreeOptions[];
    onSuccess?: () => void;
    onCancel?: () => void;
}

interface ImgState {
    attachment: {
        id: string,
        fileName: string;
        url: string;
    }
}

export function BlockForm({initialData, navigationOption, onSuccess, onCancel}: BlockFormProps) {
    const [imgData, setImgData] = useState<ImgState>({
        attachment: {
            id: initialData?.image?.id || '',
            fileName: initialData?.image?.originalFilename || '',
            url: initialData?.image?.url || ''
        }
    })
    const isEdit = !!initialData?.id;
    const defaultValues: BlockFormValues = {
        title: initialData?.title || '',
        code: initialData?.code || '',
        remark: initialData?.remark || '',
        sequence: initialData?.sequence || 0,
        status: (initialData?.status as 'ENABLED' | 'DISABLED') || 'ENABLED',
        navigationId: initialData?.navigation?.id || '',
    };

    const form = useForm<BlockFormValues>({
        resolver: zodResolver(formSchema),
        defaultValues,
    });

    const {loading: saveLoading, run: runSave} = useRequest(saveBlock, {
        manual: true,
        onSuccess: () => {
            onSuccess?.();
        },
    });

    const {loading: updateLoading, run: runUpdate} = useRequest(saveBlock, {
        manual: true,
        onSuccess: () => {
            onSuccess?.();
        },
    });

    // 处理文件上传
    const handleFileUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
        const files = Array.from(e.target.files || []);
        for (const file of files) {
            if (!file.type.includes("image")) {
                toast.error('请上传图片类型的文件！');
                return;
            }
            const formData = new FormData();
            formData.append('file', file);
            const res = await uploadFileInfo(formData);
            if (res?.data) {
                setImgData({
                    attachment: {
                        id: res.data.id,
                        fileName: file.name,
                        url: res.data.url,
                    }
                });
            }
        }
    };

    // 移除附件
    const removeAttachment = () => {
        setImgData(() => ({
            attachment: {
                id: '',
                fileName: '',
                url: ''
            },
        }));
    };

    const onSubmit: SubmitHandler<BlockFormValues> = (data) => {
        if (isEdit && initialData?.id) {
            runUpdate({
                ...data,
                id: initialData.id,
                imageId: imgData.attachment?.id
            });
        } else {
            const formData = {
                ...data,
                imageId: imgData.attachment?.id

            };
            runSave(formData);
        }
    };

    return (
        <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                <div className="flex space-x-4">
                    <FormField
                        control={form.control}
                        name="navigationId"
                        render={({field}) => (
                            <FormItem className="flex-1">
                                <FormLabel>栏目</FormLabel>
                                <Select onValueChange={field.onChange} defaultValue={field.value}>
                                    <FormControl>
                                        <SelectTrigger className="w-[425.6px]">
                                            <SelectValue placeholder="请选择栏目"/>
                                        </SelectTrigger>
                                    </FormControl>
                                    <SelectContent>
                                        {navigationOption?.map((item) => (
                                            <SelectItem key={item.id} value={item.id}>{item.title}</SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                                <FormMessage/>
                            </FormItem>
                        )}
                    />
                    <FormField
                        control={form.control}
                        name="title"
                        render={({field}) => (
                            <FormItem className="flex-1">
                                <FormLabel>模块名称</FormLabel>
                                <FormControl>
                                    <Input placeholder="请输入模块名称" {...field} />
                                </FormControl>
                                <FormMessage/>
                            </FormItem>
                        )}
                    />
                </div>

                <div className="flex space-x-4">
                    <FormField
                        control={form.control}
                        name="code"
                        render={({field}) => (
                            <FormItem className="flex-1">
                                <FormLabel>编码</FormLabel>
                                <FormControl>
                                    <Input placeholder="请输入编码" {...field} />
                                </FormControl>
                                <FormMessage/>
                            </FormItem>
                        )}
                    />
                    <FormField
                        control={form.control}
                        name="status"
                        render={({field}) => (
                            <FormItem className="flex-1">
                                <FormLabel>状态</FormLabel>
                                <Select onValueChange={field.onChange} defaultValue={field.value}>
                                    <FormControl>
                                        <SelectTrigger className="w-[100%]">
                                            <SelectValue placeholder="请选择状态"/>
                                        </SelectTrigger>
                                    </FormControl>
                                    <SelectContent>
                                        <SelectItem value="ENABLED">启用</SelectItem>
                                        <SelectItem value="DISABLED">禁用</SelectItem>
                                    </SelectContent>
                                </Select>
                                <FormMessage/>
                            </FormItem>
                        )}
                    />
                </div>

                <div className="space-y-2">
                    <Label>图片</Label>
                    <div className="border rounded-lg p-4">
                        <Input
                            type="file"
                            onChange={handleFileUpload}
                            className="mb-2"
                            multiple
                        />
                        {imgData.attachment.id !== '' && (
                            <div className="space-y-2">
                                <div key={imgData.attachment.id} className="flex items-center gap-2 p-2 border rounded">
                                    <img
                                        className="w-15 h-15"
                                        src={imgData.attachment.url} alt="" />
                                    <Button
                                        variant="ghost"
                                        size="sm"
                                        onClick={() => removeAttachment()}
                                    >
                                        <X className="h-4 w-4" />
                                    </Button>
                                </div>
                            </div>
                        )}
                    </div>
                </div>

                <FormField
                    control={form.control}
                    name="sequence"
                    render={({field}) => (
                        <FormItem>
                            <FormLabel>排序</FormLabel>
                            <FormControl>
                                <Input
                                    type="number"
                                    placeholder="请输入排序号"
                                    {...field} onChange={(e) => field.onChange(Number(e.target.value))}
                                />
                            </FormControl>
                            <FormMessage/>
                        </FormItem>
                    )}
                />

                <FormField
                    control={form.control}
                    name="remark"
                    render={({field}) => (
                        <FormItem>
                            <FormLabel>说明</FormLabel>
                            <FormControl>
                                <Textarea placeholder="请输入说明" {...field} />
                            </FormControl>
                            <FormMessage/>
                        </FormItem>
                    )}
                />

                <div className="flex justify-end space-x-4">
                    <Button type="button" variant="outline" onClick={onCancel}>
                        取消
                    </Button>
                    <Button type="submit" disabled={saveLoading || updateLoading}>
                        {isEdit ? '保存' : '创建'}
                    </Button>
                </div>
            </form>
        </Form>
    );
}
