import {ColumnDef} from "@tanstack/react-table";
import {NewsBlock} from "@/service/news/block";
import {Badge} from "@/components/ui/badge";
import {format} from "date-fns";
import {DataTableColumnHeader} from "@/components/data-table/data-table-column-header";
import {Link} from "@@/exports";
import React from "react";

export const getColumns = ({
   actions,
}: {
    actions: any;
}): ColumnDef<NewsBlock>[] => {
    const columns: ColumnDef<NewsBlock>[] = [
        {
            accessorKey: "title",
            header: ({column}) => (
                <DataTableColumnHeader column={column} title="模块名称"/>
            ),
            cell: ({row}) => {
                return (
                    <Link to={`/news/resource-manager?newsBlockId=${row.original.id}`}>
                        <div className="flex space-x-2">
                            <span className="max-w-[200px] truncate font-medium">
                                {row.getValue("title")}
                            </span>
                        </div>
                    </Link>
                );
            },
            meta: {
                title: "模块名称",
            },
            enableSorting: false,
            enableHiding: true
        },
        {
            accessorKey: "code",
            header: ({column}) => (
                <DataTableColumnHeader column={column} title="编码"/>
            ),
            cell: ({row}) => {
                return (
                    <div className="flex space-x-2">
                        <span className="max-w-[100px] truncate">
                            {row.getValue("code")}
                        </span>
                    </div>
                );
            },
            meta: {
                title: "编码",
            },
            enableSorting: false,
            enableHiding: true
        },
        {
            accessorKey: "status",
            header: ({column}) => (
                <DataTableColumnHeader column={column} title="状态"/>
            ),
            cell: ({row}) => {
                const status = row.getValue("status");
                return (
                    <Badge variant={status === 'ENABLED' ? 'default' : 'secondary'}>
                        {status === 'ENABLED' ? '启用' : '禁用'}
                    </Badge>
                )
            },
            meta: {
                title: "状态",
            },
            filterFn: (row, id, value) => {
                return value.includes(row.getValue(id));
            },
        },
        {
            accessorKey: "sequence",
            header: ({column}) => (
                <DataTableColumnHeader column={column} title="序列"/>
            ),
            cell: ({row}) => {
                return (
                    <div className="flex space-x-2">
                        <span className="max-w-[100px] truncate">
                            {row.getValue("sequence")}
                        </span>
                    </div>
                );
            },
            meta: {
                title: "序列",
            },
            enableSorting: true
        },
    ]

    if (actions) {
        columns.push(actions);
    }
    return columns;
}
