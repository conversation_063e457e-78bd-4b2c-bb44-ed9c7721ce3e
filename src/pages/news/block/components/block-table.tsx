import React, { useEffect } from 'react';
import {DataTableFilterField, DataTableRowAction} from "@/components/data-table/types";
import {useDataTable} from "@/components/data-table/hooks/use-data-table";
import {DataTable} from "@/components/data-table/data-table";
import {DataTableToolbar} from "@/components/data-table/data-table-toolbar";
import {getColumns} from './block-table-columns';
import {NewsBlock} from '@/service/news/block';
import {Page, PageQuery, Specification} from '@/types';
import {getFilterFields} from './block-table-filters';
import {useAccess} from "umi";
import {getActions} from './block-table-columns-action';
import {NavigationTreeOptions} from "@/service/news/navigation";

interface BlockTableProps {
  onQuery: (query: PageQuery<Specification<NewsBlock>>) => Promise<Page<NewsBlock>>;
  setRowAction: React.Dispatch<React.SetStateAction<DataTableRowAction<NewsBlock> | null>>;
  data?: Page<NewsBlock>;
  navigationOption?: NavigationTreeOptions[];
}

export function BlockTable({ onQuery, setRowAction, data, navigationOption }: BlockTableProps) {
  const access = useAccess();

  const actions = React.useMemo(
    () => getActions({ setRowAction, access }),
    [setRowAction, access]
  );

  const columns = React.useMemo(() => getColumns({ actions }), [actions]);
  const [filterFields, setFilterFields] = React.useState<DataTableFilterField<NewsBlock>[]>(getFilterFields(navigationOption || []));



  const { table } = useDataTable({
    data: data?.data || [],
    columns,
    pageCount: Math.ceil((data?.total || 0) / (data?.pageSize || 10)),
    filterFields,
    initialState: {
      pagination: {
        pageIndex: 0,
        pageSize: 10,
      },
      columnPinning: { right: ["actions"] },
    },
    getRowId: (originalRow) => originalRow.id!,
  });

  React.useEffect(() => {
    setFilterFields(getFilterFields(navigationOption || []));
  }, [data]);

  return (
    <div className="space-y-4">
      <DataTableToolbar table={table} filterFields={filterFields} onFilterChange={onQuery} />
      <DataTable table={table} />
    </div>
  );
}
