import {DataTableFilterField, Option} from "@/components/data-table/types";
import {NavigationTreeOptions} from "@/service/news/navigation";
import {NewsBlock} from "@/service/news/block";

export function getFilterFields(NavigationOptions : NavigationTreeOptions[]): DataTableFilterField<NewsBlock>[] {
    return [
        {
            id: "title",
            label: "模块名称",
            placeholder: "输入模块名称...",
        },
        // {
        //     id: "columnId",
        //     label: "栏目",
        //     placeholder: "栏目",
        //     agg: false,
        //     options: NavigationOptions?.map(item => {
        //         return {label: item.title, value: item.id} as Option
        //     }) || []
        // },
        {
            id:'navigationId',
            label:'',
            placeholder:'',
            virtual: true,
            hidden:true,
        },
    ]
}
