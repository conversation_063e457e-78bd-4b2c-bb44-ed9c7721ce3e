import React, {Dispatch, SetStateAction, useEffect, useMemo, useState} from 'react';
import { Search } from "lucide-react";
import { Input } from "@/components/ui/input";
import { ScrollArea } from "@/components/ui/scroll-area";
import {parseAsString, useQueryState} from "nuqs";
import {NavigationTreeOptions} from "@/service/news/navigation";

interface BlockTreeProps {
    data: NavigationTreeOptions[];
}

export function BlockNavigationList({ data }: BlockTreeProps) {
    const [searchKeyword, setSearchKeyword] = useState('');
    const [columnData, setColumnData] = useState<NavigationTreeOptions[]>(data)

    useEffect(() => {
        if (searchKeyword == null || searchKeyword.length==0){
            setColumnData(data);
        }else {
            setColumnData(data.filter(stage=>stage.title.includes(searchKeyword)))
        }

    }, [searchKeyword,data]);

    const [navigationId, setNavigationId] = useQueryState(
        "navigationId",
        parseAsString,
    );

    const selectStageLabel = (id: string)=> {
        setNavigationId(id);
    }

    // 高亮显示匹配的文本
    const highlightText = (text: string) => {
        if (!searchKeyword) return text;

        const parts = text.split(new RegExp(`(${searchKeyword})`, 'gi'));
        return (
            <>
                {parts.map((part, i) =>
                    part.toLowerCase() === searchKeyword.toLowerCase() ? (
                        <span key={i} className="bg-yellow-200 text-black rounded px-0.5">{part}</span>
                    ) : (
                        part
                    )
                )}
            </>
        );
    };

    return (
        <div className="w-[300px] border-r flex flex-col h-full">
            <div className="p-4">
                <div className="relative mb-4">
                    <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                    <Input
                        placeholder="搜索栏目..."
                        value={searchKeyword}
                        onChange={(e) => setSearchKeyword(e.target.value)}
                        className="pl-8"
                    />
                </div>
                <ScrollArea className="h-[calc(100vh-180px)]">
                    <div className="border rounded-lg">
                        {columnData.map(column => {
                            return (
                                <div
                                    key={column.id}
                                    className={`flex items-center py-3 px-4 hover:bg-accent cursor-pointer  ${navigationId as string  === column.id ? 'bg-accent' : ''}`}
                                    onClick={() => selectStageLabel(column.id as string)}
                                >
                                    <div className="flex-1">
                                        <div className="font-medium">
                                            {highlightText(column.title)}
                                        </div>
                                        {/*{stage.code && (*/}
                                        {/*    <div className="text-xs text-muted-foreground mt-1">*/}
                                        {/*        {highlightText(stage.code)}*/}
                                        {/*    </div>*/}
                                        {/*)}*/}
                                    </div>
                                </div>
                            );
                        })}

                        {data.length === 0 && (
                            <div className="py-6 text-center text-muted-foreground">
                                未找到匹配的栏目
                            </div>
                        )}
                    </div>
                </ScrollArea>
            </div>
        </div>
    );
}