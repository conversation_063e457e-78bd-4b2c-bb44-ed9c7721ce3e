import {Access, AccessInstance} from "@@/exports";
import {NewsResourceManager} from "@/service/news/resource-manager";
import {ColumnDef} from "@tanstack/react-table";
import {Button} from "@/components/ui/button";
import {Link, Pencil, Trash} from "lucide-react";
import React from "react";
import type {DataTableRowAction} from "@/components/data-table/types";

export interface CasesTableColumnProps {
    setRowAction: React.Dispatch<React.SetStateAction<DataTableRowAction<NewsResourceManager> | null>>;
    access:AccessInstance
}

export function getActions({setRowAction,access}:CasesTableColumnProps):ColumnDef<NewsResourceManager> |undefined {
    const {hasAction} = access
    if (hasAction('NEWS:RESOURCE')) {
        return {
            id: "actions",
            cell: ({row}) => {
                return (
                    <div className="flex items-center gap-2">
                        <Access accessible={hasAction("NEWS:RESOURCE:EDIT")}>
                            <Button
                                variant="ghost"
                                size="icon"
                                className="h-8 w-8"
                                onClick={() =>
                                    setRowAction({
                                        type: "update",
                                        row: row,
                                    })
                                }
                            >
                                <Pencil className="h-4 w-4"/>
                                <span className="sr-only">编辑</span>
                            </Button>
                        </Access>
                        {row.original.newsType != "OA" && (<Access accessible={hasAction("NEWS:RESOURCE:DELETE")}>
                            <Button
                                variant="ghost"
                                size="icon"
                                className="h-8 w-8"
                                onClick={() =>
                                    setRowAction({
                                        type: "delete",
                                        row: row,
                                    })
                                }
                            >
                                <Trash className="h-4 w-4"/>
                                <span className="sr-only">删除</span>
                            </Button>
                        </Access>)}
                        <Access accessible={hasAction("NEWS:RESOURCE:EDIT")}>
                            <Button
                                variant="ghost"
                                size="icon"
                                className="h-8 w-8"
                                onClick={() =>
                                    setRowAction({
                                        type: "url",
                                        row: row,
                                    })
                                }
                            >
                                <Link className="h-4 w-4"/>
                                <span className="sr-only">生成banner关联</span>
                            </Button>
                        </Access>
                    </div>
                );
            },
        }
    }
}