import React from "react";
import {ColumnDef} from "@tanstack/react-table";
import {DataTableColumnHeader} from "@/components/data-table/data-table-column-header";
import {NewsResourceManager} from "@/service/news/resource-manager";
import {Badge} from "@/components/ui/badge";

export interface ResourceManagerTableColumnProps {
   actions: ColumnDef<NewsResourceManager> | undefined;
}

export function getColumns({actions}: ResourceManagerTableColumnProps): ColumnDef<NewsResourceManager>[] {
    const labelMap: Record<string, string> = {
        'OA': 'OA平台',
        'PLATFORM': '新闻平台'
    };
    const  columns: ColumnDef<NewsResourceManager>[] = [
        {
            accessorKey: "title",
            header: ({column}) => <DataTableColumnHeader column={column}/>,
            cell: ({row}) => (
                <div className={"flex max-w-[150px] truncate"}>
                    <div className={'pl-1'} dangerouslySetInnerHTML={{__html: row.original.title}}/>
                </div>
            ),
            meta: {
                title: "标题",
            },
            enableSorting: false
        },
        {
            accessorKey: "contentBrief",
            header: ({column}) => <DataTableColumnHeader column={column}/>,
            cell: ({row}) => (
                <div className="flex space-x-2 max-w-[150px] truncate">
                    <div className={'pl-1'} dangerouslySetInnerHTML={{__html: row.original.contentBrief}}/>
                </div>
            ),
            meta: {
                title: "内容摘要",
            },
            enableSorting: false
        },
        {
            accessorKey: "author",
            header: ({column}) => <DataTableColumnHeader column={column}/>,
            cell: ({row}) => (
                <div className={"flex"}>
                    <div className={'pl-1'} dangerouslySetInnerHTML={{__html: row.original.author}}/>
                </div>
            ),
            meta: {
                title: "作者",
            },
            enableSorting: false
        },
        {
            accessorKey: "issueDept",
            header: ({column}) => <DataTableColumnHeader column={column}/>,
            cell: ({row}) => (
                <div className={"flex"}>
                    <div className={'pl-1'} dangerouslySetInnerHTML={{__html: row.original.issueDept}}/>
                </div>
            ),
            meta: {
                title: "所属部门",
            },
            enableSorting: false
        },
        {
            accessorKey: "issueTime",
            header: ({column}) => <DataTableColumnHeader column={column}/>,
            cell: ({row}) => (
                <div className={"flex"}>
                    <div className={'pl-1'} dangerouslySetInnerHTML={{__html: row.original.issueTime}}/>
                </div>
            ),
            meta: {
                title: "发布时间",
            },
        },
        {
            accessorKey: "issuer",
            header: ({column}) => <DataTableColumnHeader column={column}/>,
            cell: ({row}) => (
                <div className={"flex"}>
                    <div className={'pl-1'} dangerouslySetInnerHTML={{__html: row.original.issuer}}/>
                </div>
            ),
            meta: {
                title: "发布人",
            },
            enableSorting: false
        },
        {
            accessorKey: "readingNum",
            header: ({column}) => <DataTableColumnHeader column={column}/>,
            cell: ({row}) => (
                <div className={"flex"}>
                    <div className={'pl-1'} dangerouslySetInnerHTML={{__html: row.original.readingNum}}/>
                </div>
            ),
            meta: {
                title: "阅读量",
            },
        },
        {
            accessorKey: "tags",
            header: ({column}) => <DataTableColumnHeader column={column}/>,
            cell: ({row}) => {
                const tags = row.getValue("tags") as string[];
                return (
                    <div className="flex gap-1 flex-wrap">
                        <Badge>
                            {tags}
                        </Badge>
                    </div>
                );
            },
            meta: {
                title: "标签",
            },
            enableSorting: false,
            enableHiding: true,
        },
        {
            accessorKey: "topStatus",
            header: ({column}) => (
                <DataTableColumnHeader column={column} title="状态"/>
            ),
            cell: ({row}) => {
                const topStatus = row.getValue("topStatus");
                return (
                    <Badge variant={topStatus === 'true' ? 'default' : 'secondary'}>
                        {topStatus === 'true' ? '是' : '否'}
                    </Badge>
                )
            },
            meta: {
                title: "置顶",
            },
            filterFn: (row, id, value) => {
                return value.includes(row.getValue(id));
            },
        },
        {
            accessorKey: "newsType",
            header: ({column}) => (
                <DataTableColumnHeader column={column} title="状态"/>
            ),
            cell: ({row}) => {
                const newsType = row.getValue("newsType") as string;
                return (
                    <Badge variant='default'>
                        {labelMap[newsType] ?? '未知类型'}
                    </Badge>
                )
            },
            meta: {
                title: "新闻类型",
            },
            filterFn: (row, id, value) => {
                return value.includes(row.getValue(id));
            },
        }
    ];
    if (actions){
        columns.push(actions);
    }
    return columns;
} 