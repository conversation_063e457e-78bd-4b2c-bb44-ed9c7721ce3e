import React from 'react';
import {DataTableFilterField, DataTableRowAction} from "@/components/data-table/types";
import {useDataTable} from "@/components/data-table/hooks/use-data-table";
import {DataTable} from "@/components/data-table/data-table";
import {DataTableToolbar} from "@/components/data-table/data-table-toolbar";
import {getColumns} from '@/pages/news/resource-manager/components/resource-manager-table-columns';
import {NewsResourceManager} from '@/service/news/resource-manager';
import {SelectOptions} from '@/service/news/navigation';
import {Page, PageQuery, Specification} from '@/types';
import {getFilterFields} from './resource-manager-table-filters';
import {getActions} from "@/pages/news/resource-manager/components/resource-manager-table-columns-action";
import {useAccess} from "umi";

interface ResourceManagerTableProps {
    onQuery: (query: PageQuery<Specification<NewsResourceManager>>) => Promise<Page<NewsResourceManager>>;
    setRowAction: React.Dispatch<React.SetStateAction<DataTableRowAction<NewsResourceManager> | null>>;
    data?: Page<NewsResourceManager>;
}

export function ResourceManagerTable({onQuery, setRowAction, data}: ResourceManagerTableProps) {
    const access = useAccess();

    const actions = React.useMemo(() => getActions({setRowAction, access}), [setRowAction, access]);
    const columns = React.useMemo(() => getColumns({actions}), [actions]);

    const [filterFields, setFilterFields] = React.useState<DataTableFilterField<NewsResourceManager>[]>(getFilterFields());

    const {table} = useDataTable({
        data: data?.data || [],
        columns,
        pageCount: Math.ceil((data?.total || 0) / (data?.pageSize || 10)),
        filterFields,
        initialState: {
            pagination: {
                pageIndex: 0,
                pageSize: 30
            },
            sorting: [{id: 'issueTime', desc: true}],
            columnPinning: {right: ["actions"]},
        },
        // getRowId: (originalRow) => originalRow.id,
        clearOnDefault: false,
        shallow: true
    });

    React.useEffect(() => {
        setFilterFields(getFilterFields());
    }, [data]);

    const handleFilterChange = (pageQuery: PageQuery<Specification<NewsResourceManager>>) => {
        onQuery(pageQuery);
    };

    return (
        <>
            <DataTable table={table} className="h-fit">
                <DataTableToolbar
                    type={'sql'}
                    onFilterChange={handleFilterChange}
                    table={table}
                    filterFields={filterFields}
                />
            </DataTable>
        </>
    );
} 