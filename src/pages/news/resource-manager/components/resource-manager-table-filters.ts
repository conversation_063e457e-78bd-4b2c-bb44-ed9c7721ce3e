import {DataTableFilterField, Option} from "@/components/data-table/types";
import {NewsResourceManager} from "@/service/news/resource-manager";

export function getFilterFields(): DataTableFilterField<NewsResourceManager>[] {
    return [
        {
            id: "title",
            label: "标题",
            placeholder: "搜索标题...",
            highlight: true,
        },
        {
            id: "author",
            label: "作者",
            placeholder: "搜索作者...",
            highlight: true,
        }, {
            id: "newsType",
            label: "新闻类型",
            placeholder: "新闻类型",
            agg: true,
            options: [
                {label: 'OA平台', value: 'OA'},
                {label: '新闻平台', value: 'PLATFORM'}
            ]
        },
        {
            id: "newsBlockId",
            label: "模块id",
            virtual: true,
            hidden: true,
            highlight: true,
        },
        {
            id: "newspaperBoardId",
            label: "报刊板块id",
            virtual: true,
            hidden: true,
            highlight: true,
        }
    ]
} 