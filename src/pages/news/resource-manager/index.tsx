import React, {useEffect, useState} from 'react';
import {Button} from "@/components/ui/button";
import {Plus} from "lucide-react";
import {Access, history, useAccess, useRequest} from "umi";
import {
    fetchResourceManagerPage,
    NewsResourceManager,
    deleteResourceManager
} from "@/service/news/resource-manager";
import {ResourceManagerTable} from '@/pages/news/resource-manager/components/resource-manager-table';
import {DataTableRowAction} from "@/components/data-table/types";
import {Page, PageQuery, Specification} from '@/types';
import {toast} from "sonner";

export default function CasesPage() {
    const [rowAction, setRowAction] = useState<DataTableRowAction<NewsResourceManager> | null>(null);
    const [pageData, setPageData] = useState<Page<NewsResourceManager>>();
    const access = useAccess();

    const {run: fetchCasesRun} = useRequest(fetchResourceManagerPage, {
        manual: true
    });

    // 跳转到新增页面
    const handleAdd = () => {
        history.push(`/news/resource-manager/new`);
    };

    const goBack = () => {
        history.back()
    }
    const {run: deleteItem} = useRequest(deleteResourceManager, {
        manual: true,
        onSuccess: () => {
            // 刷新数据
            handleQuery({
                pageSize: 10,
                page: 0,
                sort: [
                    {field: 'issueTime', order: 'desc'}
                ],
                specification: {}
            });
        },
    });

    // 处理表格操作
    useEffect(() => {
        // 如果为空，直接返回
        if (!rowAction) return;
        // 将row的原始数据转换为完整的CasesNavigation对象
        const rowData = rowAction.row.original;
        switch (rowAction?.type) {
            case "update":
                history.push(`/news/resource-manager/edit/${rowData.id}`);
                break
            case "delete":
                deleteItem(rowData.id!)
                break
            case "url":
                copyTextFallback(rowData.id!)
        }
    }, [rowAction]);

    function copyTextFallback(text: string) {
        const textarea = document.createElement("textarea");
        textarea.value = text;
        textarea.setAttribute("readonly", "true");
        textarea.style.position = "absolute";
        textarea.style.left = "-9999px";
        document.body.appendChild(textarea);
        textarea.select();

        try {
            const result = document.execCommand("copy");
            if (result) {
                toast.success("复制成功");
            } else {
                toast.error("复制失败，请手动复制：" + text);
            }
        } catch (err) {
            toast.error("复制失败：" + text);
        }

        document.body.removeChild(textarea);
    }

    // 处理查询操作
    const handleQuery = (query: PageQuery<Specification<NewsResourceManager>>) => {
        return fetchCasesRun(query).then(res => {
            setPageData(res);
            return res;
        });
    };

    return (
        <div className="flex-1 p-4 overflow-auto">
            <div className="flex justify-between items-center mb-4">
                <h1 className="text-2xl font-bold">新闻管理</h1>
                <Access accessible={access.hasAction("NEWS:RESOURCE:SAVE")}>
                    <div className="flex space-x-2">
                        <Button onClick={handleAdd}>
                            <Plus className="h-4 w-4 mr-2"/>
                            新建新闻
                        </Button>
                    </div>
                </Access>
            </div>
            <ResourceManagerTable
                onQuery={handleQuery}
                setRowAction={setRowAction}
                data={pageData}
            />
        </div>
    );
}