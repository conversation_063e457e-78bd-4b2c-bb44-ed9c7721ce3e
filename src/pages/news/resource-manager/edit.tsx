import React, {useState} from 'react';
import {Button} from "@/components/ui/button";
import {Card} from "@/components/ui/card";
import {Input} from "@/components/ui/input";
import {Label} from "@/components/ui/label";
import {history, useParams, useRequest} from "umi";
import {
    getResourceManagerDetail,
    NewsResourceManagerSave,
    saveResourceManager
} from "@/service/news/resource-manager";
import {fetchNavigationTree, NavigationTreeOptions} from "@/service/news/navigation"
import {uploadFileInfo, uploadFile} from '@/service/storage';
import {Badge} from '@/components/ui/badge';
import {ChevronDown, ChevronRight, Search, X} from 'lucide-react';
import {toast} from 'sonner';
import {Textarea} from "@/components/ui/textarea";
import {Select, SelectContent, SelectItem, SelectTrigger, SelectValue} from "@/components/ui/select";
import {
    DropdownMenu,
    DropdownMenuTrigger,
    DropdownMenuContent,
    DropdownMenuCheckboxItem,
    DropdownMenuLabel,
    DropdownMenuSub,
    DropdownMenuSubTrigger,
    DropdownMenuSubContent,
} from "@/components/ui/dropdown-menu"
import {fetchNewspaperTree, NewspaperTreeOptions} from "@/service/news/newspaper";
import {getUserInfo} from "@/service/account";
import { NotionEditor } from '@/components/editor';
import {fetchOrgFilter, OrgTree} from "@/service/org";
import {format} from 'date-fns';
import {Popover, PopoverContent, PopoverTrigger} from "@/components/ui/popover";
import {cn} from "@/lib/utils";


import {fetchOrgTree, Org} from "@/service/org";

interface FormState {
    title: string;
    author: string;
    issueDept: string;
    issueTime: string;
    issuer: string;
    issueUnit: string;
    contentBrief: string;
    contentType: "FILE" | "CONTENT";
    content: string;
    contentFileId: string;
    newsType: "PLATFORM" | "OA";
    tags: string[];
    status: "ENABLED" | "DISABLED";
    id?: string;
    topStatus: string;
    readingNum: number;
    attachments: Array<{
        fileId: string;
        url: string;
    }>;
    contentFile: {
        id: string,
        fileName: string;
    }
}

const OrgTreeItem: React.FC<{
    item: { id: string; label: string; children?: { id: string; label: string }[] };
    level: number;
    selectedId: string | null;
    onSelect: (id: string | null) => void;
    searchValue: string;
}> = ({item, level, selectedId, onSelect, searchValue}) => {
    const [isOpen, setIsOpen] = useState(false);
    const hasChildren = item.children && item.children.length > 0;
    const isMatch = item.label.toLowerCase().includes(searchValue.toLowerCase());
    const hasMatchingChildren = hasChildren && item.children?.some(child =>
        child.label.toLowerCase().includes(searchValue.toLowerCase())
    );

    // 如果有匹配的子项，自动展开
    React.useEffect(() => {
        if (hasMatchingChildren && !isOpen && searchValue != "") {
            setIsOpen(true);
        }
    }, [hasMatchingChildren, isOpen]);

    // 如果没有匹配项且没有匹配的子项，不显示
    if (!isMatch && !hasMatchingChildren) {
        return null;
    }

    return (
        <>
            <div
                onClick={() => {
                    onSelect(item.id);
                    if (hasChildren) {
                        setIsOpen(!isOpen);
                    }
                }}
                className={cn(
                    "cursor-pointer flex items-center px-2 py-1.5 hover:bg-accent rounded-sm",
                    level > 0 && "ml-4",
                    selectedId === item.id && "bg-accent"
                )}
                style={{paddingLeft: `${level * 16}px`}}
            >
                {hasChildren ? (
                    <Button
                        variant="ghost"
                        size="sm"
                        className="h-6 w-6 p-0 mr-2"
                        onClick={(e) => {
                            e.stopPropagation();
                            setIsOpen(!isOpen);
                        }}
                    >
                        {isOpen ? (
                            <ChevronDown className="h-4 w-4"/>
                        ) : (
                            <ChevronRight className="h-4 w-4"/>
                        )}
                    </Button>
                ) : (
                    <div className="w-6"/>
                )}
                <span className={cn(
                    "flex-1",
                    selectedId === item.id && "font-medium"
                )}>
          {item.label}
        </span>
            </div>
            {hasChildren && isOpen && (
                <div>
                    {item.children?.map((child) => (
                        <OrgTreeItem
                            key={child.id}
                            item={child}
                            level={level + 1}
                            selectedId={selectedId}
                            onSelect={onSelect}
                            searchValue={searchValue}
                        />
                    ))}
                </div>
            )}
        </>
    );
};

export default function EditCasePage() {
    const params = useParams<{ id: string }>();
    const isEdit = !!params.id;

    // 分类树数据
    const [navigationTree, setNavigationTree] = useState<NavigationTreeOptions[]>([]);
    const [newspaperTree, setNewspaperTree] = useState<NewspaperTreeOptions[]>([]);
    const [orgTree, setOrgTree] = useState<OrgTree[]>([]);
    // 分类树选择数据
    const [selectedNavigationTags, setSelectedNavigationTags] = useState<{ id: string; title: string }[]>([]);
    const [selectedNewspaperTags, setSelectedNewspaperTags] = useState<{ id: string; title: string }[]>([]);
    // 标签输入
    const [currentTag, setCurrentTag] = useState('');
    const [isMenuOpen, setIsMenuOpen] = useState(false);
    const [menuSearch, setMenuSearch] = useState('');

    // 表单数据
    const [formData, setFormData] = useState<FormState>({
        author: "",
        contentBrief: "",
        issueDept: "",
        issueTime: new Date().toISOString(),
        issueUnit: "",
        issuer: "",
        newsType: "PLATFORM",
        contentType: "CONTENT",
        content: "",
        contentFileId: "",
        status: "ENABLED",
        readingNum: 0,
        title: '',
        tags: [],
        id: undefined,
        topStatus: 'false',
        attachments: [],
        contentFile: {
            id: '',
            fileName: ''
        }
    });


    // 加载栏目树
    useRequest(fetchNavigationTree, {
        onSuccess: (res) => {
            if (res) {
                setNavigationTree(res);
            }
        }
    });

    // 加载报刊-板块列表
    useRequest(fetchNewspaperTree, {
        onSuccess: (res) => {
            if (res) {
                setNewspaperTree(res);
            }
        }
    });

    // 加载部门树
    useRequest(() => fetchOrgFilter(""), {
        onSuccess: (res) => {
            if (res) {
                setOrgTree(res);
            }
        },
    });

    // 加载用户基本信息
    useRequest(getUserInfo, {
        onSuccess: (res) => {
            if (res) {
                if (!isEdit) {
                    const beijingTime = new Date(Date.now() + 8 * 3600 * 1000);
                    if (Object.keys(res).length > 0) {
                        setFormData(prev => ({
                            ...prev,
                            issueUnit: res?.orgs[0]?.parent.name || "",
                            issuer: res?.name || "",
                            issueTime: beijingTime.toISOString().slice(0, 19)
                        }));
                    } else {
                        setFormData(prev => ({
                            ...prev,
                            issueTime: beijingTime.toISOString().slice(0, 19)
                        }));
                        toast.error('登录信息获取失败，请手动填写！');
                    }
                }
            }
        }
    });

    // 加载案例数据
    useRequest(() => {
        console.log('准备加载新闻数据, ID:', params.id); // 添加加载日志
        if (!isEdit) return Promise.reject('不是编辑模式');
        return getResourceManagerDetail(params.id);
    }, {
        ready: isEdit,
        onSuccess: (res) => {
            console.log('加载新闻数据成功:', res); // 添加成功日志
            if (res) {
                // 获取子模块下拉数据
                // setNewsBlockSelect(newsBlockAllSelect.filter(data => data?.navigation?.id === res.newsBlock.navigation.id));
                setFormData({
                    title: res.title ?? '',
                    tags: res.tags || [],
                    status: res.status ?? '',
                    author: res.author ?? '',
                    content: res.content ?? '',
                    contentBrief: res.contentBrief ?? '',
                    issueDept: res.issueDept ?? '',
                    contentType: res.contentType ?? '',
                    issueTime: res.issueTime ?? '',
                    issueUnit: res.issueUnit ?? '',
                    issuer: res.issuer ?? '',
                    newsType: res.newsType ?? '',
                    id: res.id ?? '',
                    contentFileId: res.contentFile?.id ?? '',
                    topStatus: res.topStatus || 'false',
                    readingNum: res.readingNum || 0,
                    attachments: res.attachments.map(att => ({
                        fileId: att.file.id,
                        url: att.file.url
                    })),
                    contentFile: {
                        id: res.contentFile?.id,
                        fileName: res.contentFile?.originalFilename,
                    }
                });
                res.newsBlocks.map(item => (
                    toggleNavigationTag({
                        id: item.id,
                        title: item.title
                    })
                ));
                res.newspaperBoards.map(item => (
                    toggleNewspaperTag({
                        id: item.id,
                        title: item.title
                    })
                ));
            }
        },
        onError: (err) => {
            console.error('加载新闻数据失败:', err); // 添加错误日志
            toast.error('加载新闻数据失败');
        }
    });

    // 保存案例
    const {run: saveProductCase, loading} = useRequest(saveResourceManager, {
        manual: true,
        onSuccess: () => {
            toast.success(isEdit ? '编辑成功' : '创建成功');
            history.back();
        }
    });

    // 处理文件上传
    const handleAttachmentsUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
        const files = Array.from(e.target.files || []);
        for (const file of files) {
            if (!file.type.includes("image")) {
                toast.error('请上传图片类型的文件！');
                return;
            }
            const formData = new FormData();
            formData.append('file', file);
            const res = await uploadFileInfo(formData);
            if (res?.data) {
                setFormData(prev => ({
                    ...prev,
                    attachments: [...prev.attachments, {
                        fileId: res.data.id,
                        url: res.data.url
                    }]
                }));
            }
        }
    };

    // 移除附件
    const removeAttachments = (fileId: string) => {
        if (formData.newsType == 'OA') {
            return;
        }
        setFormData(prev => ({
            ...prev,
            attachments: prev.attachments.filter(att => att.fileId !== fileId)
        }));
    };

    // 处理内容文件上传
    const handleFileUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
        const files = Array.from(e.target.files || []);
        for (const file of files) {
            if (!file.type.includes("pdf")) {
                toast.error('请上传PDF类型的文件！');
                return;
            }
            const formData = new FormData();
            formData.append('file', file);
            const res = await uploadFile(formData);
            if (res?.data) {
                setFormData(prev => ({
                    ...prev,
                    contentFile: {
                        id: res.data,
                        fileName: file.name
                    },
                    contentFileId: res.data
                }));
            }
        }
    };

    // 移除内容附件
    const removeFile = () => {
        setFormData(prev => ({
            ...prev,
            contentFile: {
                id: '',
                fileName: ''
            },
            contentFileId: ''
        }));
    };

    // 添加标签
    const addTag = (value: string) => {
        const tag = value.trim();
        if (tag && !formData.tags.includes(tag)) {
            setFormData(prev => ({
                ...prev,
                tags: [...prev.tags, tag]
            }));
        }
        setCurrentTag('');
    };

    // 处理标签输入
    const handleTagInput = (e: React.KeyboardEvent<HTMLInputElement>) => {
        if (e.key === 'Enter' && currentTag.trim()) {
            e.preventDefault();
            addTag(currentTag);
        } else if (e.key === 'Backspace' && !currentTag) {
            e.preventDefault();
            setFormData(prev => ({
                ...prev,
                tags: prev.tags.slice(0, -1)
            }));
        }
    };

    // 处理失去焦点
    const handleBlur = () => {
        if (currentTag.trim()) {
            addTag(currentTag);
        }
    };

    // 移除标签
    const removeTag = (tagToRemove: string) => {
        if (formData.newsType == 'OA') {
            return;
        }
        setFormData(prev => ({
            ...prev,
            tags: prev.tags.filter(tag => tag !== tagToRemove)
        }));
    };

    // 处理表单字段变化
    const handleFieldChange = (field: keyof FormState, value: string) => {
        setFormData(prev => ({
            ...prev,
            [field]: value
        }));
    };

    const richTextHandler = (content: string) => {
        setFormData(prev => ({
            ...prev,
            content
        }));
    }

    const toggleNavigationTag = (tag: { id: string; title: string }) => {
        setSelectedNavigationTags(prev =>
            prev.some(t => t.id === tag.id)
                ? prev.filter(t => t.id !== tag.id)
                : [...prev, tag]
        );
    }

    const removeNavigationTag = (id: string) => {
        setSelectedNavigationTags(prev => prev.filter(tag => tag.id !== id));
    }

    const toggleNewspaperTag = (tag: { id: string; title: string }) => {
        setSelectedNewspaperTags(prev =>
            prev.some(t => t.id === tag.id)
                ? prev.filter(t => t.id !== tag.id)
                : [...prev, tag]
        );
    }

    const removeNewspaperTag = (id: string) => {
        setSelectedNewspaperTags(prev => prev.filter(tag => tag.id !== id));
    }
    // 获取选中菜单项的名称
    const getSelectedMenuName = () => {
        if (!formData.issueDept) {
            return "选择部门";
        }
        const selectedItem = findMenuItem(orgTree, formData.issueDept);
        return selectedItem?.label || "选择部门";
    };
    // 递归查找菜单项
    const findMenuItem = (items: {
        id: string;
        label: string;
        children?: { id: string; label: string }[]
    }[], targetId: string): { id: string; label: string } | null => {
        for (const item of items) {
            if (item.id === targetId) {
                return item;
            }
            if (item.children) {
                const found = findMenuItem(item.children, targetId);
                if (found) {
                    return found;
                }
            }
        }
        return null;
    };
    // 提交表单
    const handleSubmit = () => {
        if (!formData.title) {
            toast.error('请输入标题');
            return;
        }
        if (formData.contentType == 'FILE' && !formData.contentFileId) {
            toast.error('请上传内容附件');
            return;
        } else if (formData.contentType == 'CONTENT' && !formData.content) {
            toast.error('请输入文章内容');
            return;
        }
        if (formData.contentType == 'FILE') {
            formData.content = ''
        } else {
            formData.contentFileId = ''
        }
        const submitData: NewsResourceManagerSave = {
            title: formData.title,
            tags: formData.tags,
            id: formData.id,
            status: formData.status,
            author: formData.author,
            issueDept: formData.issueDept,
            issueTime: format(new Date(formData.issueTime), 'yyyy-MM-dd HH:mm:ss'),
            issuer: formData.issuer,
            issueUnit: formData.issueUnit,
            contentType: formData.contentType,
            contentBrief: formData.contentBrief,
            content: formData.content,
            contentFileId: formData.contentFileId,
            newsType: formData.newsType,
            readingNum: formData.readingNum,
            sequence: 0,
            code: '',
            topStatus: formData.topStatus,
            attachments: formData.attachments.map(att => ({
                fileId: att.fileId
            })),
            newsBlocks: selectedNavigationTags.map(item => ({
                id: item.id
            })),
            newspaperBoards: selectedNewspaperTags.map(item => ({
                id: item.id
            })),
        };
        saveProductCase(submitData);
    };




    return (
        <div className="flex-1 p-4 overflow-auto">
            <Card className="p-6">
                <h1 className="text-2xl font-bold mb-6">{isEdit ? '编辑新闻' : '新建新闻'}</h1>
                <div className="space-y-6">
                    <div className="flex space-x-4">
                        <div className="space-y-2 flex-1">
                            <Label>模块-栏目</Label>
                            <DropdownMenu>
                                <div
                                    className="flex flex-wrap justify-between gap-2 p-2 border rounded-lg cursor-pointer min-h-[40px]">
                                    <div className="flex flex-wrap cursor-pointer" role="button">
                                        {selectedNavigationTags.length === 0 && (
                                            <span className="text-muted-foreground">请选择模块</span>
                                        )}
                                        {selectedNavigationTags.map((tag) => (
                                            <Badge key={tag.id} variant="secondary" className="flex items-center gap-1">
                                                {/* 显示标题 */}
                                                {tag.title}
                                                <button
                                                    type="button"
                                                    onClick={(e) => {
                                                        e.preventDefault();
                                                        e.stopPropagation();
                                                        removeNavigationTag(tag.id); // 传递ID删除
                                                    }}
                                                    className="h-3 w-3 rounded-full "
                                                >
                                                    <X className="h-3 w-3 cursor-pointer"/>
                                                </button>
                                            </Badge>
                                        ))}
                                    </div>
                                    <div><DropdownMenuTrigger asChild>
                                        <button>选择</button>
                                    </DropdownMenuTrigger></div>
                                </div>
                                <DropdownMenuContent className="w-64 max-h-80 overflow-auto">
                                    <DropdownMenuLabel>栏目-模块</DropdownMenuLabel>
                                    {(navigationTree ?? []).map((navigation) => (
                                        <DropdownMenuSub key={navigation.id}>
                                            <DropdownMenuSubTrigger>{navigation.title}</DropdownMenuSubTrigger>
                                            <DropdownMenuSubContent>
                                                {navigation.blocks.map((item) => (
                                                    <DropdownMenuCheckboxItem
                                                        key={item.id}
                                                        // 检查ID是否选中
                                                        checked={selectedNavigationTags.some(tag => tag.id === item.id)}
                                                        // 保存时传递ID
                                                        onCheckedChange={() => toggleNavigationTag({
                                                            id: item.id,     // 保存ID
                                                            title: item.title // 保留标题用于显示
                                                        })}
                                                    >
                                                        {item.title} {/* 显示标题 */}
                                                    </DropdownMenuCheckboxItem>
                                                ))}
                                            </DropdownMenuSubContent>
                                        </DropdownMenuSub>
                                    ))}
                                </DropdownMenuContent>
                            </DropdownMenu>
                        </div>
                        <div className="space-y-2 flex-1">
                            <Label>报刊-版面</Label>
                            <DropdownMenu>
                                <div
                                    className="flex flex-wrap justify-between gap-2 p-2 border rounded-lg cursor-pointer min-h-[40px]">
                                    <div className="flex flex-wrap cursor-pointer" role="button">
                                        {selectedNewspaperTags.length === 0 && (
                                            <span className="text-muted-foreground">请选择版面</span>
                                        )}
                                        {selectedNewspaperTags.map((tag) => (
                                            <Badge key={tag.id} variant="secondary" className="flex items-center gap-1">
                                                {/* 显示标题 */}
                                                {tag.title}
                                                <button
                                                    type="button"
                                                    onClick={(e) => {
                                                        e.preventDefault();
                                                        e.stopPropagation();
                                                        removeNewspaperTag(tag.id); // 传递ID删除
                                                    }}
                                                    className="h-3 w-3 rounded-full"
                                                >
                                                    <X className="h-3 w-3 cursor-pointer"/>
                                                </button>
                                            </Badge>
                                        ))}
                                    </div>
                                    <div>
                                        <DropdownMenuTrigger asChild>
                                            <button>选择</button>
                                        </DropdownMenuTrigger>
                                    </div>
                                </div>
                                <DropdownMenuContent className="w-64 max-h-80 overflow-auto">
                                    <DropdownMenuLabel>报刊-版面</DropdownMenuLabel>
                                    {(newspaperTree ?? []).map((newspaper) => (
                                        <DropdownMenuSub key={newspaper.id}>
                                            <DropdownMenuSubTrigger>{newspaper.title}</DropdownMenuSubTrigger>
                                            <DropdownMenuSubContent>
                                                {newspaper.newspaperBoards.map((item) => (
                                                    <DropdownMenuCheckboxItem
                                                        key={item.id}
                                                        // 检查ID是否选中
                                                        checked={selectedNewspaperTags.some(tag => tag.id === item.id)}
                                                        // 保存时传递ID
                                                        onCheckedChange={() => toggleNewspaperTag({
                                                            id: item.id,     // 保存ID
                                                            title: item.title // 保留标题用于显示
                                                        })}
                                                    >
                                                        {item.title} {/* 显示标题 */}
                                                    </DropdownMenuCheckboxItem>
                                                ))}
                                            </DropdownMenuSubContent>
                                        </DropdownMenuSub>
                                    ))}
                                </DropdownMenuContent>
                            </DropdownMenu>
                        </div>
                    </div>
                    <div className="flex space-x-4">
                        <div className="space-y-2 flex-1">
                            <Label>标题</Label>
                            <Input
                                disabled={formData.newsType == "OA"}
                                value={formData.title}
                                onChange={(e) => handleFieldChange('title', e.target.value)}
                                placeholder="请输入标题"
                            />
                        </div>
                        <div className="space-y-2 flex-1">
                            <Label>所属部门</Label>
                            <Input
                                disabled={formData.newsType == "OA"}
                                value={formData.title}
                                onChange={(e) => handleFieldChange('issueDept', e.target.value)}
                                placeholder="请输入所属部门"
                            />
                        </div>
                        {/*<div className="space-y-2 w-1/3">*/}
                        {/*    <Label htmlFor="parent">所属部门</Label>*/}
                        {/*    <Popover open={isMenuOpen} onOpenChange={setIsMenuOpen}>*/}
                        {/*        <PopoverTrigger asChild>*/}
                        {/*            <Button*/}
                        {/*                variant="outline"*/}
                        {/*                role="combobox"*/}
                        {/*                aria-expanded={isMenuOpen}*/}
                        {/*                className="w-full justify-between"*/}
                        {/*            >*/}
                        {/*                {getSelectedMenuName() || "选择部门"}*/}
                        {/*                <ChevronDown className="ml-2 h-4 w-4 shrink-0 opacity-50"/>*/}
                        {/*            </Button>*/}
                        {/*        </PopoverTrigger>*/}
                        {/*        <PopoverContent className="w-[300px] p-0" align="start">*/}
                        {/*            <div className="p-2">*/}
                        {/*                <div className="relative">*/}
                        {/*                    <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground"/>*/}
                        {/*                    <Input*/}
                        {/*                        placeholder="搜索部门..."*/}
                        {/*                        value={menuSearch}*/}
                        {/*                        onChange={(e) => setMenuSearch(e.target.value)}*/}
                        {/*                        className="pl-8"*/}
                        {/*                    />*/}
                        {/*                </div>*/}
                        {/*            </div>*/}
                        {/*            <div className="max-h-[300px] overflow-auto p-2">*/}
                        {/*                {orgTree.map(item => (*/}
                        {/*                    <OrgTreeItem*/}
                        {/*                        key={item.id}*/}
                        {/*                        item={item}*/}
                        {/*                        level={0}*/}
                        {/*                        selectedId={formData.issueUnit || null}*/}
                        {/*                        onSelect={(id) => {*/}
                        {/*                            handleFieldChange('issueDept', typeof id === "string" ? id : "")*/}
                        {/*                            setIsMenuOpen(false);*/}
                        {/*                        }}*/}
                        {/*                        searchValue={menuSearch}*/}
                        {/*                    />*/}
                        {/*                ))}*/}
                        {/*            </div>*/}
                        {/*        </PopoverContent>*/}
                        {/*    </Popover>*/}
                        {/*</div>*/}
                        <div className="space-y-2 flex-1">
                            <Label>作者</Label>
                            <Input
                                disabled={formData.newsType == "OA"}
                                value={formData.author}
                                onChange={(e) => handleFieldChange('author', e.target.value)}
                                placeholder="请输入作者"
                            />
                        </div>
                    </div>
                    <div className="flex space-x-4">
                        <div className="space-y-2 flex-1">
                            <Label>发布人</Label>
                            <Input
                                value={formData.issuer}
                                disabled={formData.newsType == "OA"}
                                onChange={(e) => handleFieldChange('issuer', e.target.value)}
                                placeholder="请输入发布人"
                            />
                        </div>
                        <div className="space-y-2 flex-1">
                            <Label>发布时间</Label>
                            <Input
                                type="datetime-local"
                                disabled={formData.newsType == "OA"}
                                value={formData.issueTime}
                                onChange={(e) => handleFieldChange('issueTime', e.target.value)}
                                placeholder="请选择发布时间"
                            />
                        </div>
                        <div className="space-y-2 flex-1">
                            <Label>发布单位</Label>
                            <Input
                                disabled={formData.newsType == "OA"}
                                value={formData.issueUnit}
                                onChange={(e) => handleFieldChange('issueUnit', e.target.value)}
                                placeholder="请输入发布单位"
                            />
                        </div>
                    </div>
                    <div className="flex space-x-4">
                        <div className="space-y-2 flex-1">
                            <Label>新闻类型</Label>
                            <Select
                                disabled={true}
                                value={formData.newsType}
                                onValueChange={(value) => handleFieldChange("newsType", value)}
                            >
                                <SelectTrigger className="w-[100%]">
                                    <SelectValue placeholder="请选择新闻类型"/>
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="OA">OA平台</SelectItem>
                                    <SelectItem value="PLATFORM">新闻平台</SelectItem>
                                </SelectContent>
                            </Select>
                        </div>
                        <div className="space-y-2 flex-1">
                            <Label>置顶</Label>
                            <Select
                                value={formData.topStatus}
                                onValueChange={(value) => handleFieldChange("topStatus", value)}
                            >
                                <SelectTrigger className="w-[100%]">
                                    <SelectValue placeholder="请选择"/>
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="true">是</SelectItem>
                                    <SelectItem value="false">否</SelectItem>
                                </SelectContent>
                            </Select>
                        </div>
                        <div className="space-y-2 flex-1">
                            <Label>标签</Label>
                            <div className="flex flex-wrap gap-2 p-2 border rounded-lg">
                                <div className="flex flex-wrap gap-2">
                                    {formData.tags?.map(tag => (
                                        <Badge key={tag} variant="secondary" className="flex items-center gap-1">
                                            {tag}
                                            <button
                                                type="button"
                                                onClick={() => removeTag(tag)}
                                                className="h-3 w-3 rounded-full"
                                            >
                                                <X className="h-3 w-3 cursor-pointer"/>
                                            </button>
                                        </Badge>
                                    ))}
                                </div>
                                <Input
                                    disabled={formData.newsType == "OA"}
                                    value={currentTag}
                                    onChange={(e) => setCurrentTag(e.target.value)}
                                    onKeyDown={handleTagInput}
                                    onBlur={handleBlur}
                                    placeholder="输入标签后按回车添加"
                                    className="border-0 flex-1 p-0 focus-visible:ring-0 placeholder:text-muted-foreground h-5 min-w-[200px]"
                                />
                            </div>
                        </div>
                    </div>
                    <div className="space-y-2">
                        <Label>封面图</Label>
                        <div className="border rounded-lg p-4">
                            <Input
                                disabled={formData.newsType == "OA"}
                                type="file"
                                onChange={handleAttachmentsUpload}
                                className="mb-2"
                                multiple
                            />
                            <div className="space-y-2">
                                {formData.attachments.length > 0 && (
                                    <div className="flex items-center gap-2 p-2 border rounded">
                                        {formData.attachments.map((attachment) => (
                                            <div key={attachment.fileId} className="flex items-center gap-2">
                                                <img className="w-20 h-20 object-cover rounded" src={attachment.url}
                                                     alt=""/>
                                                <Button
                                                    variant="ghost"
                                                    size="sm"
                                                    onClick={() => removeAttachments(attachment.fileId)}
                                                >
                                                    <X className="h-4 w-4"/>
                                                </Button>
                                            </div>
                                        ))}
                                    </div>
                                )}
                            </div>
                        </div>
                    </div>
                    <div className="space-y-2 flex-1">
                        <Label>内容摘要</Label>
                        <Textarea
                            disabled={formData.newsType == "OA"}
                            value={formData.contentBrief}
                            onChange={(e) => handleFieldChange('contentBrief', e.target.value)}
                            placeholder="请输入内容摘要"
                        />
                    </div>

                    <div className="space-y-2">
                        <Label>内容类型</Label>
                        <div className="flex gap-2 max-w-[400px]">
                            <Button
                                disabled={formData.newsType == "OA"}
                                type="button"
                                variant={formData.contentType === 'CONTENT' ? "default" : "outline"}
                                onClick={() => handleFieldChange('contentType', 'CONTENT')}
                                className="flex-1"
                            >
                                富文本
                            </Button>
                            <Button
                                disabled={formData.newsType == "OA"}
                                type="button"
                                variant={formData.contentType === 'FILE' ? "default" : "outline"}
                                onClick={() => handleFieldChange('contentType', 'FILE')}
                                className="flex-1"
                            >
                                文件
                            </Button>
                        </div>
                    </div>

                    {formData.contentType == 'CONTENT' && <div className="space-y-2 flex-1">
                        <Label>富文本内容</Label>
                        <TipTapEditor
                            content={formData.content}
                            onChange={richTextHandler}
                            readOnly={formData.newsType == "OA"}
                            placeholder="开始编写您的内容..."
                            className="min-h-[400px]"
                        />
                    </div>
                    }

                    {formData.contentType == 'FILE' &&
                        <div className="space-y-2">
                            <Label>内容附件</Label>
                            <div className="border rounded-lg p-4">
                                <Input
                                    type="file"
                                    onChange={handleFileUpload}
                                    className="mb-2"
                                    multiple
                                />
                                {formData.contentFile.id && (
                                    <div className="space-y-2">
                                        <div key={formData.contentFile.id}
                                             className="flex items-center gap-2 p-2 border rounded">
                                            <div className="flex-1 truncate">{formData.contentFile.fileName}</div>
                                            <Button
                                                variant="ghost"
                                                size="sm"
                                                onClick={() => removeFile()}
                                            >
                                                <X className="h-4 w-4"/>
                                            </Button>
                                        </div>
                                    </div>
                                )}
                            </div>
                        </div>
                    }

                    <div className="flex justify-end gap-4">
                        <Button variant="outline" onClick={() => history.back()}>
                            取消
                        </Button>
                        <Button onClick={handleSubmit} disabled={loading}>
                            {loading ? '保存中...' : '保存'}
                        </Button>
                    </div>
                </div>
            </Card>
        </div>
    );
}