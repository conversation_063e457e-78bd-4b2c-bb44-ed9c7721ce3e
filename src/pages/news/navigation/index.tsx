import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Plus } from "lucide-react";
import { useRequest, history, useAccess } from "umi";
import { Navigation, deleteNavigation, fetchNavigationPage } from "@/service/news/navigation";
import { NavigationTable } from './components/navigation-table';
import { Page, PageQuery, Specification } from '@/types';
import { DataTableRowAction } from "@/components/data-table/types";
import { She<PERSON>, SheetContent, SheetHeader, SheetTitle } from "@/components/ui/sheet";
import { NavigationForm } from './components/navigation-form';

export default function NavigationPage() {
  const [pageData, setPageData] = useState<Page<Navigation>>();
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [editingItem, setEditingItem] = useState<Navigation>();
  const access = useAccess();
  
  // 检查用户是否有编辑权限
  const canEdit = access.hasAction && access.hasAction('NEWS:NAVIGATION:SAVE');

  const { run: deleteItem } = useRequest(deleteNavigation, {
    manual: true,
    onSuccess: () => {
      // 刷新数据
      handleQuery({
        pageSize: 10,
        page: 0,
        specification: {}
      });
    },
  });

  const handleQuery = async (query: PageQuery<Specification<Navigation>>) => {
    const response = await fetchNavigationPage(query);
    setPageData(response.data);
    return response.data;
  };

  const [rowAction, setRowActionState] = useState<DataTableRowAction<Navigation> | null>(null);

  // 监听rowAction状态变化，触发相应处理
  useEffect(() => {
    if (rowAction) {
      handleRowAction(rowAction);
    }
  }, [rowAction]);

  const handleRowAction = (action: DataTableRowAction<Navigation> | null) => {
    // 如果为空，直接返回
    if (!action) return;
    
    // 将row的原始数据转换为完整的CasesNavigation对象
    const rowData = action.row.original as Navigation;
    
    if (action.type === 'delete') {
      deleteItem(rowData.id!);
    } else if (action.type === 'update') { // 使用update代替edit
      setEditingItem(rowData);
      setIsFormOpen(true);
    } else if (action.type === 'auth') { // 使用auth代替view
      // 查看详情
      setEditingItem(rowData);
      setIsFormOpen(true);
    }
  };

  const handleSuccess = () => {
    setIsFormOpen(false);
    setEditingItem(undefined);
    // 刷新数据
    handleQuery({
      pageSize: 10,
      page: 0,
      specification: {}
    });
  };

  const handleAdd = () => {
    setEditingItem(undefined);
    setIsFormOpen(true);
  };

  return (
    <div className="container mx-auto py-6">
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-2xl font-bold">栏目管理</h1>
        {canEdit && (
          <Button onClick={handleAdd}>
            <Plus className="mr-2 h-4 w-4" />
            新增栏目
          </Button>
        )}
      </div>

      <div className="w-full">
        <NavigationTable
          onQuery={handleQuery}
          setRowAction={setRowActionState}
          data={pageData}
        />
      </div>

      <Sheet open={isFormOpen} onOpenChange={setIsFormOpen}>
        <SheetContent className="sm:max-w-xl">
          <SheetHeader>
            <SheetTitle>{editingItem ? '编辑栏目' : '新增栏目'}</SheetTitle>
          </SheetHeader>
          <div className="mt-6 p-4">
            <NavigationForm
              initialData={editingItem}
              onSuccess={handleSuccess}
              onCancel={() => setIsFormOpen(false)}
            />
          </div>
        </SheetContent>
      </Sheet>
    </div>
  );
}
