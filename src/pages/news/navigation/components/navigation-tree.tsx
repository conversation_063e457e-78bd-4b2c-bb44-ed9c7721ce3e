import React from 'react';
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Skeleton } from '@/components/ui/skeleton';
import { cn } from '@/lib/utils';
import { ChevronDown, ChevronRight, Folder, Plus } from 'lucide-react';
import { useNavigate } from 'umi';
import { Navigation } from '@/service/news/navigation';

export function NavigationTree({
  data,
  isLoading,
  selectedId,
  onSelect,
  onAdd,
  className,
}: {
  data: Navigation[];
  isLoading: boolean;
  selectedId?: string;
  onSelect?: (id: string) => void;
  onAdd?: (parentId?: string) => void;
  className?: string;
}) {
  const navigate = useNavigate();
  const [expanded, setExpanded] = React.useState<Record<string, boolean>>({});

  const toggleExpand = (id: string) => {
    setExpanded(prev => ({
      ...prev,
      [id]: !prev[id],
    }));
  };

  const renderTree = (items: Navigation[], level = 0) => {
    return items.map(item => {
      const hasChildren = item.children && item.children.length > 0;
      const isExpanded = expanded[item.id!];
      const isSelected = selectedId === item.id;

      return (
        <div key={item.id} className="space-y-1">
          <div
            className={cn(
              'flex items-center rounded-md px-2 py-1.5 text-sm hover:bg-accent',
              isSelected && 'bg-accent',
            )}
            style={{ paddingLeft: `${level * 16 + 8}px` }}
            onClick={() => onSelect?.(item.id!)}
          >
            {hasChildren ? (
              <Button
                variant="ghost"
                size="icon"
                className="h-6 w-6 p-0"
                onClick={e => {
                  e.stopPropagation();
                  toggleExpand(item.id!);
                }}
              >
                {isExpanded ? (
                  <ChevronDown className="h-4 w-4" />
                ) : (
                  <ChevronRight className="h-4 w-4" />
                )}
              </Button>
            ) : (
              <div className="h-6 w-6" />
            )}
            <Folder className="mr-2 h-4 w-4 text-muted-foreground" />
            <span className="truncate">{item.title}</span>
            <Button
              variant="ghost"
              size="icon"
              className="ml-auto h-6 w-6 p-0"
              onClick={e => {
                e.stopPropagation();
                onAdd?.(item.id);
              }}
            >
              <Plus className="h-4 w-4" />
            </Button>
          </div>
          {hasChildren && isExpanded && (
            <div className="ml-4">
              {renderTree(item.children!, level + 1)}
            </div>
          )}
        </div>
      );
    });
  };

  if (isLoading) {
    return (
      <div className="space-y-2 p-2">
        {Array.from({ length: 5 }).map((_, i) => (
          <Skeleton key={i} className="h-8 w-full" />
        ))}
      </div>
    );
  }

  return (
    <div className={cn('space-y-2', className)}>
      <div className="flex items-center justify-between px-2">
        <h3 className="text-sm font-medium">栏目管理</h3>
        <Button
          variant="ghost"
          size="sm"
          className="h-8 px-2"
          onClick={() => onAdd?.()}
        >
          <Plus className="mr-1 h-4 w-4" />
          新增
        </Button>
      </div>
      <ScrollArea className="h-[calc(100vh-200px)]">
        <div className="space-y-1 p-2">
          {data.length > 0 ? (
            renderTree(data)
          ) : (
            <div className="py-6 text-center text-sm text-muted-foreground">
              暂无数据
            </div>
          )}
        </div>
      </ScrollArea>
    </div>
  );
}
