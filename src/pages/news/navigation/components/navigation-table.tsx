import React, { useEffect } from 'react';
import {DataTableFilterField, DataTableRowAction} from "@/components/data-table/types";
import {useDataTable} from "@/components/data-table/hooks/use-data-table";
import {DataTable} from "@/components/data-table/data-table";
import {DataTableToolbar} from "@/components/data-table/data-table-toolbar";
import {getColumns} from './navigation-table-columns';
import {Navigation} from '@/service/news/navigation';
import {Page, PageQuery, Specification} from '@/types';
import {getFilterFields} from './navigation-table-filters';
import {useAccess} from "umi";
import {getActions} from './navigation-table-columns-action';

interface NavigationTableProps {
  onQuery: (query: PageQuery<Specification<Navigation>>) => Promise<Page<Navigation>>;
  setRowAction: React.Dispatch<React.SetStateAction<DataTableRowAction<Navigation> | null>>;
  data?: Page<Navigation>;
}

export function NavigationTable({ onQuery, setRowAction, data }: NavigationTableProps) {
  const access = useAccess();

  const actions = React.useMemo(
    () => getActions({ setRowAction, access }),
    [setRowAction, access]
  );

  const columns = React.useMemo(() => getColumns({ actions }), [actions]);
  const [filterFields, setFilterFields] = React.useState<DataTableFilterField<Navigation>[]>(getFilterFields());



  const { table } = useDataTable({
    data: data?.data || [],
    columns,
    pageCount: Math.ceil((data?.total || 0) / (data?.pageSize || 10)),
    filterFields,
    initialState: {
      pagination: {
        pageIndex: 0,
        pageSize: 10,
      },
      columnPinning: { right: ["actions"] },
    },
    getRowId: (originalRow) => originalRow.id!,
  });

  // 监听表格状态变化，触发查询
  useEffect(() => {
    setFilterFields(getFilterFields());
  }, [data]);

  return (
    <div className="space-y-4">
      <DataTableToolbar table={table} filterFields={filterFields} onFilterChange={onQuery} />
      <DataTable table={table} />
    </div>
  );
}
