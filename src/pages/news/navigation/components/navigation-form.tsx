import React from 'react';
import { useF<PERSON>, SubmitHandler } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Navigation, saveNavigation } from '@/service/news/navigation';
import { useRequest } from 'umi';

const formSchema = z.object({
  id: z.string().optional(),
  title: z.string().min(1, '请输入栏目名称'),
  code: z.string().min(1, '请输入编码'),
  remark: z.string().optional(),
  sequence: z.number().default(0),
  status: z.enum(['ENABLED', 'DISABLED']).default('ENABLED'),
});

type NavigationFormValues = z.infer<typeof formSchema>;

interface NavigationFormProps {
  initialData?: Navigation;
  onSuccess?: () => void;
  onCancel?: () => void;
}

export function NavigationForm({ initialData, onSuccess, onCancel }: NavigationFormProps) {
  const isEdit = !!initialData?.id;
  const defaultValues: NavigationFormValues = {
    title: initialData?.title || '',
    code: initialData?.code || '',
    remark: initialData?.remark || '',
    sequence: initialData?.sequence || 0,
    status: (initialData?.status as 'ENABLED' | 'DISABLED') || 'ENABLED',
  };

  const form = useForm<NavigationFormValues>({
    resolver: zodResolver(formSchema),
    defaultValues,
  });

  const { loading: saveLoading, run: runSave } = useRequest(saveNavigation, {
    manual: true,
    onSuccess: () => {
      onSuccess?.();
    },
  });

  const { loading: updateLoading, run: runUpdate } = useRequest(saveNavigation, {
    manual: true,
    onSuccess: () => {
      onSuccess?.();
    },
  });

  const onSubmit: SubmitHandler<NavigationFormValues> = (data) => {
    if (isEdit && initialData?.id) {
      runUpdate({
        ...data,
        id: initialData.id
      });
    } else {
      // 确保parentId是字符串或undefined，不是null
      const formData = {
        ...data
      };
      runSave(formData);
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
        <FormField
          control={form.control}
          name="title"
          render={({ field }) => (
            <FormItem>
              <FormLabel>栏目名称</FormLabel>
              <FormControl>
                <Input placeholder="请输入栏目名称" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="code"
          render={({ field }) => (
            <FormItem>
              <FormLabel>编码</FormLabel>
              <FormControl>
                <Input placeholder="请输入编码" {...field} disabled={isEdit} />
              </FormControl>
              <FormDescription>编码创建后不可修改</FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="status"
          render={({ field }) => (
            <FormItem>
              <FormLabel>状态</FormLabel>
              <Select onValueChange={field.onChange} defaultValue={field.value}>
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="请选择状态" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  <SelectItem value="ENABLED">启用</SelectItem>
                  <SelectItem value="DISABLED">禁用</SelectItem>
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="sequence"
          render={({ field }) => (
            <FormItem>
              <FormLabel>排序</FormLabel>
              <FormControl>
                <Input
                  type="number"
                  placeholder="请输入排序号"
                  {...field}
                  onChange={(e) => field.onChange(Number(e.target.value))}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="remark"
          render={({ field }) => (
            <FormItem>
              <FormLabel>说明</FormLabel>
              <FormControl>
                <Textarea placeholder="请输入说明" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="flex justify-end space-x-4">
          <Button type="button" variant="outline" onClick={onCancel}>
            取消
          </Button>
          <Button type="submit" disabled={saveLoading || updateLoading}>
            {isEdit ? '保存' : '创建'}
          </Button>
        </div>
      </form>
    </Form>
  );
}
