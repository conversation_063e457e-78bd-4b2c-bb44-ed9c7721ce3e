import {ColumnDef} from "@tanstack/react-table";
import {Navigation} from "@/service/news/navigation";
import {Badge} from "@/components/ui/badge";
import {format} from "date-fns";
import {DataTableColumnHeader} from "@/components/data-table/data-table-column-header";

export const getColumns = ({
                               actions,
                           }: {
    actions: any;
}): ColumnDef<Navigation>[] => {
    const columns: ColumnDef<Navigation>[] = [
        {
            accessorKey: "title",
            header: ({column}) => (
                <DataTableColumnHeader column={column} title="栏目名称"/>
            ),
            enableSorting: false,
            cell: ({row}) => {
                return (
                    <div className="flex space-x-2">
                        <span className="max-w-[200px] truncate font-medium">
                            {row.getValue("title")}
                        </span>
                    </div>
                );
            },
            meta: {
                title: "栏目名称",
            },
            enableHiding: true,
        },
        {
            accessorKey: "code",
            header: ({column}) => (
                <DataTableColumnHeader column={column} title="编码"/>
            ),
            cell: ({row}) => {
                return (
                    <div className="flex space-x-2">
                        <span className="max-w-[100px] truncate">
                            {row.getValue("code")}
                        </span>
                    </div>
                );
            },
            meta: {
                title: "编码",
            },
            enableSorting: false,
            enableHiding: true
        },
        {
            accessorKey: "status",
            header: ({column}) => (
                <DataTableColumnHeader column={column} title="状态"/>
            ),
            cell: ({row}) => {
                const status = row.getValue("status");
                return (
                    <Badge variant={status === 'ENABLED' ? 'default' : 'secondary'}>
                        {status === 'ENABLED' ? '启用' : '禁用'}
                    </Badge>
                );
            },
            filterFn: (row, id, value) => {
                return value.includes(row.getValue(id));
            },
            meta: {
                title: "状态",
            },
            enableSorting: false,
            enableHiding: true
        },
        {
            accessorKey: "sequence",
            header: ({column}) => (
                <DataTableColumnHeader column={column} title="序列"/>
            ),
            cell: ({row}) => {
                return (
                    <div className="flex space-x-2">
                        <span className="max-w-[100px] truncate">
                            {row.getValue("sequence")}
                        </span>
                    </div>
                );
            },
            meta: {
                title: "序列",
            },
            enableSorting: true
        },
    ]

    if (actions) {
        columns.push(actions);
    }
    return columns;
}
