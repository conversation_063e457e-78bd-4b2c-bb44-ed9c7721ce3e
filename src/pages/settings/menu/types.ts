export type PermissionType = 'WORKSPACE' | 'MENU' | 'ACTION'



export interface Permission {
  id: string ;
  code: string;
  name: string;
  description?: string;
  type: PermissionType;
  href?: string;
  sequence?: number;
  status: 'ACTIVE' | 'INACTIVE' | 'CONCEAL';
  meta?: {
    icon?: string;
    [key: string]: any;
  };
  children?: Permission[];
  parent?: Permission;
  roles?: any[]; // 这里可以根据实际需求定义 Role 类型
}

export interface MenuFormData {
  code: string;
  name: string;
  description?: string;
  type: PermissionType;
  href?: string;
  sequence?: number;
  status: 'ACTIVE' | 'INACTIVE' | 'CONCEAL';
  parent?:{id:string} | null;
  meta?: {
    icon?: string;
    [key: string]: any;
  };
} 