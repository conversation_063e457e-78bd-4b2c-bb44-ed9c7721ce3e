import React, {useMemo, useState} from 'react';
import {But<PERSON>} from "@/components/ui/button";
import {Plus} from "lucide-react";
import {Sheet, SheetContent, SheetDescription, SheetHeader, SheetTitle,} from "@/components/ui/sheet";
import {MenuForm} from './components/MenuForm';
import {MenuList} from './components/MenuList';
import {MenuFormData, Permission} from './types';
import {useRequest} from "umi";
import {fetchPermission, savePermission} from "@/service/auth";

const MenuPage: React.FC = () => {
  const [expandedRows, setExpandedRows] = useState<Set<string>>(new Set());
  const {run:runFetchPermission} = useRequest(fetchPermission,{
    onSuccess:(res)=>{
      setMenuItems(res)
    },
  });
  const {run:runSavePermission} = useRequest(savePermission,{
    manual:true,
  });
  const [menuItems, setMenuItems] = useState<Permission[]>([]);
  const [isSheetOpen, setIsSheetOpen] = useState(false);
  const [editingItem, setEditingItem] = useState<Permission | null>(null);
  const [formData, setFormData] = useState<MenuFormData>({
    code: '',
    name: '',
    description: '',
    type: 'MENU',
    href: '',
    sequence: 1,
    status: 'ACTIVE'
  });

  // 将树形菜单数据转换为扁平结构，用于父菜单选择
  const getParentOptions = useMemo(() => {
    const flattenTree = (items: Permission[]): { id: string; name: string; children?: { id: string; name: string }[] }[] => {
      return items.map(item => ({
        id: item.id,
        name: item.name,
        children: item.children ? flattenTree(item.children) : undefined
      }));
    };

    return flattenTree(menuItems);
  }, [menuItems]);

  const toggleRow = (id: string) => {
    const newExpandedRows = new Set(expandedRows);
    if (newExpandedRows.has(id)) {
      newExpandedRows.delete(id);
    } else {
      newExpandedRows.add(id);
    }
    setExpandedRows(newExpandedRows);
  };

  // 添加菜单（可以是顶级菜单或子菜单）
  const handleAddMenu = (parentItem?: Permission) => {
    setEditingItem(null);
    setFormData({
      code: '',
      name: '',
      description: '',
      type: 'MENU',
      href: '',
      sequence: 1,
      status: 'ACTIVE',
      parent: parentItem ? { id: parentItem.id } : null
    });
    setIsSheetOpen(true);
  };

  const handleEditMenu = (item: Permission) => {
    setEditingItem(item);
    setFormData(item);
    setIsSheetOpen(true);
  };

  // 删除菜单
  const handleDeleteMenu = (id: string) => {
    if (window.confirm('确定要删除这个菜单吗？')) {
      const deleteMenuItem = (items: Permission[]): Permission[] => {
        return items.filter(item => {
          if (item.id === id) return false;
          if (item.children) {
            item.children = deleteMenuItem(item.children);
          }
          return true;
        });
      };
      setMenuItems(deleteMenuItem(menuItems));
    }
  };

  // 切换菜单状态（上架/下架）
  const handleToggleMenuStatus = (item: Permission) => {
    const newStatus = item.status === 'ACTIVE' ? 'INACTIVE' : 'ACTIVE';
    const statusText = newStatus === 'ACTIVE' ? '上架' : '下架';
    
    if (window.confirm(`确定要${statusText}这个菜单吗？`)) {
      // 递归更新菜单及其子菜单的状态
      const updateMenuStatus = (items: Permission[], targetId: string): Permission[] => {
        return items.map(menuItem => {
          if (menuItem.id === targetId) {
            return { ...menuItem, status: newStatus };
          }
          
          if (menuItem.children && menuItem.children.length > 0) {
            return {
              ...menuItem,
              children: updateMenuStatus(menuItem.children, targetId)
            };
          }
          
          return menuItem;
        });
      };
      
      setMenuItems(updateMenuStatus(menuItems, item.id));
      
      // 如果是在编辑中的菜单，也更新表单数据
      if (editingItem && editingItem.id === item.id) {
        setFormData({ ...formData, status: newStatus });
      }
      
      // 保存到服务器
      runSavePermission({
        ...item,
        status: newStatus
      });
    }
  };

  const handleSubmit = async  () => {
    if (editingItem) {
    await  runSavePermission(formData)
    } else {
    await runSavePermission({
        ...formData
      })
    }
    runFetchPermission();
    setIsSheetOpen(false);
  };

  return (
    <div className="container mx-auto py-6">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold">菜单管理</h1>
          <p className="text-muted-foreground">管理系统菜单和权限</p>
        </div>
        <Button onClick={() => handleAddMenu()}>
          <Plus className="mr-2 h-4 w-4" />
          添加菜单
        </Button>
      </div>

      <div className="bg-card rounded-lg shadow">
        <MenuList
          items={menuItems}
          expandedRows={expandedRows}
          onToggleRow={toggleRow}
          onEdit={handleEditMenu}
          onDelete={handleDeleteMenu}
          onAddSubMenu={(parentItem) => handleAddMenu(parentItem)}
          onToggleStatus={handleToggleMenuStatus}
        />
      </div>

      <Sheet open={isSheetOpen} onOpenChange={setIsSheetOpen}>
        <SheetContent className="w-[800px] sm:max-w-[800px]">
          <SheetHeader>
            <SheetDescription/>
            <SheetTitle>{editingItem ? '编辑菜单' : '添加菜单'}</SheetTitle>
          </SheetHeader>
          <div className="mt-6">
            <MenuForm
              formData={formData}
              onChange={setFormData}
              parentOptions={getParentOptions}
              onSubmit={handleSubmit}
              onCancel={() => setIsSheetOpen(false)}
              isEditing={!!editingItem}
            />
          </div>
        </SheetContent>
      </Sheet>
    </div>
  );
};

export default MenuPage; 