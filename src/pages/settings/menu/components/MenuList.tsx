import React from 'react';
import {But<PERSON>} from "@/components/ui/button";
import {Table, TableBody, TableCell, TableHead, TableHeader, TableRow,} from "@/components/ui/table";
import {ChevronDown, ChevronRight, Pencil, Trash2, Plus, EyeOff, Eye} from "lucide-react";
import {Permission} from '../types';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

interface MenuListProps {
  items: Permission[];
  expandedRows: Set<string>;
  onToggleRow: (id: string) => void;
  onEdit: (item: Permission) => void;
  onDelete: (id: string) => void;
  onAddSubMenu?: (parentItem: Permission) => void;
  onToggleStatus?: (item: Permission) => void;
}

export const MenuList: React.FC<MenuListProps> = ({
  items,
  expandedRows,
  onToggleRow,
  onEdit,
  onDelete,
  onAddSubMenu,
  onToggleStatus
}) => {
  const renderMenuItem = (item: Permission, level: number = 0) => {
    const hasChildren = item.children && item.children.length > 0;
    const isExpanded = expandedRows.has(item.id);

    return (
      <React.Fragment key={item.id}>
        <TableRow>
          <TableCell>
            <div className="flex items-center" style={{ paddingLeft: `${level * 20}px` }}>
              {hasChildren && (
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-6 w-6 p-0 mr-2"
                  onClick={() => onToggleRow(item.id)}
                >
                  {isExpanded ? (
                    <ChevronDown className="h-4 w-4" />
                  ) : (
                    <ChevronRight className="h-4 w-4" />
                  )}
                </Button>
              )}
              {!hasChildren && <div className="w-6" />}
              {item.name}
            </div>
          </TableCell>
          <TableCell>{item.code}</TableCell>
          <TableCell>{item.type}</TableCell>
          <TableCell>{item.href || '-'}</TableCell>
          <TableCell>{item.sequence}</TableCell>
          <TableCell>
            <span className={`px-2 py-1 rounded-full text-xs ${
              item.status === 'ACTIVE' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
            }`}>
              {item.status === 'ACTIVE' ? '启用' : item.status === 'CONCEAL' ? '隐藏' : '禁用'}
            </span>
          </TableCell>
          <TableCell>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm" className="mr-2">
                  操作
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem onClick={() => onEdit(item)}>
                  <Pencil className="h-4 w-4 mr-2" />
                  编辑
                </DropdownMenuItem>
                
                {/* ACTION 类型的菜单不能添加子菜单 */}
                {item.type !== 'ACTION' && onAddSubMenu && (
                  <DropdownMenuItem onClick={() => onAddSubMenu(item)}>
                    <Plus className="h-4 w-4 mr-2" />
                    添加子菜单
                  </DropdownMenuItem>
                )}
                
                {onToggleStatus && (
                  <DropdownMenuItem onClick={() => onToggleStatus(item)}>
                    {item.status === 'ACTIVE' ? (
                      <>
                        <EyeOff className="h-4 w-4 mr-2" />
                        下架
                      </>
                    ) : (
                      <>
                        <Eye className="h-4 w-4 mr-2" />
                        上架
                      </>
                    )}
                  </DropdownMenuItem>
                )}
                
                <DropdownMenuItem className="text-red-600" onClick={() => onDelete(item.id)}>
                  <Trash2 className="h-4 w-4 mr-2" />
                  删除
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </TableCell>
        </TableRow>
        {hasChildren && isExpanded && item.children?.map(child => renderMenuItem(child, level + 1))}
      </React.Fragment>
    );
  };

  return (
    <div className="border rounded-lg">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>菜单名称</TableHead>
            <TableHead>权限编码</TableHead>
            <TableHead>权限类型</TableHead>
            <TableHead>访问路径</TableHead>
            <TableHead>排序</TableHead>
            <TableHead>状态</TableHead>
            <TableHead>操作</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {items.map(item => renderMenuItem(item))}
        </TableBody>
      </Table>
    </div>
  );
}; 