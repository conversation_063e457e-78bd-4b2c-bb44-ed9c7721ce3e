import React, {useMemo, useState} from 'react';
import {But<PERSON>} from "@/components/ui/button";
import {Input} from "@/components/ui/input";
import {Label} from "@/components/ui/label";
import {Select, SelectContent, SelectItem, SelectTrigger, SelectValue,} from "@/components/ui/select";
import {Textarea} from "@/components/ui/textarea";
import {ChevronDown, ChevronRight, Folder, icons, Search} from "lucide-react";
import {MenuFormData, PermissionType} from '../types';
import {Popover, PopoverContent, PopoverTrigger,} from "@/components/ui/popover";
import {cn} from "@/lib/utils";

interface MenuFormProps {
  formData: MenuFormData;
  onChange: (data: MenuFormData) => void;
  parentOptions?: { id: string; name: string; children?: { id: string; name: string }[] }[];
  onSubmit: () => void;
  onCancel: () => void;
  isEditing: boolean;
}

const ITEMS_PER_PAGE = 20;

const MenuTreeItem: React.FC<{
  item: { id: string; name: string; children?: { id: string; name: string }[] };
  level: number;
  selectedId: string | null;
  onSelect: (id: string | null) => void;
  searchValue: string;
}> = ({ item, level, selectedId, onSelect, searchValue }) => {
  const [isOpen, setIsOpen] = useState(false);
  const hasChildren = item.children && item.children.length > 0;
  const isMatch = item.name.toLowerCase().includes(searchValue.toLowerCase());
  const hasMatchingChildren = hasChildren && item.children?.some(child => 
    child.name.toLowerCase().includes(searchValue.toLowerCase())
  );

  // 如果有匹配的子项，自动展开
  React.useEffect(() => {
    if (hasMatchingChildren && !isOpen) {
      setIsOpen(true);
    }
  }, [hasMatchingChildren, isOpen]);

  // 如果没有匹配项且没有匹配的子项，不显示
  if (!isMatch && !hasMatchingChildren) {
    return null;
  }

  return (
    <>
      <div
        onClick={() => {
          onSelect(item.id);
          if (hasChildren) {
            setIsOpen(!isOpen);
          }
        }}
        className={cn(
          "cursor-pointer flex items-center px-2 py-1.5 hover:bg-accent rounded-sm",
          level > 0 && "ml-4",
          selectedId === item.id && "bg-accent"
        )}
      >
        {hasChildren ? (
          <Button
            variant="ghost"
            size="sm"
            className="h-6 w-6 p-0 mr-2"
            onClick={(e) => {
              e.stopPropagation();
              setIsOpen(!isOpen);
            }}
          >
            {isOpen ? (
              <ChevronDown className="h-4 w-4" />
            ) : (
              <ChevronRight className="h-4 w-4" />
            )}
          </Button>
        ) : (
          <div className="w-6" />
        )}
        <span className={cn(
          "flex-1",
          selectedId === item.id && "font-medium"
        )}>
          {item.name}
        </span>
      </div>
      {hasChildren && isOpen && (
        <div>
          {item.children?.map((child) => (
            <MenuTreeItem
              key={child.id}
              item={child}
              level={level + 1}
              selectedId={selectedId}
              onSelect={onSelect}
              searchValue={searchValue}
            />
          ))}
        </div>
      )}
    </>
  );
};

export const MenuForm: React.FC<MenuFormProps> = ({
  formData,
  onChange,
  parentOptions = [],
  onSubmit,
  onCancel,
  isEditing
}) => {
  const [iconSearch, setIconSearch] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [menuSearch, setMenuSearch] = useState('');
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  // 递归查找菜单项
  const findMenuItem = (items: { id: string; name: string; children?: { id: string; name: string }[] }[], targetId: string): { id: string; name: string } | null => {
    for (const item of items) {
      if (item.id === targetId) {
        return item;
      }
      if (item.children) {
        const found = findMenuItem(item.children, targetId);
        if (found) {
          return found;
        }
      }
    }
    return null;
  };

  // 获取选中菜单项的名称
  const getSelectedMenuName = () => {
    if (!formData.parent?.id) {
      return "选择上级菜单";
    }
    const selectedItem = findMenuItem(parentOptions, formData.parent.id);
    return selectedItem?.name || "选择上级菜单";
  };

  const handleTypeChange = (value: PermissionType) => {
    // 当类型变更为 ACTION 时，清空访问路径，但保留父菜单
    if (value === 'ACTION') {
      onChange({ ...formData, type: value, href: '' });
    } else {
      onChange({ ...formData, type: value });
    }
  };

  const handleIconSelect = (iconName: string) => {
    onChange({
      ...formData,
      meta: {
        ...formData.meta,
        icon: iconName
      }
    });
    setIconSearch('');
    setCurrentPage(1);
  };

  const selectedIcon = formData.meta?.icon ? icons[formData.meta.icon as keyof typeof icons] : null;

  // 过滤和分页图标列表
  const filteredIcons = useMemo(() => {
    const iconList = Object.entries(icons)
      .filter(([name]) => name.toLowerCase().includes(iconSearch.toLowerCase()))
      .map(([name, Icon]) => ({ name, Icon }));
    
    const startIndex = (currentPage - 1) * ITEMS_PER_PAGE;
    return {
      icons: iconList.slice(startIndex, startIndex + ITEMS_PER_PAGE),
      total: iconList.length
    };
  }, [iconSearch, currentPage]);

  const totalPages = Math.ceil(filteredIcons.total / ITEMS_PER_PAGE);

  return (
    <div className="space-y-6 px-4">
      <div className="space-y-2">
        <Label>权限类型</Label>
        <div className="flex gap-2">
          <Button
            type="button"
            variant={formData.type === 'WORKSPACE' ? "default" : "outline"}
            onClick={() => handleTypeChange("WORKSPACE")}
            className="flex-1"
          >
            工作空间
          </Button>
          <Button
            type="button"
            variant={formData.type === 'MENU' ? "default" : "outline"}
            onClick={() => handleTypeChange("MENU")}
            className="flex-1"
          >
            菜单
          </Button>
          <Button
            type="button"
            variant={formData.type === "ACTION" ? "default" : "outline"}
            onClick={() => handleTypeChange("ACTION")}
            className="flex-1"
          >
            操作
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-2 gap-6">
        <div className="space-y-2">
          <Label htmlFor="code">权限编码</Label>
          <Input
            id="code"
            value={formData.code}
            onChange={(e) => onChange({ ...formData, code: e.target.value })}
            disabled={isEditing}
            placeholder="请输入权限编码"
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="name">菜单名称</Label>
          <Input
            id="name"
            value={formData.name}
            onChange={(e) => onChange({ ...formData, name: e.target.value })}
            placeholder="请输入菜单名称"
          />
        </div>
        {formData.type !== "WORKSPACE" && (
          <div className="space-y-2">
            <Label htmlFor="parent">父菜单</Label>
            <Popover open={isMenuOpen} onOpenChange={setIsMenuOpen}>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  role="combobox"
                  aria-expanded={isMenuOpen}
                  className="w-full justify-between"
                >
                  {getSelectedMenuName() || "选择父菜单"}
                  <ChevronDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-[300px] p-0" align="start">
                <div className="p-2">
                  <div className="relative">
                    <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="搜索菜单..."
                      value={menuSearch}
                      onChange={(e) => setMenuSearch(e.target.value)}
                      className="pl-8"
                    />
                  </div>
                </div>
                <div className="max-h-[300px] overflow-auto p-2">
                  <div
                    className="cursor-pointer px-2 py-1.5 hover:bg-accent rounded-sm"
                    onClick={() => {
                      onChange({ ...formData, parent: null });
                      setIsMenuOpen(false);
                    }}
                  >
                    <Folder className="h-4 w-4 mr-2 inline-block" />
                    <span>根目录</span>
                  </div>
                  {parentOptions.map(item => (
                    <MenuTreeItem
                      key={item.id}
                      item={item}
                      level={0}
                      selectedId={formData.parent?.id || null}
                      onSelect={(id) => {
                        if (id) {
                          onChange({ ...formData, parent: { id } });
                        } else {
                          onChange({ ...formData, parent: null });
                        }
                        setIsMenuOpen(false);
                      }}
                      searchValue={menuSearch}
                    />
                  ))}
                </div>
              </PopoverContent>
            </Popover>
          </div>
        )}
        <div className="space-y-2">
          <Label>图标</Label>
          <Select
            value={formData.meta?.icon || 'none'}
            onValueChange={handleIconSelect}
          >
            <SelectTrigger className="w-full">
              <SelectValue placeholder="选择图标">
                {selectedIcon && (
                  <div className="flex items-center gap-2">
                    {React.createElement(selectedIcon, { className: "h-4 w-4" })}
                    <span>{formData.meta?.icon}</span>
                  </div>
                )}
              </SelectValue>
            </SelectTrigger>
            <SelectContent className="w-[300px]">
              <div className="p-2">
                <div className="relative">
                  <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="搜索图标..."
                    value={iconSearch}
                    onChange={(e) => {
                      setIconSearch(e.target.value);
                      setCurrentPage(1);
                    }}
                    className="pl-8"
                  />
                </div>
              </div>
              <div className="max-h-[300px] overflow-auto">
                <SelectItem value="none">无</SelectItem>
                {filteredIcons.icons.map(({ name, Icon }) => (
                  <SelectItem key={name} value={name}>
                    <div className="flex items-center gap-2">
                      {React.createElement(Icon, { className: "h-4 w-4" })}
                      <span>{name}</span>
                    </div>
                  </SelectItem>
                ))}
              </div>
              {totalPages > 1 && (
                <div className="flex items-center justify-between p-2 border-t">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setCurrentPage(p => Math.max(1, p - 1))}
                    disabled={currentPage === 1}
                  >
                    上一页
                  </Button>
                  <span className="text-sm text-muted-foreground">
                    第 {currentPage} 页，共 {totalPages} 页
                  </span>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setCurrentPage(p => Math.min(totalPages, p + 1))}
                    disabled={currentPage === totalPages}
                  >
                    下一页
                  </Button>
                </div>
              )}
            </SelectContent>
          </Select>
        </div>
        <div className="space-y-2">
          <Label htmlFor="order">排序</Label>
          <Input
            id="order"
            type="number"
            value={formData.sequence}
            onChange={(e) => onChange({ ...formData, sequence: parseInt(e.target.value) })}
            placeholder="请输入排序号"
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="status">状态</Label>
          <Select
            value={formData.status}
            onValueChange={(value: 'ACTIVE' | 'INACTIVE' | 'CONCEAL') => onChange({ ...formData, status: value })}
          >
            <SelectTrigger className="w-full">
              <SelectValue placeholder="请选择状态" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="ACTIVE">启用</SelectItem>
              <SelectItem value="INACTIVE">禁用</SelectItem>
              <SelectItem value="CONCEAL">隐藏</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

        {/* ACTION 类型菜单不显示访问路径输入框 */}
        {formData.type !== 'ACTION' && (
          <div className="space-y-2">
            <Label htmlFor="href">访问路径</Label>
            <Input
              id="href"
              value={formData.href || ''}
              onChange={(e) => onChange({ ...formData, href: e.target.value })}
              placeholder="请输入访问路径"
            />
          </div>
        )}

      <div className="space-y-2">
        <Label htmlFor="description">描述</Label>
        <Textarea
          id="description"
          value={formData.description}
          onChange={(e) => onChange({ ...formData, description: e.target.value })}
          placeholder="请输入描述信息"
          className="min-h-[100px]"
        />
      </div>

      <div className="flex justify-end gap-2 pt-4 border-t">
        <Button variant="outline" onClick={onCancel}>
          取消
        </Button>
        <Button onClick={onSubmit}>
          确定
        </Button>
      </div>
    </div>
  );
}; 