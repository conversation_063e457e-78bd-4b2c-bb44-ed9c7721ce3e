import {RoleTable} from "@/pages/settings/role/components/role-table";
import {useRequest} from "@@/exports";
import {Role, fetchRoles} from "@/service/role";
import {DataTableRowAction} from "@/components/data-table/types";
import React, {useEffect, useState} from "react";
import {RoleAuthSheet} from "@/pages/settings/role/components/role-auth-sheet";
import {RoleFormSheet} from "@/pages/settings/role/components/role-form-sheet";

export const RolePage = () => {
    const [rowAction, setRowAction] = useState<DataTableRowAction<Role> | null>(null);
    const [authDialogOpen, setAuthDialogOpen] = React.useState(false);
    const [formDialogOpen, setFormDialogOpen] = React.useState(false);

    const {run: fetchRolesRun, refresh, data} = useRequest(fetchRoles, {
        manual: true,
    })


    useEffect(() => {
        if (rowAction?.type === 'auth') {
            setAuthDialogOpen(true);
        } else if (rowAction?.type === 'update') {
            setFormDialogOpen(true);
        }
    }, [rowAction]);

    const handleAddRole = () => {
        setRowAction(null);
        setFormDialogOpen(true);
    };

    return <>
        <RoleTable
            data={data}
            onQuery={fetchRolesRun as any}
            setRowAction={setRowAction}
            onAddRole={handleAddRole}
        />
        <RoleAuthSheet
            open={authDialogOpen}
            onOpenChange={setAuthDialogOpen}
            roleId={rowAction?.row.original.id}
        />
        <RoleFormSheet
            open={formDialogOpen}
            onOpenChange={(open) => {
                setFormDialogOpen(open);
                if (!open) {
                    setRowAction(null);
                }
            }}
            role={rowAction?.row.original}
            onSuccess={() => {
                refresh();
            }}
        />
    </>
}

export default RolePage;