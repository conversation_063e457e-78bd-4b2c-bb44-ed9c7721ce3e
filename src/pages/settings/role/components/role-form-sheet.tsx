import React, {useEffect} from "react";
import {<PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON>etDescription, <PERSON>et<PERSON><PERSON><PERSON>, SheetTitle} from "@/components/ui/sheet";
import {But<PERSON>} from "@/components/ui/button";
import {Form, FormControl, FormField, FormItem, FormLabel, FormMessage} from "@/components/ui/form";
import {Input} from "@/components/ui/input";
import {useForm} from "react-hook-form";
import * as z from "zod";
import {zodResolver} from "@hookform/resolvers/zod";
import {Role, RoleSave, saveRole} from "@/service/role";
import {useRequest} from "umi";
import {toast} from "sonner";

const formSchema = z.object({
    code: z.string().min(1, "请输入角色编码"),
    name: z.string().min(1, "请输入角色名称"),
});

interface RoleFormSheetProps {
    open: boolean;
    onOpenChange: (open: boolean) => void;
    role?: Role;
    onSuccess?: () => void;
}

export function RoleFormSheet({open, onOpenChange, role, onSuccess}: RoleFormSheetProps) {
    const form = useForm<z.infer<typeof formSchema>>({
        resolver: zodResolver(formSchema),
        defaultValues: {
            code: "",
            name: "",
        },
    });

    useEffect(() => {
        if (open) {
            form.reset({
                code: role?.code || "",
                name: role?.name || "",
            });
        }
    }, [open, role]);

    const {loading, run: handleSave} = useRequest(
        (values: RoleSave) => saveRole(values),
        {
            manual: true,
            onSuccess: () => {
                toast.success(role ? "修改成功" : "新增成功");
                onOpenChange(false);
                form.reset();
                onSuccess?.();
            },
            onError: (error) => {
                toast.error((role ? "修改" : "新增") + "失败：" + error.message);
            }
        }
    );

    function onSubmit(values: z.infer<typeof formSchema>) {
        handleSave({
            id: role?.id,
            ...values,
        });
    }

    return (
        <Sheet open={open} onOpenChange={onOpenChange}>
            <SheetContent>
                <SheetHeader>
                    <SheetDescription/>
                    <SheetTitle>{role ? "修改角色" : "新增角色"}</SheetTitle>
                </SheetHeader>
                <div className="mt-6 p-4">
                    <Form {...form}>
                        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                            <FormField
                                control={form.control}
                                name="code"
                                render={({field}) => (
                                    <FormItem>
                                        <FormLabel>角色编码</FormLabel>
                                        <FormControl>
                                            <Input 
                                                placeholder="请输入角色编码" 
                                                {...field} 
                                                disabled={!!role}
                                            />
                                        </FormControl>
                                        <FormMessage/>
                                    </FormItem>
                                )}
                            />
                            <FormField
                                control={form.control}
                                name="name"
                                render={({field}) => (
                                    <FormItem>
                                        <FormLabel>角色名称</FormLabel>
                                        <FormControl>
                                            <Input placeholder="请输入角色名称" {...field} />
                                        </FormControl>
                                        <FormMessage/>
                                    </FormItem>
                                )}
                            />
                            <div className="flex justify-end">
                                <Button type="submit" disabled={loading}>
                                    {loading ? "保存中..." : "保存"}
                                </Button>
                            </div>
                        </form>
                    </Form>
                </div>
            </SheetContent>
        </Sheet>
    );
}
