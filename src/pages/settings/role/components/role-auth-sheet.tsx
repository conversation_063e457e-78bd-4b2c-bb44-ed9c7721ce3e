import React, {useState} from 'react';
import {Sheet, SheetContent, SheetDes<PERSON>, Sheet<PERSON>eader, SheetTitle,} from "@/components/ui/sheet";
import {<PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger} from "@/components/ui/tabs";
import {RoleAccountAuth} from "@/pages/settings/role/components/role-account-auth";
import {RolePermissionAuth} from "@/pages/settings/role/components/role-permission-auth";
import {Button} from "@/components/ui/button";
import {useRequest} from "umi";
import {roleAuthorization} from "@/service/role";
import {toast} from "sonner";

interface RoleAuthSheetProps {
    open: boolean;
    onOpenChange: (open: boolean) => void;
    roleId?: string;
    onSuccess?: () => void;
}

type AuthTab = "account" | "permission";

export function RoleAuthSheet({
    open,
    onOpenChange,
    roleId,
    onSuccess
}: RoleAuthSheetProps) {
    const [activeTab, setActiveTab] = useState<AuthTab>("permission");
    const [selectedAccountIds, setSelectedAccountIds] = useState<string[]>([]);
    const [selectedPermissionIds, setSelectedPermissionIds] = useState<string[]>([]);

    const {loading: submitting, run: submitAuthorization} = useRequest(
        (data: { id: string; accounts?: { id: string }[]; permissions?: { id: string }[] }) =>
            roleAuthorization(data),
        {
            manual: true,
            onSuccess: () => {
                toast.success("授权成功");
                onOpenChange(false);
                onSuccess?.();
            },
            onError: (error) => {
                toast.error("授权失败：" + error.message);
            }
        }
    );

    const handleSubmit = async () => {
        if (!roleId) {
            toast.error("角色ID不能为空");
            return;
        }
        
        switch (activeTab) {
            case "account":
                if (selectedAccountIds.length === 0) {
                    toast.error("请选择用户");
                    return;
                } else {
                    await submitAuthorization({
                        id: roleId,
                        accounts: selectedAccountIds.map(id => ({id})),
                    });
                }
                break;
            case "permission":
                if (selectedPermissionIds.length === 0) {
                    toast.error("请选择权限");
                    return;
                } else {
                    await submitAuthorization({
                        id: roleId,
                        permissions: selectedPermissionIds.map(id => ({id})),
                    });
                }
        }
    };

    const handleTabChange = (value: string) => {
        setActiveTab(value as AuthTab);
    };

    return (
        <Sheet open={open} onOpenChange={onOpenChange}>
            <SheetContent className="sm:max-w-dvw p-4 w-dvw">
                <SheetHeader>
                    <SheetDescription/>
                    <SheetTitle>角色授权</SheetTitle>
                </SheetHeader>
                <div className="h-full">
                    <Tabs value={activeTab} onValueChange={handleTabChange}>
                        <TabsList >
                            <TabsTrigger value="permission">权限授权</TabsTrigger>
                            <TabsTrigger value="account">用户授权</TabsTrigger>
                        </TabsList>
                        <TabsContent value="account">
                            {roleId && (
                                <RoleAccountAuth 
                                    roleId={roleId}
                                    onChange={data => setSelectedAccountIds(data.selectedIds)}
                                />
                            )}
                        </TabsContent>
                        <TabsContent value="permission">
                            {roleId && (
                                <RolePermissionAuth 
                                    roleId={roleId}
                                    onChange={data => setSelectedPermissionIds(data.selectedIds)}
                                />
                            )}
                        </TabsContent>
                    </Tabs>
                </div>
                <div className="flex justify-end gap-2">
                    <Button variant="outline" onClick={() => onOpenChange(false)}>
                        取消
                    </Button>
                    <Button onClick={handleSubmit} disabled={submitting}>
                        确定
                    </Button>
                </div>
            </SheetContent>
        </Sheet>
    );
}
