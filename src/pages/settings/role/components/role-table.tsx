import {DataTableFilter<PERSON>ield, DataTableRowAction} from "@/components/data-table/types";
import {useDataTable} from "@/components/data-table/hooks/use-data-table";
import React, {useCallback} from "react";
import {DataTable} from "@/components/data-table/data-table";
import {DataTableToolbar} from "@/components/data-table/data-table-toolbar";
import {Role, RoleSpecification} from "@/service/role";
import {Button} from "@/components/ui/button";
import {Plus} from "lucide-react";
import {getColumns} from "@/pages/settings/role/components/role-table-columns";
import {Page, PageQuery, Specification} from "@/types";
import {getActions} from "@/pages/settings/role/components/role-table-columns-action";
import {getFilterFields} from "@/pages/settings/role/components/role-table-filter";
import {useAccess} from "umi";

interface RoleTableProps {
    onQuery: (query: PageQuery<Specification<Role>>) => Promise<Page<Role>>;
    setRowAction: React.Dispatch<React.SetStateAction<DataTableRowAction<Role> | null>>;
    onAddRole?: () => void;
    data: Page<Role> | undefined;
}

export function RoleTable({onQuery, setRowAction, onAddRole, data}: RoleTableProps) {
    const access = useAccess();

    const actions = React.useMemo(() => getActions({setRowAction, access}), [setRowAction]);

    const columns = React.useMemo(() => getColumns({actions}), [actions]);
    const [filterFields, setFilterFields] = React.useState<DataTableFilterField<Role>[]>(getFilterFields());

    const {table} = useDataTable({
        data: data?.data || [],
        columns,
        pageCount: Math.ceil((data?.total || 0) / (data?.pageSize || 10)),
        filterFields,
        initialState: {
            sorting: [{id: "id", desc: true}],
            columnPinning: {right: ["actions"]},
            pagination: {
                pageIndex: 0,
                pageSize: 30,
            }
        },
        getRowId: (originalRow) => originalRow.id,
        shallow: false,
        clearOnDefault: true,
    });

    return (
        <>
            <DataTable table={table} className="h-fit">
                <DataTableToolbar 
                    table={table} 
                    filterFields={filterFields} 
                    onFilterChange={(pageQuery) => {
                        // Use type assertion to handle the type incompatibility
                        onQuery(pageQuery as any);
                    }}
                >
                    <Button
                        variant="outline"
                        size="sm"
                        className="ml-auto h-8"
                        onClick={onAddRole}
                    >
                        <Plus className="mr-2 h-4 w-4"/>
                        新增角色
                    </Button>
                </DataTableToolbar>
            </DataTable>
        </>
    )
}