import React from "react";
import {ColumnDef} from "@tanstack/react-table";
import {DataTableColumnHeader} from "@/components/data-table/data-table-column-header";
import {Role} from "@/service/role";

export interface RoleTableColumnProps {
    actions: ColumnDef<Role> | undefined;
}

export function getColumns({actions}: RoleTableColumnProps): ColumnDef<Role>[] {
    const columns: ColumnDef<Role>[] = [
        {
            accessorKey: "code",
            header: ({column}) => (
                <DataTableColumnHeader column={column}/>
            ),
            cell: ({row}) => <div className="w-20">{row.getValue("code")}</div>,
            enableSorting: false,
            enableHiding: true,
            meta: {
                title: "角色编码",
            },
        },
        {
            accessorKey: "name",
            header: ({column}) => (
                <DataTableColumnHeader column={column}/>
            ),
            cell: ({row}) => <div className="w-20">{row.getValue("name")}</div>,
            meta: {
                title: "角色名称",
            },
            enableSorting: false,
            enableHiding: true,
        },
    ];
    
    if (actions) {
        columns.push(actions);
    }
    
    return columns;
}