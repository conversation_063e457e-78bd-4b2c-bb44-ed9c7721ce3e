import {GroupTable} from "@/pages/settings/group/components/group-table";
import {history, useRequest} from "@@/exports";
import {Group, fetchGroups} from "@/service/group";
import {DataTableRowAction} from "@/components/data-table/types";
import React, {useEffect, useState} from "react";
import {GroupAuthSheet} from "@/pages/settings/group/components/group-auth-sheet";
import {GroupFormSheet} from "@/pages/settings/group/components/group-form-sheet";

export const GroupPage = () => {
    const [rowAction, setRowAction] = useState<DataTableRowAction<Group> | null>(null);
    const [authDialogOpen, setAuthDialogOpen] = React.useState(false);
    const [formDialogOpen, setFormDialogOpen] = React.useState(false);

    const {run: query, refresh,data} = useRequest(fetchGroups, {
        manual: true,
    })

    useEffect(() => {
        if (rowAction?.type === 'auth') {
            setAuthDialogOpen(true);
        } else if (rowAction?.type === 'update') {
            setFormDialogOpen(true);
        }
    }, [rowAction]);

    const handleAddGroup = () => {
        setRowAction(null);
        setFormDialogOpen(true);
    };

    return <>
        <GroupTable
            data={data}
            onQuery={query}
            setRowAction={setRowAction}
            onAddGroup={handleAddGroup}
        />
        <GroupAuthSheet
            open={authDialogOpen}
            onOpenChange={setAuthDialogOpen}
            groupId={rowAction?.row.original.id}
        />
        <GroupFormSheet
            open={formDialogOpen}
            onOpenChange={(open) => {
                setFormDialogOpen(open);
                if (!open) {
                    setRowAction(null);
                }
            }}
            group={rowAction?.row.original}
            onSuccess={() => {
                refresh();
            }}
        /></>
}

export default GroupPage;