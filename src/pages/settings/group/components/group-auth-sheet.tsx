import React, {useState} from "react";
import {Sheet, SheetContent, SheetDes<PERSON>, Sheet<PERSON>eader, SheetTitle} from "@/components/ui/sheet";
import {<PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, Ta<PERSON>Trigger} from "@/components/ui/tabs";
import {useRequest} from "@@/exports";
import {toast} from "sonner";
import {Button} from "@/components/ui/button";
import {GroupAuthorization, groupAuthorization} from "@/service/group";
import {GroupAccountAuth} from "@/pages/settings/group/components/group-account-auth";
import {GroupPermissionAuth} from "@/pages/settings/group/components/group-permission-auth";
import {GroupDynamicAuth} from "@/pages/settings/group/components/group-dynamic-auth";
import {Group as ConditionGroup} from "@/components/condition-react/types";
import {convertToSpel} from "@/components/condition-react/spel-converter";


type AuthTab = 'account' | 'permission' | 'dynamic';

interface GroupAuthSheetProps {
    open: boolean;
    onOpenChange: (open: boolean) => void;
    groupId?: string;
}

export function GroupAuthSheet({open, onOpenChange, groupId}: GroupAuthSheetProps) {
    const [activeTab, setActiveTab] = useState<AuthTab>("permission");

    const [selectedAccountIds, setSelectedAccountIds] = useState<string[]>([]);
    const [selectedPermissionIds, setSelectedPermissionIds] = useState<string[]>([]);

    // 动态授权条件状态
    const [conditionGroup, setConditionGroup] = useState<ConditionGroup>({
        conditions: [],
        groups: [],
        logic: "AND"
    });

    // 当动态授权条件变化时的处理函数
    const handleDynamicAuthChange = (data: { conditionGroup: ConditionGroup }) => {
        setConditionGroup(data.conditionGroup);
    };

    const {loading: submitting, run: submitAuthorization} = useRequest(
        (data: GroupAuthorization) =>
            groupAuthorization(data),
        {
            manual: true,
            onSuccess: () => {
                toast.success("授权成功");
                onOpenChange(false);
            },
            onError: (error) => {
                toast.error("授权失败：" + error.message);
            }
        }
    );

    const handleSubmit = async () => {
        if (!groupId) {
            toast.error("角色ID不能为空");
            return;
        }

        switch (activeTab) {
            case "account":
                if (selectedAccountIds.length === 0) {
                    toast.error("请选择用户");
                    return;
                } else {
                    await submitAuthorization({
                        id: groupId,
                        accounts: selectedAccountIds.map(id => ({id})),
                    });
                }
                break;
            case "permission":
                if (selectedPermissionIds.length === 0) {
                    toast.error("请选择权限");
                    return;
                } else {
                    await submitAuthorization({
                        id: groupId,
                        permissions: selectedPermissionIds.map(id => ({id})),
                    });
                }
                break;
            case "dynamic":
                // 检查条件组是否有效
                const hasValidConditions = conditionGroup.conditions.length > 0 || 
                                          conditionGroup.groups.some(g => g.conditions.length > 0 || g.groups.length > 0);
                
                if (!hasValidConditions) {
                    toast.error("请设置动态授权条件");
                    return;
                } else {
                    try {
                        const expression = convertToSpel(conditionGroup);
                        submitAuthorization({
                            id: groupId,
                            groupTerm: {
                                terms: conditionGroup,
                                expression: expression
                            }
                        });
                    } catch (error) {
                        console.error("转换动态条件失败:", error);
                        toast.error("动态条件设置有误，请检查");
                    }
                }
                break;
        }
    };

    const handleTabChange = (value: string) => {
        setActiveTab(value as AuthTab);
    };

    return (
        <Sheet open={open} onOpenChange={onOpenChange}>
            <SheetContent className="sm:max-w-dvw p-4 w-dvw">
                <SheetHeader>
                    <SheetDescription/>
                    <SheetTitle>角色授权</SheetTitle>
                </SheetHeader>
                <div className="h-full">
                    <Tabs value={activeTab} onValueChange={handleTabChange}>
                        <TabsList>
                            <TabsTrigger value="permission">权限授权</TabsTrigger>
                            <TabsTrigger value="account">用户授权</TabsTrigger>
                            <TabsTrigger value="dynamic">动态授权</TabsTrigger>
                        </TabsList>
                        <TabsContent value="account">
                            {groupId && (
                                <GroupAccountAuth
                                    groupId={groupId}
                                    onChange={data => setSelectedAccountIds(data.selectedIds)}
                                />
                            )}
                        </TabsContent>
                        <TabsContent value="permission">
                            {groupId && (
                                <GroupPermissionAuth
                                    groupId={groupId}
                                    onChange={data => setSelectedPermissionIds(data.selectedIds)}
                                />
                            )}
                        </TabsContent>
                        <TabsContent value="dynamic">
                            {groupId && (
                                <GroupDynamicAuth
                                    groupId={groupId}
                                    onChange={handleDynamicAuthChange}
                                />
                            )}
                        </TabsContent>
                    </Tabs>
                </div>
                <div className="flex justify-end gap-2">
                    <Button variant="outline" onClick={() => onOpenChange(false)}>
                        取消
                    </Button>
                    <Button onClick={handleSubmit} disabled={submitting}>
                        确定
                    </Button>
                </div>
            </SheetContent>
        </Sheet>
    );
}
