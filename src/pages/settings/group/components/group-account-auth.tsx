import React, {useEffect} from 'react';
import {Account, fetchAccounts} from "@/service/account";
import {DataTable} from "@/components/data-table/data-table";
import {DataTableToolbar} from "@/components/data-table/data-table-toolbar";
import {useLocalDataTable} from "@/components/data-table/hooks/use-local-data-table";
import {ColumnDef} from "@tanstack/react-table";
import {Button} from "@/components/ui/button";
import {DataTableColumnHeader} from "@/components/data-table/data-table-column-header";
import {useRequest} from "umi";
import {DataTableFilterField, Page} from "@/types";
import {Plus} from "lucide-react";
import {fetchGroupAccounts} from "@/service/group";

interface GroupAccountAuthData {
    selectedIds: string[];
}

interface GroupAccountAuthProps {
    groupId: string;
    onChange?: (data: GroupAccountAuthData) => void;
}

// 移除不再需要的 ref 接口

const getColumns = (onAdd?: (account: Account) => void): ColumnDef<Account>[] => {
    return [
        {
            accessorKey: "name",
            header: ({column}) => (
                <DataTableColumnHeader column={column} title="姓名"/>
            ),
            cell: ({row}) => <div className="w-[120px] truncate">{row.getValue("name")}</div>,
            size: 120,
        },
        {
            accessorKey: "email",
            header: ({column}) => (
                <DataTableColumnHeader column={column} title="邮箱"/>
            ),
            cell: ({row}) => <div className="w-[200px] truncate">{row.getValue("email")}</div>,
            size: 200,
        },
        ...(onAdd ? [{
            id: "actions",
            cell: ({row}: { row: { original: Account } }) => {
                return (
                    <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => onAdd(row.original)}
                    >
                        <Plus className="h-4 w-4"/>
                    </Button>
                );
            },
            size: 50,
        }] : []),
    ];
};

const filterFields: DataTableFilterField<Account>[] = [
    {
        id: "name",
        label: "姓名",
        placeholder: "搜索人员姓名",
    },
];

export function GroupAccountAuth({groupId, onChange}: GroupAccountAuthProps) {
    const [page, setPage] = React.useState(0);
    const [perPage, setPerPage] = React.useState(20);
    const [accounts, setAccounts] = React.useState<Page<Account>>({
        page: 0,
        pageSize: 20,
        total: 0,
        data: [],
    });
    const [authorizedAccounts, setAuthorizedAccounts] = React.useState<Account[]>([]);
    const [seaerchAuthorizedAccounts, setseaerchAuthorizedAccounts] = React.useState<Account[]>([]);

    const handleAddAccount = React.useCallback((account: Account) => {
        setAuthorizedAccounts(prev => {
            // 检查是否已经存在
            if (prev.some(a => a.id === account.id)) {
                return prev;
            }
            return [...prev, account];
        });
    }, []);

    const handleRemoveAccount = React.useCallback((accountId: string) => {
        setAuthorizedAccounts(prev => prev.filter(a => a.id !== accountId));
    }, []);

    const columnsWithAdd = React.useMemo(() => getColumns(handleAddAccount), []);
    const columnsWithRemove = React.useMemo(() => [
        ...getColumns(),
        {
            id: "actions",
            cell: ({row}: { row: { original: Account } }) => {
                return (
                    <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => handleRemoveAccount(row.original.id)}
                    >
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-4 w-4"><path d="M3 6h18"/><path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"/><path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"/></svg>
                    </Button>
                )
            },
        }
    ], []);





    // 左侧表格 - 所有用户
    const {table: allAccountsTable} = useLocalDataTable({
        pageCount: accounts.total / perPage,
        data: accounts.data,
        columns: columnsWithAdd,
        filterFields,
        initialState: {
            pagination: {
                pageIndex: page,
                pageSize: perPage,
            },
        },
        getRowId: (originalRow: Account) => originalRow.id,
        onPaginationChange: (updaterOrValue) => {
            const newState = typeof updaterOrValue === 'function'
                ? updaterOrValue({pageIndex: page, pageSize: perPage})
                : updaterOrValue;
            setPage(newState.pageIndex);
            setPerPage(newState.pageSize);
        },
    });

    // 右侧表格 - 已授权用户
    const {table: authorizedTable} = useLocalDataTable({
        data: seaerchAuthorizedAccounts,
        columns: columnsWithRemove,
        pageCount: 1,
        getRowId: (originalRow: Account) => originalRow.id,
    });



    useEffect(() => {
        if (authorizedTable.getState().columnFilters.length === 0) {
            setseaerchAuthorizedAccounts(authorizedAccounts);
        }else {
            const name = authorizedTable.getColumn("name")?.getFilterValue() as string;
            setseaerchAuthorizedAccounts(authorizedAccounts.filter(a => a.name.includes(name)));
        }
    }, [authorizedTable.getState().columnFilters, authorizedAccounts]);

    // 获取角色已有权限的用户列表
    const {data: authorizedAccountIds} = useRequest(() => {
        return fetchGroupAccounts(groupId)
    }, {
        onSuccess: async (res) => {
            // 避免不必要的状态更新
            setAuthorizedAccounts(prevAccounts => {
                // 检查是否真的有变化
                if (prevAccounts.length === res.length &&
                    prevAccounts.every(prev => res.some(curr => curr.id === prev.id))) {
                    return prevAccounts;
                }
                return res;
            });
        }
    });

    // 获取所有用户列表
    useRequest(
        () => {
            const username = allAccountsTable.getColumn("name")?.getFilterValue() as string;
            return fetchAccounts({
                page: page,
                pageSize: perPage,
                specification: {
                    realName: username,
                }
            })
        },
        {
            refreshDeps: [page, perPage, allAccountsTable.getState().columnFilters, authorizedAccountIds],
            onSuccess: (res) => {
                setAccounts(res);
            }
        }
    );

    // 当授权账户列表变化时，通知父组件
    // 使用 useRef 跟踪上一次的数据，避免不必要的更新
    const prevSelectedIdsRef = React.useRef<string[]>([]);

    useEffect(() => {
        const currentSelectedIds = authorizedAccounts.map(account => account.id);

        // 只有当选中的账户真正变化时才通知父组件
        if (onChange &&
            (prevSelectedIdsRef.current.length !== currentSelectedIds.length ||
                !prevSelectedIdsRef.current.every(id => currentSelectedIds.includes(id)))) {
            prevSelectedIdsRef.current = [...currentSelectedIds];
            onChange({
                selectedIds: currentSelectedIds
            });
        }
    }, [authorizedAccounts, onChange]);

    return (
        <div className="flex gap-4 h-full">
            <div className="flex-1 min-w-[600px]">
                <h3 className="mb-4 text-lg font-medium">待选用户</h3>
                <DataTable offsetBottom={40} table={allAccountsTable}>
                    <DataTableToolbar table={allAccountsTable} filterFields={filterFields}/>
                </DataTable>
            </div>
            <div className="flex-1 min-w-[600px]">
                <h3 className="mb-4 text-lg font-medium">已授权用户 ({authorizedAccounts.length})</h3>
                <DataTable offsetBottom={40} table={authorizedTable}  showPagination={false}>
                    <DataTableToolbar table={authorizedTable} filterFields={filterFields}/>
                </DataTable>
            </div>
        </div>
    );
}