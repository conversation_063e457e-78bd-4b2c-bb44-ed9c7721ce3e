import React from "react";
import type {DataTableRowAction} from "@/components/data-table/types";
import {ColumnDef} from "@tanstack/react-table";
import {DataTableColumnHeader} from "@/components/data-table/data-table-column-header";
import {Button} from "@/components/ui/button";
import {Key, Pencil, Trash2} from "lucide-react";
import {Group} from "@/service/group";
import {Account} from "@/service/account";
import {AccessInstance} from "umi";
import {Access} from "@@/exports";

export interface GroupTableColumnProps {
    setRowAction:  React.Dispatch<React.SetStateAction<DataTableRowAction<Group> | null>>;
    access: AccessInstance;
}

export function getActions({setRowAction, access}: GroupTableColumnProps): ColumnDef<Group> | undefined {
    const {hasAction} = access;
    if (true) {
        return {
            id: "actions",
            cell: ({row}) => {
                const role = row.original;

                return (
                    <div className="flex items-center gap-2">
                        <Button
                            variant="ghost"
                            className="h-8 w-8 p-0"
                            onClick={() => setRowAction({
                                type: "auth",
                                row: row
                            })}
                        >
                            <Key className="h-4 w-4"/>
                        </Button>
                        <Button
                            variant="ghost"
                            className="h-8 w-8 p-0"
                            onClick={() => setRowAction({
                                type: "update",
                                row: row
                            })}
                        >
                            <Pencil className="h-4 w-4"/>
                        </Button>
                        <Button
                            variant="ghost"
                            className="h-8 w-8 p-0"
                            onClick={() => setRowAction({
                                type: "delete",
                                row: row
                            })}
                        >
                            <Trash2 className="h-4 w-4"/>
                        </Button>
                    </div>
                );
            },
        }
    }

}