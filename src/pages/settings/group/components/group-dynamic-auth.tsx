import React, {useState, useEffect} from 'react';
import {ScrollArea} from "@/components/ui/scroll-area";
import {Skeleton} from "@/components/ui/skeleton";
import Terms from "@/components/condition-react/terms";
import {Field, Group as ConditionGroup} from "@/components/condition-react/types";
import {fetchTerms} from "@/service/group";
import {useRequest} from "@@/exports";

// 示例字段定义
const sampleFields: Field[] = [
    {
        fieldName: "账户",
        fieldCode: "userName",
        type: "text",
        children: []
    },
    {
        fieldName: "用户",
        fieldCode: "user",
        type: "object",
        children: [
            {
                fieldName: "姓名",
                fieldCode: "name",
                type: "text",
                children: [],
            },
            {
                fieldName: "用户编码",
                fieldCode: "code",
                type: "text",
                children: [],
            },
            {
                fieldName: "邮箱",
                fieldCode: "email",
                type: "text",
                children: [],
            }, {
                fieldName: "手机号",
                fieldCode: "phone",
                type: "text",
                children: [],
            }
        ],
    },
    {
        fieldName: "组织",
        fieldCode: "user?.allOrg",
        type: "array",
        children: [
            {
                fieldName: "组织编码",
                fieldCode: "code",
                type: "text",
                children: [],
            },
            {
                fieldName: "组织名称",
                fieldCode: "name",
                type: "text",
                children: [],
            }
        ],
    }
];

// 接口定义
interface GroupDynamicAuthData {
    conditionGroup: ConditionGroup;
}

interface GroupDynamicAuthProps {
    groupId: string;
    onChange?: (data: GroupDynamicAuthData) => void;
}

export function GroupDynamicAuth({groupId, onChange}: GroupDynamicAuthProps) {
    // 状态管理
    const [loading, setLoading] = useState(false);
    const [conditionGroup, setConditionGroup] = useState<ConditionGroup>({
        conditions: [],
        groups: [],
        logic: "AND"
    });

    // 加载动态授权条件
    const { data, loading: termsLoading } = useRequest(() => fetchTerms(groupId), {
        ready: !!groupId,
        onSuccess: (res) => {
            if (res) {
                console.log('加载到动态条件:', res);
                setConditionGroup(res);
                
                // 当条件加载后立即通知父组件
                if (onChange) {
                    onChange({
                        conditionGroup: res
                    });
                }
            }
        }
    });


    // 当条件组变化时通知父组件
    const handleConditionChange = (group: ConditionGroup) => {
        setConditionGroup(group);
        if (onChange) {
            onChange({
                conditionGroup: group
            });
        }
    };

    // 加载状态显示
    if (termsLoading) {
        return <Skeleton className="w-full h-[200px]"/>;
    }

    return (
        <div className="p-2 h-[calc(100vh-250px)]">
            <ScrollArea className="h-full w-full rounded-md border">
                <div className="p-2 bg-muted/20 rounded-md">
                    <Terms
                        group={conditionGroup}
                        fields={sampleFields}
                        root={true}
                        onChange={handleConditionChange}
                    />
                </div>
            </ScrollArea>
        </div>
    );
}
