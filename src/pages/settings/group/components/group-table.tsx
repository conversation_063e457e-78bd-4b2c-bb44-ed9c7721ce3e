import {DataTableFilter<PERSON>ield, DataTableRowAction} from "@/components/data-table/types";
import {useDataTable} from "@/components/data-table/hooks/use-data-table";
import React, {useCallback} from "react";
import {DataTable} from "@/components/data-table/data-table";
import {DataTableToolbar} from "@/components/data-table/data-table-toolbar";
import {useAccess} from "umi";
import {Button} from "@/components/ui/button";
import {Plus} from "lucide-react";
import {getColumns} from "@/pages/settings/group/components/group-table-columns";
import {Group} from "@/service/group";
import {Page, PageQuery, Specification} from "@/types";
import {getActions} from "@/pages/settings/group/components/group-table-columns-action";
import {getFilterFields} from "@/pages/settings/group/components/account-table-filter";


interface GroupTableProps {
    onQuery: (query: PageQuery<Specification<Group>>) => Promise<Page<Group>>;
    setRowAction: React.Dispatch<React.SetStateAction<DataTableRowAction<Group> | null>>;
    onAddGroup?: () => void;
    data:Page<Group> | undefined;
}

export function GroupTable({onQuery, setRowAction, onAddGroup, data}: GroupTableProps) {
    const access = useAccess();

    const actions = React.useMemo(() => getActions({setRowAction, access}), [setRowAction]);

    const columns = React.useMemo(() => getColumns({actions}), [actions]);
    const [filterFields, setFilterFields] = React.useState<DataTableFilterField<Group>[]>(getFilterFields());


    const {table} = useDataTable({
        data: data?.data || [],
        columns,
        pageCount: Math.ceil((data?.total || 0) / (data?.pageSize || 10)),
        filterFields,
        initialState: {
            sorting: [{id: "id", desc: true}],
            columnPinning: {right: ["actions"]},
            pagination: {
                pageIndex: 0,
                pageSize: 30,
            }
        },
        getRowId: (originalRow) => originalRow.id,
        shallow: false,
        clearOnDefault: true,
    });

    return (
        <>
            <DataTable table={table} className="h-fit">
                <DataTableToolbar table={table} filterFields={filterFields} onFilterChange={onQuery}>
                    <Button
                        variant="outline"
                        size="sm"
                        className="ml-auto h-8"
                        onClick={onAddGroup}
                    >
                        <Plus className="mr-2 h-4 w-4"/>
                        新增用户组
                    </Button>
                </DataTableToolbar>
            </DataTable>

        </>
    )
} 