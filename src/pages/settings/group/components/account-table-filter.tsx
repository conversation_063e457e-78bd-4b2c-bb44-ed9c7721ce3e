import React from "react";
import {ColumnDef} from "@tanstack/react-table";
import {DataTableColumnHeader} from "@/components/data-table/data-table-column-header";
import {Account} from "@/service/account";
import {Aggs, DataTableFilterField} from "@/types";
import {ProductCase} from "@/service/cases/product";
import {Group} from "@/service/group";

export interface AccountTableColumnProps {
    actions:ColumnDef<Account> | undefined;
}

export function getFilterFields(): DataTableFilterField<Group>[] {
   return [
        {
            id: "code",
            label: "编码",
            placeholder: "用户编码",
        },
        {
            id: "name",
            label: "名称",
            placeholder: "用户名称",
        },
    ];
}