import React, {useEffect} from "react";
import {<PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, SheetDescription, <PERSON>etHeader, SheetTitle} from "@/components/ui/sheet";
import {But<PERSON>} from "@/components/ui/button";
import {Form, FormControl, FormField, FormItem, FormLabel, FormMessage} from "@/components/ui/form";
import {Input} from "@/components/ui/input";
import {useForm} from "react-hook-form";
import * as z from "zod";
import {zodResolver} from "@hookform/resolvers/zod";
import {useRequest} from "umi";
import {Group, GroupSave, saveGroup} from "@/service/group";

const formSchema = z.object({
    code: z.string().min(1, "请输入用户组编码"),
    name: z.string().min(1, "请输入用户组名称"),
});

interface GroupFormDialogProps {
    open: boolean;
    onOpenChange: (open: boolean) => void;
    group?: Group;
    onSuccess?: () => void;
}

export function GroupFormSheet({open, onOpenChange, group, onSuccess}: GroupFormDialogProps) {
    const form = useForm<z.infer<typeof formSchema>>({
        resolver: zodResolver(formSchema),
        defaultValues: {
            code: "",
            name: "",
        },
    });

    useEffect(() => {
        if (open) {
            form.reset({
                code: group?.code || "",
                name: group?.name || "",
            });
        }
    }, [open, group]);

    const {loading, run: handleSave} = useRequest(
        (values: GroupSave) => saveGroup(values),
        {
            manual: true,
            onSuccess: () => {
                onOpenChange(false);
                form.reset();
                onSuccess?.();
            },
        }
    );

    function onSubmit(values: z.infer<typeof formSchema>) {
        handleSave({
            id: group?.id,
            ...values,
        });
    }

    return (
        <Sheet open={open} onOpenChange={onOpenChange}>
            <SheetContent>
                <SheetHeader>
                    <SheetDescription/>
                    <SheetTitle>{group ? "修改用户组" : "新增用户组"}</SheetTitle>
                </SheetHeader>
                <div className="mt-6 p-4">
                    <Form {...form}>
                        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                            <FormField
                                control={form.control}
                                name="code"
                                render={({field}) => (
                                    <FormItem>
                                        <FormLabel>用户组编码</FormLabel>
                                        <FormControl>
                                            <Input 
                                                placeholder="请输入用户组编码"
                                                {...field} 
                                                disabled={!!group}
                                            />
                                        </FormControl>
                                        <FormMessage/>
                                    </FormItem>
                                )}
                            />
                            <FormField
                                control={form.control}
                                name="name"
                                render={({field}) => (
                                    <FormItem>
                                        <FormLabel>用户组名称</FormLabel>
                                        <FormControl>
                                            <Input placeholder="请输入用户组名称" {...field} />
                                        </FormControl>
                                        <FormMessage/>
                                    </FormItem>
                                )}
                            />
                            <div className="flex justify-end">
                                <Button type="submit" disabled={loading}>
                                    {loading ? "保存中..." : "保存"}
                                </Button>
                            </div>
                        </form>
                    </Form>
                </div>
            </SheetContent>
        </Sheet>
    );
} 