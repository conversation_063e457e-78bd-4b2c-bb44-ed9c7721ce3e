import React from "react";
import type {DataTableRowAction} from "@/components/data-table/types";
import {ColumnDef} from "@tanstack/react-table";
import {DataTableColumnHeader} from "@/components/data-table/data-table-column-header";
import {Group} from "@/service/group";

export interface GroupTableColumnProps {
    actions:ColumnDef<Group> | undefined;
}

    export function getColumns({actions}: GroupTableColumnProps): ColumnDef<Group>[] {
    const columns: ColumnDef<Group>[]= [
        {
            accessorKey: "code",
            header: ({column}) => (
                <DataTableColumnHeader column={column}/>
            ),
            cell: ({row}) => <div className="w-20">{row.getValue("code")}</div>,
            enableSorting: false,
            enableHiding: true,
            meta: {
                title: "用户组编码",
            },
        },
        {
            accessorKey: "name",
            header: ({column}) => (
                <DataTableColumnHeader column={column}/>
            ),
            cell: ({row}) => <div className="w-20">{row.getValue("name")}</div>,
            meta: {
                title: "用户组名称",
            },
            enableSorting: false,
            enableHiding: true,
        },
    ];
    if (actions){
        columns.push(actions)
    }
    return columns;
} 