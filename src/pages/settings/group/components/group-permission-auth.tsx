import React, {useEffect, useState} from 'react';
import {fetchPermission, Permission, PermissionType} from '@/service/auth';
import {Skeleton} from '@/components/ui/skeleton';
import {useRequest} from 'umi';
import {Checkbox} from "@/components/ui/checkbox";
import {ScrollArea} from "@/components/ui/scroll-area";
import {fetchGroupPermissions} from "@/service/group";
import {Badge} from "@/components/ui/badge";
import {Layers, Settings, Menu} from "lucide-react";
import {Tooltip, TooltipContent, TooltipProvider, TooltipTrigger} from "@/components/ui/tooltip";

interface GroupPermissionAuthData {
    selectedIds: string[];
}

interface GroupPermissionAuthProps {
    groupId: string;
    onChange?: (data: GroupPermissionAuthData) => void;
}

export function GroupPermissionAuth({groupId, onChange}: GroupPermissionAuthProps) {

    const [selectedIds, setSelectedIds] = useState<string[]>([]);
    const [permissions, setPermissions] = useState<Permission[]>([]);

    // 获取所有权限列表
    const { loading: permissionsLoading } = useRequest(fetchPermission, {
        onSuccess: res => {
            console.log('权限列表:', res);
            setPermissions(res);
        }
    });

    // 获取角色已有权限
    const { loading: groupPermissionsLoading } = useRequest(() => fetchGroupPermissions(groupId), {
        ready: !!groupId,
        onSuccess: (res) => {
            setSelectedIds(res);
        }
    });

    // 当选中的权限变化时，通知父组件
    // 使用 useRef 来跟踪上一次的 selectedIds，避免不必要的更新
    const prevSelectedIdsRef = React.useRef<string[]>([]);

    useEffect(() => {
        // 只有当 selectedIds 真正变化时才通知父组件
        if (onChange &&
            (prevSelectedIdsRef.current.length !== selectedIds.length ||
                !prevSelectedIdsRef.current.every(id => selectedIds.includes(id)))) {
            prevSelectedIdsRef.current = [...selectedIds];
            onChange({
                selectedIds
            });
        }
    }, [selectedIds, onChange]);

    // 获取所有父节点的ID
    const getAllParentIds = (permissionId: string, allPermissions: Permission[]): string[] => {
        const parentIds: string[] = [];

        const findParent = (currentId: string) => {
            for (const permission of allPermissions) {
                if (permission.children?.some(child => child.id === currentId)) {
                    parentIds.push(permission.id);
                    findParent(permission.id);
                }
                if (permission.children) {
                    for (const child of permission.children) {
                        if (child.children?.some(grandChild => grandChild.id === currentId)) {
                            parentIds.push(child.id);
                            findParent(child.id);
                        }
                    }
                }
            }
        };

        findParent(permissionId);
        return parentIds;
    };

    // 获取所有子节点的ID
    const getAllChildIds = (permission: Permission): string[] => {
        const childIds: string[] = [];

        const traverse = (item: Permission) => {
            if (item.children) {
                for (const child of item.children) {
                    childIds.push(child.id);
                    traverse(child);
                }
            }
        };

        traverse(permission);
        return childIds;
    };

    // 查找指定ID的权限节点
    const findPermissionById = (id: string, items: Permission[]): Permission | null => {
        for (const item of items) {
            if (item.id === id) return item;
            if (item.children) {
                const found = findPermissionById(id, item.children);
                if (found) return found;
            }
        }
        return null;
    };

    // 使用 useRef 来避免在渲染期间重新创建函数
    const handlePermissionChange = React.useCallback((checked: boolean, permissionId: string) => {
        console.log('点击权限:', permissionId, '状态:', checked);
        setSelectedIds(prev => {
            // 检查是否已经处于目标状态，如果是则直接返回原状态，避免不必要的更新
            const alreadyChecked = prev.includes(permissionId);
            if ((checked && alreadyChecked) || (!checked && !alreadyChecked)) {
                return prev;
            }

            let newSelectedIds = [...prev];

            if (checked) {
                // 添加当前节点
                newSelectedIds.push(permissionId);

                // 添加所有父节点
                const parentIds = getAllParentIds(permissionId, permissions);
                console.log('父节点IDs:', parentIds);
                newSelectedIds = [...new Set([...newSelectedIds, ...parentIds])];

                // 添加所有子节点
                const permission = findPermissionById(permissionId, permissions);
                if (permission) {
                    const childIds = getAllChildIds(permission);
                    console.log('子节点IDs:', childIds);
                    newSelectedIds = [...new Set([...newSelectedIds, ...childIds])];
                }
            } else {
                // 移除当前节点
                newSelectedIds = newSelectedIds.filter(id => id !== permissionId);

                // 移除所有子节点
                const permission = findPermissionById(permissionId, permissions);
                if (permission) {
                    const childIds = getAllChildIds(permission);
                    console.log('要移除的子节点IDs:', childIds);
                    newSelectedIds = newSelectedIds.filter(id => !childIds.includes(id));
                }

                // 检查是否需要移除父节点（当父节点的所有子节点都未选中时）
                const parentIds = getAllParentIds(permissionId, permissions);
                console.log('要检查的父节点IDs:', parentIds);
                for (const parentId of parentIds) {
                    const parent = findPermissionById(parentId, permissions);
                    if (parent && parent.children) {
                        const allChildIds = getAllChildIds(parent);
                        const hasSelectedChild = allChildIds.some(childId =>
                            newSelectedIds.includes(childId)
                        );
                        if (!hasSelectedChild) {
                            newSelectedIds = newSelectedIds.filter(id => id !== parentId);
                        }
                    }
                }
            }

            console.log('最终选中的IDs:', newSelectedIds);
            return newSelectedIds;
        });
    }, [permissions]);

    // 获取权限类型的图标和颜色
    const getPermissionTypeInfo = (type: PermissionType) => {
        switch (type) {
            case 'WORKSPACE':
                return { icon: <Layers className="h-3 w-3" />, color: 'bg-blue-100 text-blue-800', label: '工作区' };
            case 'ACTION':
                return { icon: <Settings className="h-3 w-3" />, color: 'bg-amber-100 text-amber-800', label: '操作' };
            case 'MENU':
                return { icon: <Menu className="h-3 w-3" />, color: 'bg-green-100 text-green-800', label: '菜单' };
            default:
                return { icon: null, color: '', label: '未知' };
        }
    };

    // 使用 useCallback 记忆化渲染函数，避免不必要的重新创建
    const renderPermissionItem = React.useCallback((permission: Permission) => {
        const typeInfo = getPermissionTypeInfo(permission.type);

        return (
            <div key={permission.id} className="space-y-2">
                <div className="flex items-center space-x-2">
                    <Checkbox
                        id={permission.id}
                        checked={selectedIds.includes(permission.id)}
                        onCheckedChange={(checked) => {
                            if (checked !== 'indeterminate') { // 确保只处理布尔值
                                console.log('Checkbox 点击:', permission.id, checked);
                                handlePermissionChange(checked, permission.id);
                            }
                        }}
                    />
                    <div className="flex items-center gap-2">
                        <TooltipProvider>
                            <Tooltip>
                                <TooltipTrigger asChild>
                                    <Badge variant="outline" className={`${typeInfo.color} text-xs px-1.5 py-0.5 h-5 flex items-center gap-1`}>
                                        {typeInfo.icon}
                                        <span className="text-xs">{typeInfo.label}</span>
                                    </Badge>
                                </TooltipTrigger>
                                <TooltipContent>
                                    <p>{permission.type === 'ACTION' ? 'ACTION类型可以有上级菜单，但不能添加子菜单' :
                                        permission.type === 'MENU' ? '菜单类型，可以包含子菜单或操作' :
                                            '工作区类型，顶级权限分类'}</p>
                                </TooltipContent>
                            </Tooltip>
                        </TooltipProvider>
                        <label
                            htmlFor={permission.id}
                            className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                        >
                            {permission.name}
                            {permission.href && <span className="text-xs text-gray-500 ml-1">({permission.href})</span>}
                        </label>
                    </div>
                </div>
                {permission.children && permission.children.length > 0 && (
                    <div className="ml-6 space-y-2">
                        {permission.children.map(child => renderPermissionItem(child))}
                    </div>
                )}
            </div>
        );
    }, [selectedIds, handlePermissionChange]);

    if (permissionsLoading || groupPermissionsLoading) {
        return <Skeleton className="w-full h-[200px]" />;
    }

    return (
        <div className="p-4">
            <div className="mb-4 flex gap-3">
                <Badge variant="outline" className="bg-blue-100 text-blue-800 flex items-center gap-1">
                    <Layers className="h-3 w-3" />
                    <span>工作区</span>
                </Badge>
                <Badge variant="outline" className="bg-green-100 text-green-800 flex items-center gap-1">
                    <Menu className="h-3 w-3" />
                    <span>菜单</span>
                </Badge>
                <Badge variant="outline" className="bg-amber-100 text-amber-800 flex items-center gap-1">
                    <Settings className="h-3 w-3" />
                    <span>操作</span>
                </Badge>
            </div>
            <ScrollArea className="h-[400px] w-full rounded-md border p-4">
                <div className="space-y-4">
                    {permissions.map(permission => renderPermissionItem(permission))}
                </div>
            </ScrollArea>
        </div>
    );
};