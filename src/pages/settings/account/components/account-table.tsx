import {DataTableFilterField, DataTableRowAction} from "@/components/data-table/types";
import {useDataTable} from "@/components/data-table/hooks/use-data-table";
import React from "react";
import {DataTable} from "@/components/data-table/data-table";
import {DataTableToolbar} from "@/components/data-table/data-table-toolbar";
import {Account} from "@/service/account";
import {Page, PageQuery, Specification} from "@/types";
import {useAccess} from "@@/exports";
import {getActions} from "./account-table-columns-actions";
import {getFilterFields} from "@/pages/settings/account/components/account-table-filter";
import {getColumns} from "./account-table-columns";

interface AccountTableProps {
    onQuery: (query: PageQuery<Specification<Account>>) => Promise<Page<Account>>;
    setRowAction:  React.Dispatch<React.SetStateAction<DataTableRowAction<Account> | null>>;
    data:Page<Account> | undefined;
}

export function AccountTable({onQuery, setRowAction,data}: AccountTableProps) {
    const access = useAccess();

    const actions = React.useMemo(() => getActions({setRowAction, access}), [setRowAction]);

    const columns = React.useMemo(() => getColumns({actions}), [actions]);
    const [filterFields] = React.useState<DataTableFilterField<Account>[]>(getFilterFields());


    const {table} = useDataTable({
        data: data?.data || [],
        columns,
        pageCount: Math.ceil((data?.total || 0) / (data?.pageSize || 10)),
        filterFields,
        initialState: {
            sorting: [{id: "id", desc: true}],
            columnPinning: {right: ["actions"]},
            pagination: {
                pageIndex: 0,
                pageSize: 30,
            }
        },
        getRowId: (originalRow) => originalRow.id,
        shallow: false,
        clearOnDefault: true,
    });


    return (
        <>
            <DataTable table={table} className="h-fit">
                <DataTableToolbar onFilterChange={onQuery} table={table} filterFields={filterFields}/>
            </DataTable>
        </>
    )
}