import React from "react";
import {ColumnDef} from "@tanstack/react-table";
import {DataTableColumnHeader} from "@/components/data-table/data-table-column-header";
import {Account} from "@/service/account";
import {Aggs, DataTableFilterField} from "@/types";
import {ProductCase} from "@/service/cases/product";

export interface AccountTableColumnProps {
    actions:ColumnDef<Account> | undefined;
}

export function getFilterFields(): DataTableFilterField<Account>[] {
   return [
        {
            id: "code",
            label: "编码",
            placeholder: "用户编码",
        }
    ];
}