import React from "react";
import {ColumnDef} from "@tanstack/react-table";
import {DataTableColumnHeader} from "@/components/data-table/data-table-column-header";
import {Account} from "@/service/account";

export interface AccountTableColumnProps {
    actions:ColumnDef<Account> | undefined;
}

export function getColumns({actions}: AccountTableColumnProps): ColumnDef<Account>[] {
    const columns:ColumnDef<Account>[]= [
        {
            accessorKey: "code",
            header: ({column}) => (
                <DataTableColumnHeader column={column}/>
            ),
            cell: ({row}) => <div className="w-[80px] truncate">{row.getValue("code")}</div>,
            enableSorting: false,
            enableHiding: true,
            meta: {
                title: "用户编码",
            },
        },
        {
            accessorKey: "name",
            header: ({column}) => (
                <DataTableColumnHeader column={column}/>
            ),
            cell: ({row}) => <div className="w-[160px] truncate">{row.getValue("name")}</div>,
            meta: {
                title: "用户名称",
            },
            enableSorting: false,
            enableHiding: true,
        },
        {
            accessorKey: "phone",
            header: ({column}) => (
                <DataTableColumnHeader column={column}/>
            ),
            cell: ({row}) => <div className="w-[120px] truncate">{row.getValue("phone")}</div>,
            meta: {
                title: "手机号",
            },
            enableSorting: false,
            enableHiding: true,
        },
        {
            accessorKey: "email",
            header: ({column}) => (
                <DataTableColumnHeader column={column}/>
            ),
            cell: ({row}) => <div className="w-[200px] truncate">{row.getValue("email")}</div>,
            meta: {
                title: "邮箱",
            },
            enableSorting: false,
            enableHiding: true,
        }
    ];
    if (actions){
        columns.push(actions)
    }
    return columns;
}