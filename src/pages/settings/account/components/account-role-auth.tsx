import React from "react";
import {Account, accountAuthorization, fetchAccountRoles} from "@/service/account";
import {fetchRoles, Role} from "@/service/role";
import {useRequest} from "umi";
import {DataTable} from "@/components/data-table/data-table";
import {DataTableToolbar} from "@/components/data-table/data-table-toolbar";
import {useLocalDataTable} from "@/components/data-table/hooks/use-local-data-table";
import {ColumnDef} from "@tanstack/react-table";
import {DataTableColumnHeader} from "@/components/data-table/data-table-column-header";
import {Button} from "@/components/ui/button";
import {Plus, Trash2} from "lucide-react";
import {Page} from "@/types";

interface AccountRoleAuthProps {
    account?: Account;
    onOpenChange: (open: boolean) => void;
}

const getColumns = (onAdd?: (role: Role) => void): ColumnDef<Role>[] => {
    return [
        {
            accessorKey: "code",
            header: ({column}) => (
                <DataTableColumnHeader column={column} title="编码"/>
            ),
            cell: ({row}) => <div className="w-[80px] truncate">{row.getValue("code")}</div>,
            size: 80,
        },
        {
            accessorKey: "name",
            header: ({column}) => (
                <DataTableColumnHeader column={column} title="名称"/>
            ),
            cell: ({row}) => <div className="w-[160px] truncate">{row.getValue("name")}</div>,
            size: 160,
        },
        ...(onAdd ? [{
            id: "actions",
            cell: ({row}: { row: { original: Role } }) => {
                return (
                    <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => onAdd(row.original)}
                    >
                        <Plus className="h-4 w-4"/>
                    </Button>
                );
            },
            size: 50,
        }] : []),
    ];
};

export function AccountRoleAuth({account, onOpenChange}: AccountRoleAuthProps) {
    const [page, setPage] = React.useState(0);
    const [perPage, setPerPage] = React.useState(20);
    const [roles, setRoles] = React.useState<Page<Role>>({
        page: 0,
        pageSize: 20,
        total: 0,
        data: [],
    });
    const [authorizedRoles, setAuthorizedRoles] = React.useState<Role[]>([]);

    // 获取所有角色列表
    const {run: fetchRoleList} = useRequest(
        () => fetchRoles({
            page: page,
            pageSize: perPage,
            specification: {
                code: '',
                name: ''
            }
        }),
        {
            manual: true,
            onSuccess: (res) => {
                setRoles(res);
            }
        }
    );

    // 获取已授权角色
    const {run: fetchAuthorizedRoles} = useRequest(
        () => fetchAccountRoles(account?.id || ""),
        {
            manual: true,
            onSuccess: (res) => {
                setAuthorizedRoles(res);
            }
        }
    );

    // 监听组件挂载，触发查询
    React.useEffect(() => {
        fetchRoleList();
        if (account?.id) {
            fetchAuthorizedRoles();
        }
        return () => {
            // 组件卸载时重置状态
            setPage(0);
            setPerPage(20);
            setRoles({
                page: 0,
                pageSize: 20,
                total: 0,
                data: [],
            });
            setAuthorizedRoles([]);
        };
    }, [account?.id]);

    const handleAddRole = (role: Role) => {
        setAuthorizedRoles(prev => {
            if (prev.some(r => r.id === role.id)) {
                return prev;
            }
            return [...prev, role];
        });
    };

    const handleRemoveRole = (roleId: string) => {
        setAuthorizedRoles(prev => prev.filter(r => r.id !== roleId));
    };

    const columnsWithAdd = React.useMemo(() => getColumns(handleAddRole), []);
    const columnsWithRemove = React.useMemo(() => [
        ...getColumns(),
        {
            id: "actions",
            cell: ({row}: { row: { original: Role } }) => {
                return (
                    <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => handleRemoveRole(row.original.id)}
                    >
                        <Trash2 className="h-4 w-4"/>
                    </Button>
                );
            },
            size: 50,
        }
    ], []);

    // 保存授权
    const {loading, run: handleSave} = useRequest(
        () => accountAuthorization({
            id: account?.id || "",
            roles: authorizedRoles.map(role => ({id: role.id}))
        }),
        {
            manual: true,
            onSuccess: () => {
                onOpenChange(false);
            }
        }
    );

    const handlePaginationChange = React.useCallback((updaterOrValue: any) => {
        const newState = typeof updaterOrValue === 'function'
            ? updaterOrValue({pageIndex: page, pageSize: perPage})
            : updaterOrValue;
        setPage(newState.pageIndex);
        setPerPage(newState.pageSize);
        // 触发查询
        fetchRoleList();
    }, [page, perPage, fetchRoleList]);

    const {table: allRolesTable} = useLocalDataTable({
        data: roles.data,
        columns: columnsWithAdd,
        pageCount: Math.ceil(roles.total / perPage),
        initialState: {
            pagination: {
                pageIndex: page,
                pageSize: perPage,
            }
        },
        getRowId: (originalRow: Role) => originalRow.id,
        onPaginationChange: handlePaginationChange,
    });

    const {table: authorizedTable} = useLocalDataTable({
        data: authorizedRoles,
        columns: columnsWithRemove,
        pageCount: 1,
        getRowId: (originalRow: Role) => originalRow.id,
    });

    return (
        <div className="p-4">
            <div className="flex gap-4">
                <div className="flex-1">
                    <h3 className="mb-4 text-lg font-medium">待选角色</h3>
                    <div className="overflow-hidden">
                        <DataTable table={allRolesTable}>
                            <DataTableToolbar table={allRolesTable}/>
                        </DataTable>
                    </div>
                </div>
                <div className="flex-1">
                    <h3 className="mb-4 text-lg font-medium">已授权角色 ({authorizedRoles.length})</h3>
                    <div className="overflow-hidden">
                        <DataTable table={authorizedTable} showPagination={false}>
                            <DataTableToolbar table={authorizedTable}/>
                        </DataTable>
                    </div>
                </div>
            </div>
            <div className="mt-6 flex justify-end">
                <Button onClick={() => handleSave()} disabled={loading}>
                    {loading ? "保存中..." : "保存"}
                </Button>
            </div>
        </div>
    );
} 