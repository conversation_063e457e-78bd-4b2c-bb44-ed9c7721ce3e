import React from "react";
import {Account, accountAuthorization, fetchAccountGroups} from "@/service/account";
import {fetchGroups, Group} from "@/service/group";
import {useRequest} from "umi";
import {DataTable} from "@/components/data-table/data-table";
import {DataTableToolbar} from "@/components/data-table/data-table-toolbar";
import {useLocalDataTable} from "@/components/data-table/hooks/use-local-data-table";
import {ColumnDef} from "@tanstack/react-table";
import {DataTableColumnHeader} from "@/components/data-table/data-table-column-header";
import {Button} from "@/components/ui/button";
import {Plus, Trash2} from "lucide-react";
import {Page} from "@/types";

interface AccountGroupAuthProps {
    account?: Account;
    onOpenChange: (open: boolean) => void;
}

const getColumns = (onAdd?: (group: Group) => void): ColumnDef<Group>[] => {
    return [
        {
            accessorKey: "code",
            header: ({column}) => (
                <DataTableColumnHeader column={column} title="编码"/>
            ),
            cell: ({row}) => <div className="w-[80px] truncate">{row.getValue("code")}</div>,
            size: 80,
        },
        {
            accessorKey: "name",
            header: ({column}) => (
                <DataTableColumnHeader column={column} title="名称"/>
            ),
            cell: ({row}) => <div className="w-[160px] truncate">{row.getValue("name")}</div>,
            size: 160,
        },
        ...(onAdd ? [{
            id: "actions",
            cell: ({row}: { row: { original: Group } }) => {
                return (
                    <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => onAdd(row.original)}
                    >
                        <Plus className="h-4 w-4"/>
                    </Button>
                );
            },
            size: 50,
        }] : []),
    ];
};

export function AccountGroupAuth({account, onOpenChange}: AccountGroupAuthProps) {
    const [page, setPage] = React.useState(0);
    const [perPage, setPerPage] = React.useState(20);
    const [groups, setGroups] = React.useState<Page<Group>>({
        page: 0,
        pageSize: 20,
        total: 0,
        data: [],
    });
    const [authorizedGroups, setAuthorizedGroups] = React.useState<Group[]>([]);

    // 获取所有用户组列表
    const {run: fetchGroupList} = useRequest(
        () => fetchGroups({
            page: page,
            pageSize: perPage,
            specification: {
                code: '',
                name: ''
            }
        }),
        {
            manual: true,
            onSuccess: (res) => {
                setGroups(res);
            }
        }
    );

    // 获取已授权用户组
    const {run: fetchAuthorizedGroups} = useRequest(
        () => fetchAccountGroups(account?.id || ""),
        {
            manual: true,
            onSuccess: (res) => {
                setAuthorizedGroups(res);
            }
        }
    );

    // 监听组件挂载，触发查询
    React.useEffect(() => {
        fetchGroupList();
        if (account?.id) {
            fetchAuthorizedGroups();
        }
        return () => {
            // 组件卸载时重置状态
            setPage(0);
            setPerPage(20);
            setGroups({
                page: 0,
                pageSize: 20,
                total: 0,
                data: [],
            });
            setAuthorizedGroups([]);
        };
    }, [account?.id]);

    const handleAddGroup = (group: Group) => {
        setAuthorizedGroups(prev => {
            if (prev.some(g => g.id === group.id)) {
                return prev;
            }
            return [...prev, group];
        });
    };

    const handleRemoveGroup = (groupId: string) => {
        setAuthorizedGroups(prev => prev.filter(g => g.id !== groupId));
    };

    const columnsWithAdd = React.useMemo(() => getColumns(handleAddGroup), []);
    const columnsWithRemove = React.useMemo(() => [
        ...getColumns(),
        {
            id: "actions",
            cell: ({row}: { row: { original: Group } }) => {
                return (
                    <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => handleRemoveGroup(row.original.id)}
                    >
                        <Trash2 className="h-4 w-4"/>
                    </Button>
                );
            },
            size: 50,
        }
    ], []);

    // 保存授权
    const {loading, run: handleSave} = useRequest(
        () => accountAuthorization({
            id: account?.id || "",
            groups: authorizedGroups.map(group => ({id: group.id}))
        }),
        {
            manual: true,
            onSuccess: () => {
                onOpenChange(false);
            }
        }
    );

    const handlePaginationChange = React.useCallback((updaterOrValue: any) => {
        const newState = typeof updaterOrValue === 'function'
            ? updaterOrValue({pageIndex: page, pageSize: perPage})
            : updaterOrValue;
        setPage(newState.pageIndex);
        setPerPage(newState.pageSize);
        // 触发查询
        fetchGroupList();
    }, [page, perPage, fetchGroupList]);

    const {table: allGroupsTable} = useLocalDataTable({
        data: groups.data,
        columns: columnsWithAdd,
        pageCount: Math.ceil(groups.total / perPage),
        initialState: {
            pagination: {
                pageIndex: page,
                pageSize: perPage,
            }
        },
        getRowId: (originalRow: Group) => originalRow.id,
        onPaginationChange: handlePaginationChange,
    });

    const {table: authorizedTable} = useLocalDataTable({
        data: authorizedGroups,
        columns: columnsWithRemove,
        pageCount: 1,
        getRowId: (originalRow: Group) => originalRow.id,
    });

    return (
        <div className="p-4">
            <div className="flex gap-4">
                <div className="flex-1">
                    <h3 className="mb-4 text-lg font-medium">待选用户组</h3>
                    <div className="overflow-hidden">
                        <DataTable table={allGroupsTable}>
                            <DataTableToolbar table={allGroupsTable}/>
                        </DataTable>
                    </div>
                </div>
                <div className="flex-1">
                    <h3 className="mb-4 text-lg font-medium">已授权用户组 ({authorizedGroups.length})</h3>
                    <div className="overflow-hidden">
                        <DataTable table={authorizedTable} showPagination={false}>
                            <DataTableToolbar table={authorizedTable}/>
                        </DataTable>
                    </div>
                </div>
            </div>
            <div className="mt-6 flex justify-end">
                <Button onClick={() => handleSave()} disabled={loading}>
                    {loading ? "保存中..." : "保存"}
                </Button>
            </div>
        </div>
    );
} 