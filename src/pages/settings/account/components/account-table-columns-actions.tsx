import React from "react";
import {ColumnDef} from "@tanstack/react-table";
import {Account} from "@/service/account";
import {Button} from "@/components/ui/button";
import {Key} from "lucide-react";
import {Access, AccessInstance} from "umi";
import type {DataTableRowAction} from "@/components/data-table/types";
import {ProductCase} from "@/service/cases/product";

export interface AccountTableColumnProps {
    setRowAction:  React.Dispatch<React.SetStateAction<DataTableRowAction<Account> | null>>;
    access: AccessInstance;
}

export function getActions({setRowAction, access}: AccountTableColumnProps): ColumnDef<Account> | undefined {
    const {hasAction} = access;
    if (hasAction('SETTINGS:ACCOUNT')) {
        return {
            id: "actions",
            cell: ({row}) => {
                const account = row.original;
                return (
                    <Access accessible={hasAction('SETTINGS:ACCOUNT:AUTHORIZATION')}>
                        <div className="flex items-center gap-2">
                            <Button
                                variant="ghost"
                                className="h-8 w-8 p-0"
                                onClick={() =>
                                    setRowAction({
                                        type: "auth",
                                        row: row,
                                    })}
                            >
                                <Key className="h-4 w-4"/>
                            </Button>
                        </div>
                    </Access>
                );
            },
        }
    }

}