import React, {useState} from "react";
import {Sheet, SheetContent, SheetDes<PERSON>, Sheet<PERSON>eader, SheetTitle} from "@/components/ui/sheet";
import {Account} from "@/service/account";
import {Tabs, <PERSON><PERSON>Content, TabsList, TabsTrigger} from "@/components/ui/tabs";
import {AccountRoleAuth} from "./account-role-auth";
import {AccountGroupAuth} from "./account-group-auth";

type AuthTab = 'role' | 'group';

interface AccountAuthDialogProps {
    open: boolean;
    onOpenChange: (open: boolean) => void;
    account?: Account | undefined;
}

export function AccountAuthSheet({open, onOpenChange, account}: AccountAuthDialogProps) {
    const [activeTab, setActiveTab] = useState<AuthTab>("role");

    return (
        <Sheet open={open} onOpenChange={onOpenChange}>
            <SheetContent className="sm:max-w-dvw p-4 w-dvw">
                <SheetHeader>
                    <SheetDescription/>
                    <SheetTitle>账户授权 - {account?.name}</SheetTitle>
                </SheetHeader>
                <div className="mt-6">
                    <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as AuthTab)}>
                        <TabsList className="grid w-[400px] grid-cols-2">
                            <TabsTrigger value="role">角色授权</TabsTrigger>
                            <TabsTrigger value="group">用户组授权</TabsTrigger>
                        </TabsList>
                        <TabsContent value="role" className="mt-6">
                            <AccountRoleAuth
                                account={account}
                                onOpenChange={onOpenChange}
                            />
                        </TabsContent>
                        <TabsContent value="group" className="mt-6">
                            <AccountGroupAuth
                                account={account}
                                onOpenChange={onOpenChange}
                            />
                        </TabsContent>
                    </Tabs>
                </div>
            </SheetContent>
        </Sheet>
    );
} 