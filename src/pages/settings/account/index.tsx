import {AccountTable} from "@/pages/settings/account/components/account-table";
import {history, useRequest} from "@@/exports";
import {Account, fetchAccounts} from "@/service/account";
import {DataTableRowAction} from "@/components/data-table/types";
import React, {useEffect, useState} from "react";
import {AccountAuthSheet} from "@/pages/settings/account/components/account-auth-sheet";

export const AccountPage = () => {

    const [rowAction, setRowAction] = useState<DataTableRowAction<Account> | null>(null);
    const [authDialogOpen, setAuthDialogOpen] = React.useState(false);
    const {run: runFetchAccounts, data, loading} = useRequest(fetchAccounts, {
        manual: true
    });

    useEffect(() => {
        if (rowAction?.type == 'auth') {
            setAuthDialogOpen(true);
        }
    }, [rowAction]);

    return (<>
        <AccountTable
            data={data!!}
            onQuery={runFetchAccounts}
            setRowAction={setRowAction}>
        </AccountTable>
        <AccountAuthSheet
            open={authDialogOpen}
            onOpenChange={setAuthDialogOpen}
            account={rowAction?.row.original}
        />
    </>)
}

export default AccountPage;