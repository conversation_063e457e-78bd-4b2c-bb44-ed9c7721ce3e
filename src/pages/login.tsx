import * as React from "react"
import {history, useModel, useSearchParams} from 'umi';
import {Library} from "lucide-react"
import {ThemeProvider} from '@/components/theme/theme-provider';

import {Card, CardContent, Card<PERSON>ooter, CardHeader, CardTitle} from "@/components/ui/card"
import {Button} from "@/components/ui/button"
import {Input} from "@/components/ui/input"
import {Form, FormControl, FormField, FormItem, FormLabel, FormMessage} from "@/components/ui/form"
import {zodResolver} from "@hookform/resolvers/zod"
import {useForm} from "react-hook-form"
import {z} from "zod"
import {useEffect, useLayoutEffect} from "react";
// 定义登录表单验证规则
const formSchema = z.object({
    username: z.string().min(2, {
        message: "用户名至少需要2个字符",
    }),
    password: z.string().min(6, {
        message: "密码至少需要6个字符",
    }),
})

const LoginPage = () => {
    const [params] = useSearchParams();
    const userModel = useModel('user');
    const [isLoading, setIsLoading] = React.useState<boolean>(false);
    const [loginError, setLoginError] = React.useState<string>("");
    const workspaceModel = useModel('workspace');

    // 如果用户已登录，重定向到首页
    useEffect(() => {
        if (userModel.isLoggedIn) {
            const space = params.get("space");
            if (space) {
                workspaceModel.initializeWorkspaces()
                workspaceModel.switchWorkspace(space)
            } else {
                history.push("/")
            }

            return;
        }
    }, [userModel.isLoggedIn]);


    useEffect(() => {
        const code = params.get("code");
        if (code && !userModel.isLoggedIn) {
            userModel.login('*************', code, 'dingTalk')
        }
    })

    // 创建表单
    const form = useForm<z.infer<typeof formSchema>>({
        resolver: zodResolver(formSchema),
        defaultValues: {
            username: "",
            password: "",
        },
    })

    // 提交表单
    async function onSubmit(values: z.infer<typeof formSchema>) {
        setIsLoading(true);
        setLoginError("");
        try {
            await userModel.login(values.username, values.password, 'web');
            // 登录成功后会自动触发userModel.isLoggedIn的变化，进而触发上面的useEffect重定向
        } catch (error: any) {
            setLoginError(error.message || "登录失败，请检查用户名和密码");
        } finally {
            setIsLoading(false);
        }
    }

    return (
        <ThemeProvider defaultTheme="light" storageKey="ui-theme-mode">
            <div className="min-h-screen flex items-center justify-center bg-background">
                <Card className="w-full max-w-md mx-4">
                    <CardHeader className="space-y-1 flex flex-col items-center">
                        <div className="bg-primary w-12 h-12 rounded-full flex items-center justify-center mb-4">
                            <Library className="h-6 w-6 text-primary-foreground"/>
                        </div>
                        <CardTitle className="text-2xl text-center">登录系统</CardTitle>
                        <p className="text-sm text-muted-foreground text-center">输入您的账号和密码登录</p>
                    </CardHeader>
                    <CardContent className="pb-0">
                        <Form {...form}>
                            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                                <FormField
                                    control={form.control}
                                    name="username"
                                    render={({field}) => (
                                        <FormItem>
                                            <FormLabel>用户名</FormLabel>
                                            <FormControl>
                                                <Input placeholder="请输入用户名" {...field} />
                                            </FormControl>
                                            <FormMessage/>
                                        </FormItem>
                                    )}
                                />
                                <FormField
                                    control={form.control}
                                    name="password"
                                    render={({field}) => (
                                        <FormItem>
                                            <FormLabel>密码</FormLabel>
                                            <FormControl>
                                                <Input type="password" placeholder="请输入密码" {...field} />
                                            </FormControl>
                                            <FormMessage/>
                                        </FormItem>
                                    )}
                                />
                                {loginError && (
                                    <div className="text-destructive text-sm">{loginError}</div>
                                )}
                                <Button type="submit" className="w-full" disabled={isLoading}>
                                    {isLoading ? "登录中..." : "登录"}
                                </Button>
                            </form>
                        </Form>
                    </CardContent>
                    <CardFooter className="flex flex-col gap-2 mt-4">
                        <div className="text-sm text-muted-foreground text-center">
                            使用账户名和密码登录系统
                        </div>
                    </CardFooter>
                </Card>
            </div>
        </ThemeProvider>
    )
}

export default LoginPage;