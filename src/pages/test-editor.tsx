import { useState } from 'react';
import {  } from '@/components/editor/simple-editor';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';

export default function TestEditor() {
  const [content, setContent] = useState('<p>欢迎使用 TipTap 编辑器！</p><p>这是一个类似 Notion 的富文本编辑器。</p>');

  const handleContentChange = (newContent: string) => {
    setContent(newContent);
    console.log('Content changed:', newContent);
  };

  const handleSave = () => {
    console.log('Saving content:', content);
    alert('内容已保存到控制台');
  };

  return (
    <div className="container mx-auto p-6 max-w-4xl">
      <Card>
        <CardHeader>
          <CardTitle>TipTap 编辑器测试</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <TiotapEd
            content={content}
            onChange={handleContentChange}
            placeholder="开始输入您的内容..."
            className="min-h-[400px]"
          />
          
          <div className="flex gap-2">
            <Button onClick={handleSave}>保存内容</Button>
            <Button 
              variant="outline" 
              onClick={() => setContent('')}
            >
              清空内容
            </Button>
            <Button 
              variant="outline" 
              onClick={() => setContent('<h1>示例标题</h1><p>这是一个<strong>粗体</strong>和<em>斜体</em>的示例。</p><ul><li>列表项 1</li><li>列表项 2</li></ul>')}
            >
              加载示例内容
            </Button>
          </div>

          <div className="mt-6">
            <h3 className="text-lg font-semibold mb-2">当前内容（HTML）：</h3>
            <pre className="bg-gray-100 p-4 rounded text-sm overflow-auto max-h-40">
              {content}
            </pre>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
