import {Skeleton} from "@/components/ui/skeleton"

export default function Loading() {
    return (
        <div className="flex flex-col items-center justify-center min-h-screen space-y-4 p-8">
            <div className="w-full max-w-2xl space-y-4">
                {/* 头部加载骨架 */}
                <div className="space-y-2">
                    <Skeleton className="h-8 w-[250px]" />
                    <Skeleton className="h-4 w-[200px]" />
                </div>
                
                {/* 内容区域加载骨架 */}
                <div className="space-y-4">
                    <Skeleton className="h-24 w-full rounded-lg" />
                    <div className="grid grid-cols-2 gap-4">
                        <Skeleton className="h-32 rounded-lg" />
                        <Skeleton className="h-32 rounded-lg" />
                    </div>
                </div>
                
                {/* 底部加载骨架 */}
                <div className="space-y-2">
                    <Skeleton className="h-4 w-full" />
                    <Skeleton className="h-4 w-[80%]" />
                    <Skeleton className="h-4 w-[60%]" />
                </div>
            </div>
        </div>
    )
}