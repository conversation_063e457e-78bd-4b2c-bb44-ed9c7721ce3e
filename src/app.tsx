import {history, RequestConfig, RequestError, RequestOptions, RuntimeConfig} from 'umi';
import {AccountInfo, fetchAccountInfo} from "@/service/account";

import {toast} from "sonner"
import React from "react";
import {Toaster} from "@/components/ui/sonner";
import {Token} from "@/service/auth";

export interface ResponseStructure<T> {
    code: number;
    data: T;
    msg: string;
}

export async function getInitialState(): Promise<AccountInfo | undefined> {
    //全局初始化
    const tokenString = localStorage.getItem('auth_token');
    if (tokenString) {
        const token = JSON.parse(tokenString) as Token;
        if (token.expireTime > Date.now()) {
            const {data} = await fetchAccountInfo()
            return data
        }
    }
    return undefined
}

export const rootContainer: RuntimeConfig['rootContainer'] = (container, args) => {
    return React.createElement(
        React.Fragment,
        {},
        container,
        React.createElement(
            'div',
            {style: {position: 'fixed', bottom: 0, right: 0}},
            React.createElement('div', {}, React.createElement(Toaster)),
        ),
    );
}


export const request: RequestConfig<ResponseStructure<any>> = {
    // other axios options you want
    errorConfig: {
        errorThrower: (res: ResponseStructure<any>) => {
            const {code, data, msg} = res;
            if (code !== 200) {
                throw new Error(msg);
            }
        },
        errorHandler: (error: RequestError, opts: RequestOptions) => {
            if ('response' in error && error.response?.status === 401) {
                localStorage.removeItem('auth_token');
                history.push('/login');
            }

        },
    },
    requestInterceptors: [],
    responseInterceptors: [
        async (response) => {
            // 拦截响应数据，进行个性化处理
            const {status, data} = response;
            const {code, msg} = data as ResponseStructure<any>;
            if (code !== 200) {
                toast.error(msg);
                return Promise.reject(response);
            }
            return response;
        }
    ]
};