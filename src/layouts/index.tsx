import {history, Outlet, useLocation, useModel} from 'umi';
import {NuqsAdapter} from 'nuqs/adapters/react-router/v6'
import React, {useEffect, useState} from 'react';
import {ThemeProvider} from '@/components/theme/theme-provider';
import {AppSidebar} from "@/components/layout/app-sidebar"
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb"
import {Separator} from "@/components/ui/separator"
import {SidebarInset, SidebarProvider, SidebarTrigger,} from "@/components/ui/sidebar"
import {ModeToggle} from "@/components/theme/mode-toggle"
import {LogOut, User} from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {Button} from "@/components/ui/button"
import {Avatar, AvatarFallback} from "@/components/ui/avatar"
import {Toaster} from "@/components/ui/sonner";

// 页面路径与标题的映射
const pathTitles: Record<string, string> = {
  '/': '首页',
  '/docs': '文档中心',
};

export default function Layout() {
  const location = useLocation();
  // 使用useModel钩子获取工作空间和用户数据
  const workspaceModel = useModel('workspace');
  const userModel = useModel('user');
  const [isInitialized, setIsInitialized] = useState(false);


  // 获取当前页面标题
  const currentPageTitle = pathTitles[location.pathname] || '当前页面';

  // 初始化时检查用户登录状态
  useEffect(() =>  {
    // 已经初始化过，无需再次执行
    if (isInitialized) return;

    // 检查当前是否在登录页面
    if (location.pathname === '/login') {
      setIsInitialized(true);
      return;
    }
    // 如果还未登录
    if (!userModel.isLoggedIn){
      setIsInitialized(true);
    }else {
      if (workspaceModel){
        workspaceModel.initializeWorkspaces().then(()=>{
          setIsInitialized(true);
        })

      }
    }
  }, [userModel, location.pathname, isInitialized, workspaceModel]);

  // 登录状态变化时的处理
  useEffect(() => {
    // 等待初始化完成再处理登录状态变化
    if (!isInitialized) return;

    // 如果未登录且不在登录页，则重定向到登录页
    if (!userModel.isLoggedIn && location.pathname !== '/login') {

      history.push('/login');
    }
  }, [userModel.isLoggedIn, location.pathname, isInitialized]);

  // 如果未初始化或未登录，且当前不在登录页，则不渲染内容
  if ((!isInitialized || !userModel.isLoggedIn) && location.pathname !== '/login') {
    return null;
  }

  // 处理登出
  const handleLogout = async () => {
    await userModel.logout();
    history.push('/login');
  };

  return (
     <ThemeProvider defaultTheme="light" storageKey="ui-theme-mode">
      <SidebarProvider>
        <AppSidebar
          workspaces={workspaceModel.workspaces}
          activeWorkspace={workspaceModel.activeWorkspace}
          onWorkspaceChange={workspaceModel.switchWorkspace}
        />
        <SidebarInset>
          <header className="flex h-16 shrink-0 items-center gap-2 transition-[width,height] ease-linear group-has-[[data-collapsible=icon]]/sidebar-wrapper:h-12 border-b">
            <div className="flex justify-between w-full pr-4">
              <div className="flex items-center gap-2 px-4">
                <SidebarTrigger className="-ml-1" />
                <Separator orientation="vertical" className="mr-2 h-4" />
                <Breadcrumb>
                  <BreadcrumbList>
                    <BreadcrumbItem className="hidden md:block">
                      <BreadcrumbLink href="#">
                        {workspaceModel.activeWorkspace?.name}
                      </BreadcrumbLink>
                    </BreadcrumbItem>
                    <BreadcrumbSeparator className="hidden md:block" />
                    <BreadcrumbItem>
                      <BreadcrumbPage>{currentPageTitle}</BreadcrumbPage>
                    </BreadcrumbItem>
                  </BreadcrumbList>
                </Breadcrumb>
              </div>
              <div className="flex items-center gap-2">
                <ModeToggle />
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="icon">
                      <Avatar className="h-8 w-8">
                        <AvatarFallback>{userModel.user.name.charAt(0).toUpperCase()}</AvatarFallback>
                      </Avatar>
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuLabel>我的账户</DropdownMenuLabel>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem>
                      <User className="mr-2 h-4 w-4" />
                      <span>{userModel.user.name}</span>
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={handleLogout}>
                      <LogOut className="mr-2 h-4 w-4" />
                      <span>退出登录</span>
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </div>
          </header>
          <div className="flex flex-1 flex-col gap-4 p-4">
            <NuqsAdapter>
              <Outlet context={{ workspace: workspaceModel, user: userModel }} />
            </NuqsAdapter>
          </div>
        </SidebarInset>
      </SidebarProvider>
       <Toaster/>
    </ThemeProvider>
  );
}
