export interface ACL {
    resource: string;

    resourceId?: string;

    policies: Policy[];
}

export interface ACLSpecification {
    resource: string;

    resourceId?: string;

    policyType?: PolicyType;
}




export type PolicyType = 'USER' | 'ROLE' | 'ORGANIZATION' | 'GROUP';

export type StrategyType = 'ROOT' | 'DRILL_UP' | 'DRILL_DOWN';

export interface Policy {
    id?: string;
    policyType: PolicyType;
    value: string;
    strategyType: StrategyType;
}



export interface ACLAuthorization {
    id: string;
    acl: ACL
}

