export interface PageQuery<E> {
    page: number;

    pageSize: number;

    specification?: Specification<E>;

    sort?: Sort<E>[];
}

export interface Sort<E> {
    field: CommaKeyOf<E>;
    order: 'asc' | 'desc'
}


export type Aggs<E> = {
    [key in keyof E]?: Agg<E>;
};

export interface Page<E> {
    pageSize: number;

    page: number;

    total: number;

    data: Array<E>;
    aggs?: Aggs<E>
}


type IsPlainObject<T> =
    T extends object
        ? T extends any[] ? false
            : T extends Function ? false
                : T extends null ? false
                    : true
        : false;

// 递归深度辅助类型
type Prev = [never, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10];

// 支持递归嵌套 key，排除数组/函数/null/undefined，限制递归深度
// 支持单个字段、点分隔嵌套字段
export type NestedKeyOf<T, Depth extends number = 5> =
    [Depth] extends [never] ? never :
        {
            [K in keyof T & (string)]: IsPlainObject<NonNullable<T[K]>> extends true
            ? K | `${K}.${NestedKeyOf<NonNullable<T[K]>, Prev[Depth]>}`
            : K
        }[keyof T & (string)];

// 支持逗号分隔的多字段类型
// 使用字符串类型避免生成过于复杂的联合类型
export type CommaKeyOf<T> = NestedKeyOf<T> | string;


// ES
export interface AggBucket<T> {
    key: string;
    desc?: string;
    docCount: number;
}

export interface Agg<T> {
    name: string;
    buckets: AggBucket<T>[];
}

export interface ConditionMultiMatch<T> {
    query: string;
    fields: (CommaKeyOf<T>)[];
}

export interface ConditionRange {
    from: number | string;
    to: number | string;
}

/**
 * es 查询构造器
 */
export interface Condition<T> {
    term?: {
        [K in CommaKeyOf<T>]?: any;
    };
    terms?: {
        [K in CommaKeyOf<T>]?: any[];
    };
    multi_match?: ConditionMultiMatch<T>;
    range?: {
        [K in CommaKeyOf<T>]?: ConditionRange;
    };
}

export interface A {
    [key: string]: any;
}


export interface ESSpecification<T> {
    aggs: string[];
    conditions: {
        [K in CommaKeyOf<T>]?: Condition<T>;
    };
    highlight: (CommaKeyOf<T>)[];
    source: (CommaKeyOf<T>)[];
}

// SQL查询
export type SQLSpecification<T> = {
    [K in CommaKeyOf<T>]?: any;
}

//查询参数
export type Specification<T> = ESSpecification<T> | SQLSpecification<T>;