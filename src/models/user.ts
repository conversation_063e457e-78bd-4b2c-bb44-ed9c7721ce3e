import {useCallback, useState} from 'react';
import {useModel} from 'umi';
import {login as apiLogin, LoginScenario, logout as apiLogout, Permission, Token} from '@/service/auth';
import {AccountInfo, fetchAccountInfo, savePreferences} from "@/service/account";
import {Role} from "@/service/role";
import {Group} from "@/service/group";

export interface UserModelState {
    isLoggedIn: boolean;
    token: Token | null;
}

export interface UserModelType {
    isLoggedIn: boolean;
    token: Token | null;
    user: AccountInfo['user'];
    preferences: AccountInfo['preference'];
    login: (username: string, password: string, scenario: LoginScenario) => Promise<void>;
    logout: () => Promise<void>;
    setPreferredWorkspace: (workspaceName: string) => Promise<void>;
    hasPermission: (permission: string) => boolean;
}

export default function useUserModel(): UserModelType {
    const {initialState, setInitialState} = useModel('@@initialState')

    // 用户状态只保存登录相关信息
    const [userState, setUserState] = useState<UserModelState>(() => {
        // 在初始化状态时直接尝试从localStorage恢复
        const storedToken = localStorage.getItem('auth_token');
        if (storedToken) {
            try {
                const token = JSON.parse(storedToken) as Token;
                // 检查token是否过期
                if (token.expireTime > Date.now()) {
                    return {
                        isLoggedIn: true,
                        token,
                    };
                } else {
                    // 清除过期的token
                    localStorage.removeItem('auth_token');
                }
            } catch (e) {
                console.error('恢复会话失败:', e);
                localStorage.removeItem('auth_token');
            }
        }
        // 默认状态
        return {
            isLoggedIn: false,
            token: null,
        };
    });

    // 登录方法
    const login = useCallback(async (username: string, password: string, scenario: LoginScenario = 'web') => {
        const {data, msg, code} = await apiLogin({
            account: username,
            password,
            scenario,
        });

        if (code !== 200) {
            throw new Error(msg);
        }

        // 获取用户信息并更新 initialState
        const {data: accountInfo} = await fetchAccountInfo();
        if (accountInfo) {
            setInitialState(accountInfo);
        }

        // 处理 token
        const token = {
            ...data,
            expireTime: Date.now() + data.expireTime * 1000,
        };

        // 存储token到本地存储
        localStorage.setItem('auth_token', JSON.stringify(token));

        // 更新登录状态
        setUserState({
            isLoggedIn: true,
            token,
        });
    }, [setInitialState]);

    // 注销方法
    const logout = useCallback(async () => {
        try {
            await apiLogout();
        } catch (error) {
            console.error('登出失败:', error);
        } finally {
            setUserState({
                isLoggedIn: false,
                token: null,
            });
            setInitialState(undefined);
            localStorage.removeItem('auth_token');
        }
    }, [setInitialState]);

    // 设置用户偏好的工作区
    const setPreferredWorkspace = useCallback(async (workspaceName: string) => {
        if (!initialState) return;
        savePreferences({
            account: initialState.account,
            preference: {
                ...initialState.preference,
                workspace: workspaceName,
            },
        })
        if (initialState) {
            setInitialState({
                ...initialState,
                preference: {
                    ...initialState.preference,
                    workspace: workspaceName,
                },
            });
        }
    }, [initialState, setInitialState]);

    // 检查用户是否有特定权限
    const hasPermission = useCallback((permission: string) => {
        if (!initialState) return false;

        const rolePermissions = initialState.roles
            .flatMap((role: Role) => role.permissions)
            .map((p: Permission) => p.code);

        const groupPermissions = initialState.groups
            .flatMap((group: Group) => group.permissions)
            .map((p: Permission) => p.code);

        return [...rolePermissions, ...groupPermissions].includes(permission);
    }, [initialState]);

    const defaultUser = {
        name: '',
        email: '',
        phone: '',
    };

    const defaultPreferences = {
        theme: 'LIGHT' as const,
        workspace: '',
    };

    return {
        isLoggedIn: userState.isLoggedIn && localStorage.getItem('auth_token') !== null,
        token: userState.token,
        user: initialState?.user || defaultUser,
        preferences: initialState?.preference || defaultPreferences,
        login,
        logout,
        setPreferredWorkspace,
        hasPermission,
    };
} 