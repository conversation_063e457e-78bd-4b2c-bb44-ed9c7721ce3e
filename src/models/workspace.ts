import {useCallback, useEffect, useMemo, useState} from "react"
import {icons, Settings} from "lucide-react";
import {WorkspaceType} from "@/components/layout/team-switcher"
import {NavMainItemType} from "@/components/layout/nav-main"
import {Permission} from "@/service/auth"
import {history, useModel} from 'umi';
import {fetchWorkspace} from "@/service/account";

// 定义初始工作空间数据

/**
 * 将扁平结构的权限列表转换为树形结构
 * @param permissions 扁平结构的权限列表
 * @returns 树形结构的权限列表
 */
const convertFlatToTree = (permissions: Permission[]): Permission[] => {
    if (!permissions || permissions.length === 0) return [];

    // 创建一个映射表，用于快速查找权限
    const permissionMap = new Map<string, Permission>();
    permissions.forEach(permission => {
        permissionMap.set(permission.id, {...permission, children: []});
    });

    // 构建树形结构
    const rootPermissions: Permission[] = [];

    permissions.forEach(permission => {
        const permissionWithChildren = permissionMap.get(permission.id);
        if (!permissionWithChildren) return;

        if (permission.parent && permissionMap.has(permission.parent.id)) {
            // 如果有父权限，将当前权限添加到父权限的子权限列表中
            const parent = permissionMap.get(permission.parent.id);
            if (parent && parent.children) {
                parent.children.push(permissionWithChildren);
            }
        } else {
            // 如果没有父权限，则为根权限
            rootPermissions.push(permissionWithChildren);
        }
    });

    return rootPermissions;
};

// 将权限转换为工作空间
const convertPermissionToWorkspace = (permission: Permission): WorkspaceType => {
    // 根据权限名称选择对应的图标
    const getIcon = (metaIcon?: string) => {
        // 如果有 meta.icon，使用它
        const icon = Object.entries(icons)
            .findLast(([name]) => name.toLowerCase() === (metaIcon?.toLowerCase() || 'setting'))
        return icon ? icon[1] : Settings;
    };

    // 将权限的子项转换为导航项
    const convertToNavItems = (permissions: Permission[], parentPath: string = ''): NavMainItemType[] => {
        return permissions.filter(permission => permission.type !== 'ACTION' && permission.status == 'ACTIVE').map(p => {
            const currentPath = `${parentPath}${p.href}`;
            return {
                title: p.name,
                url: currentPath,
                icon: getIcon(p.meta?.icon),
                items: p.children ? convertToNavItems(p.children, currentPath) : undefined,
            };
        });
    };

    return {
        name: permission.name,
        code: permission.code,
        logo: getIcon(permission.meta?.icon),
        type: permission.description || '默认类型',
        navItems: convertToNavItems(permission.children || [], `${permission.code.toLowerCase()}`),
    };
};

export type WorkspaceModelType = {
    /** 所有可用的工作空间列表 */
    workspaces: WorkspaceType[];
    /** 设置工作空间列表 */
    setWorkspaces: (workspaces: WorkspaceType[]) => void;
    /** 当前激活的工作空间 */
    activeWorkspace: WorkspaceType | undefined;
    /** 切换工作空间 */
    switchWorkspace: (workspace: WorkspaceType | string) => void;
    /** 从权限数据更新工作空间列表 */
    updateWorkspacesFromPermissions: (permissions: Permission[]) => WorkspaceType[];
    /** 初始化工作空间数据 */
    initializeWorkspaces: () => Promise<void>;
}

export default () => {
    const {initialState, loading} = useModel('@@initialState')
    // 工作空间列表
    const [workspaces, setWorkspaces] = useState<WorkspaceType[]>([]);
    const [activeWorkspaceCode, setActiveWorkspaceCode] = useState<string | undefined>()
    // 当前选中的工作空间，初始化为一个默认值
    const [activeWorkspace, setActiveWorkspace] = useState<WorkspaceType | undefined>();

    // 从权限数据更新工作空间
    const updateWorkspacesFromPermissions = useCallback((permissions: Permission[]) => {
        const treePermissions = convertFlatToTree(permissions);
        const treeWorkspacePermissions = treePermissions.filter(p => p.type === 'WORKSPACE');

        // 转换为工作空间
        const newWorkspaces = treeWorkspacePermissions.map(convertPermissionToWorkspace);
        // 更新工作空间列表
        setWorkspaces(newWorkspaces);
        return newWorkspaces;
    }, []);

    useEffect(() => {
        if (!activeWorkspaceCode && initialState) {
            const workspace = history.location.pathname.split('/')[1];
            setActiveWorkspaceCode(workspace || initialState?.preference?.workspace)
        }
    }, [history, initialState]);

    //切换工作空间后自动定位到第一个url
    useEffect(() => {
        if (activeWorkspaceCode && workspaces.length > 0) {
            const activeSpace = workspaces.find(w => w.code.toLowerCase() === activeWorkspaceCode.toLowerCase());
            setActiveWorkspace(activeSpace)
            if (activeSpace && activeSpace?.code.toLowerCase() !== activeWorkspaceCode) {
                history.push(`/${activeSpace.navItems[0].url}`)
            }
        }
    }, [activeWorkspaceCode, workspaces]);

    // 切换工作空间
    const switchWorkspace = useCallback((workspace: WorkspaceType | string) => {
        if (typeof workspace === 'string') {
            setActiveWorkspaceCode(workspace)
        } else {
            setActiveWorkspace(workspace);
        }
    }, [workspaces]);

    useEffect(() => {
        if (workspaces.length > 0 && activeWorkspace) {
            const url = workspaces.find(w => w.code === activeWorkspace.code)?.navItems[0]?.url;
            const workspace = history.location.pathname.split('/')[1];
            if (url && workspace.toLowerCase().toLowerCase() !== activeWorkspaceCode?.toLowerCase()) {
                history.push(`/${url}`);
            }
        }
    }, [workspaces, activeWorkspace]);

    // 初始化加载工作空间
    const initializeWorkspaces = useCallback(async () => {
        try {
            const res = await fetchWorkspace();
            if (res.data) {
                updateWorkspacesFromPermissions(res.data)
            }
        } catch (error) {
            console.error('加载工作空间失败:', error);
        }
    }, []);


    return {
        workspaces,
        setWorkspaces,
        activeWorkspace,
        switchWorkspace,
        updateWorkspacesFromPermissions,
        initializeWorkspaces,
    };
}
